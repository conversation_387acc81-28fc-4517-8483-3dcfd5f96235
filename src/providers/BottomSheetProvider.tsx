import React, { createContext, useContext, useRef, useCallback, useMemo, useState, ReactNode } from 'react'
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetModalProvider, BottomSheetView } from '@gorhom/bottom-sheet'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { FullWindowOverlay } from 'react-native-screens'
import { isNative } from '@src/lib/device'

type BottomSheetContextType = {
  show: (node: ReactNode, snapPoints?: (string | number)[]) => void
  hide: () => void
}

const BottomSheetContext = createContext<BottomSheetContextType | undefined>(undefined)

export const useBottomSheet = () => {
  const context = useContext(BottomSheetContext)
  if (!context) throw new Error('useBottomSheet must be used within a BottomSheetProvider')
  return context
}

/**
 * Bottom Sheet Provider - Used to show bottom sheets only on Native Mobile or Tablet devices.
 *
 * @param children
 * @constructor
 */
export const BottomSheetProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null)
  const [content, setContent] = useState<React.ReactNode>(null)
  const [snapPoints, setSnapPoints] = useState<(string | number)[]>(['25%', '50%', '80%'])
  const { styles } = useStyles(styleSheet)

  const show = useCallback(
    (node: ReactNode, snapPointsParam?: (string | number)[]) => {
      setContent(node)
      if (snapPointsParam) {
        setSnapPoints(snapPointsParam)
      }
      if (isNative) {
        bottomSheetModalRef.current?.present()
      }
    },
    [isNative]
  )

  const hide = useCallback(() => {
    if (isNative) {
      bottomSheetModalRef.current?.close()
    }
  }, [isNative])

  const value = useMemo(() => ({ show, hide }), [show, hide])

  return (
    <BottomSheetContext.Provider value={value}>
      <GestureHandlerRootView style={styles.container}>
        {children}
        {isNative && (
          <BottomSheetModalProvider>
            <BottomSheetModal
              ref={bottomSheetModalRef}
              snapPoints={snapPoints}
              enableDismissOnClose
              enablePanDownToClose
              backdropComponent={props => (
                <BottomSheetBackdrop appearsOnIndex={0} disappearsOnIndex={-1} pressBehavior={'close'} {...props} />
              )}
              style={[
                {
                  backgroundColor: '#000',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 12,
                  },
                  shadowOpacity: 0.58,
                  shadowRadius: 16.0,

                  elevation: 24,
                },
              ]}
              containerComponent={props => <FullWindowOverlay>{props.children}</FullWindowOverlay>}>
              <BottomSheetView style={styles.contentContainer}>{content}</BottomSheetView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        )}
      </GestureHandlerRootView>
    </BottomSheetContext.Provider>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    padding: 16,
    alignItems: 'center',
    paddingBottom: 50,
  },
}))
