import { Pressable, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { DrawerContentComponentProps } from '@react-navigation/drawer'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { sharedState } from '@src/shared-state/shared-state'
import { Drawer } from 'expo-router/drawer'
import { Routes } from '@src/navigation/constants'
import { usePathname, useRouter } from 'expo-router'
import { getRouteConfig, getRoutePath } from '@src/navigation/utils'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaHeaderImage } from '@src/components/_atoms/SeaHeaderImage/SeaHeaderImage'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { ScrollView } from 'react-native-gesture-handler'
import { SeaHeaderBar } from '@src/components/_atoms/SeaHeaderBar/SeaHeaderBar'
import { colors } from '@src/theme/colors'
import { SeaAvatar } from '@src/components/_atoms/SeaAvatar/SeaAvatar'
import { attemptLogout } from '@src/App'

export const Sidebar = () => {
  // Hooks
  const { isDesktopWidth } = useDeviceWidth()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const layout = sharedState.layoutMode.use()
  const vessel = sharedState.vessel.use()
  const isStandardLayout = useMemo(() => layout === 'standard', [layout])
  const [parentActiveSection, setParentActiveSection] = useState<string>('')

  useEffect(() => {
    setIsCollapsed(!isDesktopWidth)
  }, [isDesktopWidth])

  // Get current route navigation path name
  // This only works because the nav path names matches the file name and the route key
  const currentPathName = usePathname()

  // Determine if a route is active
  const isRouteActive = useCallback(
    (routeName: string, parentRouteName = '') => {
      const isActive = currentPathName.includes(routeName)
      if (isActive && parentRouteName) {
        setParentActiveSection(parentRouteName)
      }
      return isActive
    },
    [currentPathName]
  )

  return (
    <Drawer
      screenOptions={{
        headerShown: true,
        header: () =>
          !isDesktopWidth ? <SeaHeaderBar title={vessel?.name} iconName={'directions_boat_filled'} /> : <></>,
        drawerType: isDesktopWidth ? 'permanent' : 'back',
        drawerStyle: {
          marginTop: isDesktopWidth ? 12 : 0,
          borderTopRightRadius: isDesktopWidth ? 15 : 0,
          backgroundColor: colors.background.primary,
          borderColor: colors.borderColor,
          borderWidth: 2,
          borderRightWidth: 2,
          borderRightColor: colors.borderColor,
          // The width is set to 0 to hide the sidebar on all the other layouts.
          ...(!isStandardLayout ? { width: 0 } : isDesktopWidth && isCollapsed ? { width: 60 } : { width: 280 }),
        },
      }}
      drawerContent={props =>
        isStandardLayout && (
          <CustomDrawer
            {...props}
            isCollapsed={isCollapsed && isDesktopWidth}
            isRouteActive={isRouteActive}
            parentActiveSection={parentActiveSection}
            setIsCollapsed={setIsCollapsed}
          />
        )
      }
    />
  )
}

const SidebarToggleIcon = ({
  isCollapsed,
  setIsCollapsed,
}: {
  isCollapsed: boolean
  setIsCollapsed: (isCollapsed: boolean) => void
}) => {
  const { styles } = useStyles(styleSheet)

  return (
    <Pressable
      onPress={() => setIsCollapsed(!isCollapsed)}
      style={StyleSheet.flatten(
        isCollapsed ? [styles.sidebarSection, styles.sidebarSectionCollapsed, styles.sidebarToggleCollapsed] : []
      )}>
      <SeaIcon icon={isCollapsed ? 'keyboard_tab' : 'keyboard_tab_rtl'} color={colors.text.primary} size={25} />
    </Pressable>
  )
}

interface CustomDrawerProps extends DrawerContentComponentProps {
  isCollapsed: boolean
  isRouteActive: (routeName: string, parentRouteName: string) => boolean
  parentActiveSection: string
  setIsCollapsed: (isCollapsed: boolean) => void
}

const CustomDrawer = (props: CustomDrawerProps) => {
  const vessel = sharedState.vessel.use()
  const vesselId = sharedState.vesselId.use()
  const { isDesktopWidth } = useDeviceWidth()
  const { styles, theme } = useStyles(styleSheet)

  const renderSectionHeader = (title: string) => {
    return (
      <SeaTypography variant="title" textStyle={styles.sidebarTitle} fontWeight="bold" color={theme.colors.primary}>
        {title}
      </SeaTypography>
    )
  }

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'space-between',
        padding: 0,
        height: '100%',
        paddingBottom: isDesktopWidth ? 0 : 10,
      }}>
      <ScrollView>
        {isDesktopWidth ? (
          <SeaStack
            direction="row"
            gap={0}
            justify="between"
            style={{
              padding: 18,
            }}>
            <SeaHeaderImage showIcon={!props.isCollapsed} />
            {!props.isCollapsed && (
              <SidebarToggleIcon isCollapsed={props.isCollapsed} setIsCollapsed={props.setIsCollapsed} />
            )}
          </SeaStack>
        ) : (
          <View style={{ height: 40 }}>
            <SeaHeaderImage mobile showIcon={!props.isCollapsed} />
          </View>
        )}

        {props.isCollapsed && (
          <SidebarToggleIcon isCollapsed={props.isCollapsed} setIsCollapsed={props.setIsCollapsed} />
        )}

        <SeaStack
          direction="column"
          style={StyleSheet.flatten(
            props.isCollapsed
              ? {
                  paddingVertical: 8,
                  borderBlockEndWidth: 1,
                  borderBlockColor: colors.borderColor,
                }
              : {}
          )}>
          <CollapsibleDrawerGroup vesselId={vesselId} routeName={Routes.FLEET_DASHBOARD} {...props} />
        </SeaStack>

        {vesselId && (
          <SeaStack
            direction="column"
            align="start"
            gap={props.isCollapsed ? 10 : 0}
            style={StyleSheet.flatten(
              props.isCollapsed
                ? {
                    paddingVertical: 8,
                    borderBlockEndWidth: 1,
                    borderBlockColor: colors.borderColor,
                  }
                : {
                    paddingVertical: 0,
                  }
            )}>
            {/** Vessel Section */}
            {!props.isCollapsed && renderSectionHeader(vessel?.name)}

            <CollapsibleDrawerGroup routeName={Routes.VESSEL_DASHBOARD} vesselId={vesselId} {...props} />
            <CollapsibleDrawerGroup vesselId={vesselId} routeName={Routes.LOGBOOK} {...props} />

            <CollapsibleDrawerGroup vesselId={vesselId} routeName={Routes.VOYAGE_HISTORY} {...props} />

            <CollapsibleDrawerGroup vesselId={vesselId} routeName={Routes.SAFETY_DASHBOARD} {...props} />

            <CollapsibleDrawerGroup routeName={Routes.MAINTENANCE_DASHBOARD} {...props} vesselId={vesselId} />

            <CollapsibleDrawerGroup
              vesselId={vesselId}
              routeName={Routes.VESSEL_DOCUMENT_REGISTER_DASHBOARD}
              {...props}
            />
          </SeaStack>
        )}

        {/** Company Section */}
        {!props.isCollapsed && renderSectionHeader('Company')}

        <SeaStack
          direction="column"
          style={StyleSheet.flatten(
            props.isCollapsed
              ? {
                  paddingVertical: 8,
                }
              : {}
          )}>
          <CollapsibleDrawerGroup vesselId={vesselId} routeName={Routes.HEALTH_SAFETY_DASHBOARD} {...props} />

          <CollapsibleDrawerGroup
            vesselId={vesselId}
            routeName={Routes.COMPANY_DOCUMENT_REGISTER_DASHBOARD}
            {...props}
          />

          <CollapsibleDrawerGroup vesselId={vesselId} routeName={Routes.CREW_DASHBOARD} {...props} />
        </SeaStack>
      </ScrollView>
      <View
        style={{
          paddingVertical: 10,
          borderTopWidth: 1,
          borderColor: colors.borderColor,
        }}>
        {/*<CollapsibleDrawerGroup routeName={Routes.ACTION_LOG} {...props} />*/}

        {/*<CollapsibleDrawerGroup routeName={Routes.SUPPORT} {...props} />*/}

        <CollapsibleDrawerGroup routeName={Routes.LOGOUT} onPress={() => attemptLogout()} {...props} />

        <CollapsibleDrawerGroup routeName={Routes.PROFILE} isProfileLink {...props} />
      </View>
    </View>
  )
}

const CollapsibleDrawerGroup = ({
  routeName,
  isCollapsed,
  isRouteActive,
  parentActiveSection,
  vesselId,
  isProfileLink,
  onPress,
}: {
  routeName: string
  isCollapsed: boolean
  isRouteActive: (routeName: string, parentRouteName: string) => boolean
  parentActiveSection: string
  vesselId?: string | undefined
  isProfileLink?: boolean
  onPress?: () => void
}) => {
  const user = sharedState.user.use()
  const route = getRouteConfig(routeName as Routes)

  // Hooks
  const { styles, theme } = useStyles(styleSheet)
  const [expanded, setExpanded] = useState(false)
  const router = useRouter()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  const handleNavigation = (routePath: string, params?: Record<string, any>) => {
    // To do check child names or set the parent section whenever the child is called
    const navState = {
      pathname: routePath,
      params: {
        ...(vesselId ? { vesselId: vesselId } : {}),
        ...params,
      },
    }

    if (parentActiveSection === route.name) {
      router.navigate(navState)
    } else {
      /**
       * This is a quick fix to close the side drawer when navigating to the section
       * .replace behaves in a way that it loads the next screen, but doesn't close the side drawer
       * hence a following .navigate is needed to close the side drawer, but it doesn't affect performance
       */
      router.replace(navState)
      router.navigate(navState)
    }
  }

  const isActive = isRouteActive(route.name, route.name)
  const hasChildren = 'children' in route

  return (
    <View style={[{ width: '100%', paddingHorizontal: isCollapsed ? 6 : 15 }]}>
      <SeaStack
        direction="row"
        justify="between"
        align="center"
        style={StyleSheet.flatten([
          styles.sidebarSection,
          isCollapsed ? styles.sidebarSectionCollapsed : null,
          isActive && !isCollapsed
            ? styles.sidebarActiveLink
            : isActive && isCollapsed
              ? styles.collapsedSidebarActive
              : null,
          isProfileLink && !isCollapsed ? styles.profileLink : null,
          hoveredItem === route.name ? styles.sidebarHoverLink : null,
        ])}>
        <Pressable
          onPress={() => (onPress ? onPress?.() : handleNavigation(getRoutePath(route.name as Routes), undefined))}
          onHoverIn={() => setHoveredItem(route.name)}
          onHoverOut={() => setHoveredItem(null)}
          style={{ flex: 1 }}>
          <SeaStack
            direction="row"
            gap={isProfileLink ? 15 : 5}
            align="center"
            justify={isCollapsed ? 'center' : 'start'}>
            {isProfileLink ? (
              // TODO: Add user avatar component
              <SeaAvatar backgroundColor={theme.colors.primary} />
            ) : (
              <View
                style={{
                  width: 24,
                  alignItems: 'center',
                  justifyContent: 'center',
                  display: 'flex',
                }}>
                {route.icon && (
                  <SeaIcon
                    icon={route.icon.name}
                    size={route.icon.size}
                    fill={true}
                    color={isActive || hoveredItem === route.name ? colors.primary : colors.text.primary}
                  />
                )}
              </View>
            )}
            {!isCollapsed &&
              (isProfileLink ? (
                <SeaStack direction="column" gap={0} align="start">
                  <SeaTypography variant="body" fontWeight="semiBold" color={colors.primary}>
                    {user?.firstName} {user?.lastName}
                  </SeaTypography>
                  <SeaTypography variant="body">{user?.position}</SeaTypography>
                </SeaStack>
              ) : (
                <Text style={[styles.sidebarText, isActive && styles.sidebarActiveText]} numberOfLines={2}>
                  {route.title}
                </Text>
              ))}
          </SeaStack>
        </Pressable>

        {hasChildren && !isCollapsed && (
          <Pressable onPress={() => setExpanded(prev => !prev)}>
            <SeaIcon
              icon="arrow_drop_down"
              color={colors.text.primary}
              style={{
                transform: [{ rotate: expanded ? '180deg' : '0deg' }],
              }}
            />
          </Pressable>
        )}
      </SeaStack>

      {!isCollapsed && expanded && hasChildren && (
        <SeaStack style={styles.subMenu} align="start">
          {Object.keys(route.children ?? {}).map(key => {
            const child = {
              ...getRouteConfig(key as Routes),
              ...(route.children?.[key as keyof typeof route.children] ?? {}),
            }

            return (
              <Pressable
                key={child.name}
                onPress={() => handleNavigation(child.path, undefined)}
                onHoverIn={() => setHoveredItem(child.name)}
                onHoverOut={() => setHoveredItem(null)}
                style={{
                  width: '100%',
                }}>
                <SeaStack
                  style={StyleSheet.flatten([
                    styles.sidebarChildSection,
                    isRouteActive(child.name, route.name) && styles.childTextActive,
                    hoveredItem === child.name && styles.sidebarHoverLink,
                  ])}>
                  <Text style={styles.sidebarChildText}>{child.title}</Text>
                </SeaStack>
              </Pressable>
            )
          })}
        </SeaStack>
      )}
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  sidebarTitle: {
    fontSize: 14,
    paddingLeft: 15,
    marginVertical: 20,
    lineHeight: 0,
    textTransform: 'uppercase',
  },
  sidebarSection: {
    borderRadius: 8,
    paddingTop: 10,
    paddingBottom: 10,
    paddingHorizontal: 10,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  profileLink: {
    backgroundColor: theme.colors.white,
    borderWidth: 2,
    borderColor: 'rgba(68, 84, 111, 0.1)',
  },
  sidebarSectionCollapsed: {
    paddingVertical: 5,
    paddingHorizontal: 5,
    justifyContent: 'center',
  },
  sidebarToggleCollapsed: {
    paddingVertical: 5,
    backgroundColor: theme.colors.borderColor,
    borderBlockColor: theme.colors.borderColor,
    borderBlockWidth: 1,
    borderRadius: 0,
  },
  subMenu: {
    // Temp padding, will need to update
    paddingHorizontal: 11,
    width: '100%',
    flexDirection: 'column',
  },
  sidebarText: {
    fontSize: 14,
    color: theme.colors.text.primary,
    flexShrink: 1,
  },
  sidebarChildSection: {
    width: '100%',
    paddingVertical: 8,
    paddingLeft: 30,
    borderRadius: 8,
  },
  sidebarChildText: {
    fontSize: 14,
    color: theme.colors.text.primary,
  },

  sidebarItemPressed: {
    backgroundColor: theme.colors.primary + '10', // Even lighter when pressed
    transform: [{ scale: 0.98 }],
  },
  sidebarChildItem: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    paddingLeft: 40, // Indent child items
    borderRadius: 8,
    marginVertical: 2,
  },
  sidebarHoverLink: {
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  sidebarActiveLink: {
    backgroundColor: colors.sidebarActiveLinks,
    color: theme.colors.primary,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  collapsedSidebarActive: {
    borderRadius: 8,
    backgroundColor: colors.sidebarActiveLinks,
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  sidebarActiveText: {
    color: theme.colors.primary,
    fontWeight: 500,
  },
  childTextActive: {
    color: theme.colors.primary,
    backgroundColor: colors.sidebarActiveLinks,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  childTextHover: {
    color: theme.colors.primary,
    backgroundColor: theme.colors.white,
  },
}))
