import { SeaBreadcrumbs } from '@src/components/_atoms/SeaBreadcrumbs/SeaBreadcrumbs'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { theme } from '@src/theme'
import React, { ReactElement } from 'react'
import { ScrollView, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'

export enum SubNavType {
  Link = 'link',
  Button = 'button',
}

export type SubNav = {
  title: string
  titleAdditionalElements?: ReactElement
  onPress: () => void
  isActive?: boolean
  iconOptions?: SeaIconProps
}

export type SecondaryActionButton =
  | [] // 0 buttons
  | [React.ReactElement<typeof SeaButton>] // 1 button
  | [React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>] // 2 buttons
  | [React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>]

type SeaPageCardProps = {
  primaryActionButton?: React.ReactElement<typeof SeaButton>
  secondaryActionButton?: SecondaryActionButton // 3 buttons
  subNav?: SubNav[]
  subNavType?: SubNavType
  style?: ViewStyle
  contentStyle?: ViewStyle
  wrapperStyle?: ViewStyle
  hideBreadcrumbs?: boolean
  hidePath?: boolean
  hideHeader?: boolean
  onBackPress?: () => void
  children?: React.ReactNode
  titleComponent?: React.ReactElement<typeof SeaPageCardTitle>
}

export function SeaPageCard({
  titleComponent,
  primaryActionButton,
  secondaryActionButton,
  subNav,
  subNavType = SubNavType.Link,
  children,
  style,
  contentStyle,
  wrapperStyle,
  hideBreadcrumbs = false,
  hidePath = false,
  onBackPress,
}: SeaPageCardProps) {
  const { isMobileWidth, isTabletWidth, isDesktopWidth } = useDeviceWidth()

  return (
    <View style={[styles.container, isMobileWidth && styles.mobileContainer, style]}>
      <View style={[styles.defaultVerticalPadding, isMobileWidth && styles.mobileVerticalPadding]}>
        {/** Title and Actions */}
        <View style={[styles.defaultHorizontalPadding, isMobileWidth && styles.mobileHorizontalPadding, wrapperStyle]}>
          <>
            {(isMobileWidth || isTabletWidth || isDesktopWidth) && (
              <SeaStack direction="column" gap={10} align={'start'}>
                <SeaStack direction="row" gap={10} align={'center'} justify={'between'} width={'100%'}>
                  <View>
                    {!hideBreadcrumbs ? (
                      <SeaBreadcrumbs hidePath={hidePath} onBackPress={onBackPress} />
                    ) : (
                      (titleComponent ?? <></>)
                    )}
                  </View>
                  <SeaStack direction="row" gap={10} style={{ flexShrink: 1 }}>
                    {secondaryActionButton && secondaryActionButton}
                    {primaryActionButton && primaryActionButton}
                  </SeaStack>
                </SeaStack>
                {!hideBreadcrumbs && titleComponent ? titleComponent : <></>}
              </SeaStack>
            )}
          </>
        </View>

        {/** Children Content */}
        <View style={[contentStyle]}>{children}</View>
      </View>

      {subNav && (
        <SeaStack
          direction="row"
          style={StyleSheet.flatten([
            styles.subNav,
            subNavType === SubNavType.Button && styles.subNavWithButtons,
            isMobileWidth && styles.mobileSubNav,
          ])}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={StyleSheet.flatten([
              styles.subNavScrollable,
              subNavType === SubNavType.Button && styles.subNavScrollableWithButtons,
            ])}>
            {subNav.map((item, index) =>
              subNavType === SubNavType.Link ? (
                <SeaPageCardSubNav
                  key={index}
                  onPress={item.onPress}
                  title={item.title}
                  isActive={item.isActive}
                  iconOptions={item.iconOptions}
                />
              ) : (
                <SeaButton
                  key={index}
                  label={item.title}
                  variant={SeaButtonVariant.Tertiary}
                  iconOptions={item.iconOptions}
                  onPress={item.onPress}
                />
              )
            )}
          </ScrollView>
        </SeaStack>
      )}
    </View>
  )
}

const SeaPageCardSubNav = ({ onPress, title, isActive, iconOptions }: SubNav) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <SeaTypography
        variant="body"
        textStyle={StyleSheet.flatten([styles.subNavButton])}
        containerStyle={StyleSheet.flatten([isActive && styles.subNavButtonActive])}>
        {title}
      </SeaTypography>
    </TouchableOpacity>
  )
}

interface SeaPageCardContentSectionProps {
  children: React.ReactNode
  contentStyle?: ViewStyle
}

/**
 * This component is used to wrap the content of a page card. It is used to add padding and margin to the content.
 * Handles Desktop, Tablet and Mobile styles
 *
 * @param children
 * @param contentStyle
 */
export const SeaPageCardContentSection = ({ children, contentStyle }: SeaPageCardContentSectionProps) => {
  const { isMobileWidth } = useDeviceWidth()

  return (
    <View
      style={[
        styles.defaultHorizontalPadding,
        styles.defaultVerticalPadding,
        isMobileWidth && styles.mobileHorizontalPadding,
        isMobileWidth && styles.mobileVerticalPadding,
        contentStyle,
      ]}>
      {children}
    </View>
  )
}

interface SeaPageCardTitleProps {
  title?: string
  additionalElements?: ReactElement
  files?: string[]
  customTitleRow?: ReactElement
}

/**
 * This component is used to render the title of a page card.
 * It has a default placeholder for the title elements
 *
 * @constructor
 */
export const SeaPageCardTitle = ({ title, additionalElements, files, customTitleRow }: SeaPageCardTitleProps) => {
  const { isMobileWidth } = useDeviceWidth()

  const dimension = isMobileWidth ? 60 : 80

  return (
    <>
      {customTitleRow ??
        (title || files ? (
          <SeaStack direction={'row'} justify={'start'} gap={isMobileWidth ? 15 : 20}>
            {files && (
              <View style={{ borderRadius: 12, width: dimension }}>
                <SeaFileImage
                  files={files}
                  style={{ height: dimension, width: dimension }}
                  mustGetDefaultImage={false}
                />
              </View>
            )}
            {title && (
              <View
                style={{
                  flexWrap: 'wrap',
                  flexShrink: 1,
                }}>
                <SeaTypography variant="title" textStyle={styles.title}>
                  {title}
                </SeaTypography>
                {additionalElements}
              </View>
            )}
          </SeaStack>
        ) : (
          <></>
        ))}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: theme.colors.white,
    borderRadius: 26,
    marginBottom: 20,
  },
  mobileContainer: {
    borderBottomRightRadius: 0,
    borderBottomLeftRadius: 0,
  },
  defaultVerticalPadding: {
    paddingVertical: 20,
  },
  defaultHorizontalPadding: {
    paddingHorizontal: 20,
  },
  mobileVerticalPadding: {
    paddingVertical: 10,
  },
  mobileHorizontalPadding: {
    paddingHorizontal: 10,
  },
  header: {
    marginBottom: 14,
  },
  title: {
    marginBottom: 0,
  },
  subNav: {
    paddingHorizontal: 20,
    paddingVertical: 0,
    borderTopColor: theme.colors.borderColor,
    borderTopWidth: 2,
  },
  subNavWithButtons: {
    paddingVertical: 20,
  },
  subNavScrollable: {
    columnGap: 20,
  },
  subNavScrollableWithButtons: {
    columnGap: 10,
  },
  mobileSubNav: {
    paddingHorizontal: 12,
  },
  subNavButton: {
    paddingVertical: 10,
    paddingTop: 13,
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  subNavButtonActive: {
    fontWeight: '500',
    color: theme.colors.black,
    borderBottomWidth: 3,
    borderBottomColor: theme.colors.primary,
  },
})
