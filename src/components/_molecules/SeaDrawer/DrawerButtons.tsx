import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSaveButton } from '@src/components/_molecules/IconButtons/SeaSaveButton'
import React, { useCallback } from 'react'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'

type PrimaryActionProps = {
  onSubmit?: () => void
  variant?: SeaButtonVariant
  title?: string
}

interface DrawerPrimaryActionProps {
  mode: DrawerMode
  itemName?: string
  edit?: PrimaryActionProps
  create?: PrimaryActionProps
  complete?: PrimaryActionProps
  editCompleted?: PrimaryActionProps
}

export const DrawerPrimaryAction = ({
  mode,
  itemName,
  edit,
  create,
  complete,
  editCompleted,
}: DrawerPrimaryActionProps) => {
  const { isMobileWidth } = useDeviceWidth()

  const getTitle = useCallback(
    (actionType: PrimaryActionProps) => {
      let actionTitle = undefined

      switch (mode) {
        case DrawerMode.Edit:
        case DrawerMode.EditCompleted:
          actionTitle = 'Update'
          break
        case DrawerMode.Create:
          actionTitle = 'Add'
          break
        case DrawerMode.Complete:
          actionTitle = 'Complete'
          break
      }

      if (isMobileWidth) return actionTitle
      if (actionType.title) return actionType.title
      if (itemName) return `${actionTitle} ${itemName}`
      return actionTitle
    },
    [mode, isMobileWidth, itemName]
  )

  if (mode === DrawerMode.Edit && edit) {
    return (
      <SeaEditButton
        variant={edit?.variant ?? SeaButtonVariant.Primary}
        onPress={edit.onSubmit}
        label={getTitle(edit)}
      />
    )
  }

  if (mode === DrawerMode.Create && create) {
    return (
      <SeaSaveButton
        variant={create?.variant ?? SeaButtonVariant.Primary}
        onPress={create.onSubmit}
        label={getTitle(create)}
      />
    )
  }

  if (mode === DrawerMode.Complete && complete) {
    return (
      <SeaCheckButton
        variant={complete?.variant ?? SeaButtonVariant.Primary}
        onPress={complete.onSubmit}
        label={getTitle(complete)}
      />
    )
  }

  if (mode === DrawerMode.EditCompleted && editCompleted) {
    return (
      <SeaEditButton
        variant={editCompleted?.variant ?? SeaButtonVariant.Primary}
        onPress={editCompleted.onSubmit}
        label={getTitle(editCompleted)}
      />
    )
  }

  return null
}
