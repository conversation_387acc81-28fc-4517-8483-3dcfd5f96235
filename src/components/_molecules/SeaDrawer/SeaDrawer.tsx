import React, { ReactElement, useCallback, useEffect, useMemo, useState } from 'react'
import { Modal, Pressable, ScrollView, StyleSheet, useWindowDimensions, View, ViewStyle } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import Animated, { Easing, runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { theme } from '@src/theme'
import { closeFormIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'

export enum DrawerMode {
  Create = 'create',
  Edit = 'edit',
  Complete = 'complete',
  EditCompleted = 'editCompleted',
}

export interface SeaDrawerProps {
  title: string
  titleAdditionalElements?: () => ReactElement
  visible: boolean
  onClose: () => void
  headerActions?: ReactElement<typeof SeaButton>[]
  children: React.ReactNode
  primaryAction?: ReactElement<typeof SeaButton>
  secondaryAction?: ReactElement<typeof SeaButton>
  readonly?: boolean
  style?: ViewStyle
  contentStyle?: ViewStyle
  /**
   * This is to handle the width of the child drawers.
   * For example, if you have a drawer inside a drawer, the level would be 0 for the first drawer and 1 for the second
   * drawer and so on. This will help in adjusting the width of the drawer. If the level is 0, the drawer will take the
   * default screen width.
   */
  level?: number
  isFullWidth?: boolean
  isScrollable?: boolean

  isDirty?: boolean // function that returns if dirty. Returning true it means we should ask before dismissing modal
  confirmCloseDialog?: { title: string; yes: string; no: string }
}
const MAX_DRAWER_WIDTH = 900 // Maximum width of the drawer
const ANIMATION_DURATION = 100

export const SeaDrawer = ({
  title,
  titleAdditionalElements,
  visible,
  onClose,
  children,
  primaryAction,
  secondaryAction,
  headerActions,
  style,
  contentStyle,
  level = 0,
  isFullWidth = false,
  isScrollable = true,
  readonly = false,
  isDirty,
  confirmCloseDialog,
}: SeaDrawerProps) => {
  const { styles } = useStyles(styleSheet)
  const { width: screenWidth } = useWindowDimensions()
  const { isMobileWidth, isTabletWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const drawerWidth = isFullWidth
    ? screenWidth
    : Math.min(
        isMobileWidth || isTabletWidth
          ? screenWidth
          : screenWidth * (isDesktopWidth && !isLargeDesktopWidth ? 0.8 : 0.5) - level * 30,
        MAX_DRAWER_WIDTH
      )

  const translateX = useSharedValue(drawerWidth) // Start off-screen
  const [isModalVisible, setModalVisible] = useState(visible) // Control modal visibility

  // Open and close animations
  useEffect(() => {
    if (visible) {
      setModalVisible(true) // Ensure Modal is open
      translateX.value = withTiming(0, {
        duration: ANIMATION_DURATION,
        easing: Easing.inOut(Easing.quad),
      })
    } else {
      translateX.value = withTiming(
        drawerWidth,
        {
          duration: ANIMATION_DURATION,
          easing: Easing.inOut(Easing.quad),
        },
        () => {
          runOnJS(setModalVisible)(false) // Close Modal after animation completes
        }
      )
    }
  }, [visible, translateX, drawerWidth])

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: translateX.value,
      },
    ],
  }))

  const dynamicPadding = {
    paddingRight: isMobileWidth ? 10 : undefined,
    paddingLeft: isMobileWidth ? 10 : undefined,
  }

  const drawerTitle = useMemo(() => {
    return (
      <SeaStack direction="column" gap={0} align="start" style={{ flexShrink: 1 }}>
        <SeaTypography
          variant={'title'}
          textStyle={{ marginBottom: 0, marginTop: 5 }}
          containerStyle={{ flexShrink: 1 }}>
          {title}
        </SeaTypography>
        {titleAdditionalElements?.()}
      </SeaStack>
    )
  }, [title, titleAdditionalElements])

  const attemptClose = useCallback(async () => {
    if (isDirty) {
      await closeFormIfConfirmed({
        confirmHeader: confirmCloseDialog?.title,
        confirmText: confirmCloseDialog?.yes,
        confirmCancelText: confirmCloseDialog?.no,
        onConfirmed: onClose,
      })
    } else {
      onClose()
    }
  }, [confirmCloseDialog, isDirty, onClose])

  return (
    <Modal
      visible={isModalVisible}
      transparent={!isMobileWidth}
      style={{ zIndex: 1, backgroundColor: 'magenta' }}
      animationType={'none'}>
      <SafeAreaView style={{ flex: 1 }} edges={['top', 'left', 'right']}>
        {/* Backdrop */}

        {/* Commenting out until we fix the nested scroll issue*/}
        {/*<Pressable style={[styles.drawerContainer]} onPress={onClose}>*/}
        <View style={styles.drawerContainer}>
          <Animated.View
            // onStartShouldSetResponder={() => false}
            style={[
              styles.drawerContent,
              animatedStyle,
              { width: drawerWidth }, // Static width
              style,
              isDesktopWidth
                ? {
                    borderTopLeftRadius: 36,
                    borderBottomLeftRadius: 36,
                  }
                : {},
            ]}>
            {/*<Pressable*/}
            {/*  style={{ flex: 1 }}*/}
            {/*  onPress={() => {*/}
            {/* We need to wrap this in a pressable to prevent taps inside the drawer from causing a close event*/}
            {/*  }}*/}
            {/*>*/}
            {/* Header */}
            <SeaStack
              direction={isMobileWidth ? 'column' : 'row'}
              align={isMobileWidth ? 'start' : 'center'}
              justify={'start'}
              gap={10}
              style={StyleSheet.flatten([styles.header, isDesktopWidth ? { borderTopLeftRadius: 36 } : {}])}
              width={'100%'}>
              <SeaStack
                direction={'row'}
                align={'center'}
                justify={isMobileWidth ? 'between' : 'start'}
                gap={10}
                width={'100%'}>
                <Pressable onPress={attemptClose} style={{ marginRight: 10 }}>
                  <View style={styles.backButton}>
                    <SeaIcon icon="arrow_back" size={20} color={theme.colors.black} />
                  </View>
                </Pressable>

                <SeaStack
                  direction="row"
                  gap={5}
                  align="center"
                  style={{ flex: 1 }}
                  justify={isMobileWidth ? 'end' : 'between'}>
                  {!isMobileWidth && drawerTitle}
                  {headerActions && (
                    <SeaStack direction="row" gap={10} align="center">
                      {headerActions}
                    </SeaStack>
                  )}
                </SeaStack>
              </SeaStack>
              {isMobileWidth && drawerTitle}
            </SeaStack>

            {/* Content */}
            {isScrollable ? (
              <ScrollView style={[styles.content, dynamicPadding, contentStyle]}>{children}</ScrollView>
            ) : (
              <View style={[styles.content, dynamicPadding, contentStyle]}>{children}</View>
            )}

            {/* Footer Buttons */}
            {(primaryAction ?? secondaryAction) && !readonly && (
              <View
                style={[
                  styles.buttonRow,
                  dynamicPadding,
                  isDesktopWidth
                    ? {
                        borderBottomLeftRadius: 36,
                      }
                    : {},
                ]}>
                {secondaryAction}
                {primaryAction}
              </View>
            )}
            {/*</Pressable>*/}
          </Animated.View>
          {/*</Pressable>*/}
        </View>
      </SafeAreaView>
    </Modal>
  )
}

const styleSheet = createStyleSheet(theme => ({
  drawerContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawerContent: {
    height: '100%',
    // maxWidth: 750,
    backgroundColor: theme.colors.background.primary,
    position: 'absolute',
    right: 0, // Drawer starts from the right
  },
  header: {
    width: '100%',
    backgroundColor: theme.colors.white,
    paddingLeft: 20,
    paddingVertical: 20,
    paddingRight: 30,
    // gap: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 40,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: theme.colors.white,
    shadowOffset: { width: 0, height: 3 },
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 5,
  },
  title: {
    marginBottom: 0,
  },
  content: {
    flex: 1,
    paddingLeft: 40,
    paddingRight: 30,
    paddingVertical: 20,
  },
  buttonRow: {
    paddingLeft: 40,
    paddingVertical: 20,
    paddingRight: 30,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderColor,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 16,
    backgroundColor: theme.colors.white,
  },
}))
