import React, { useCallback, useMemo } from 'react'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'

export interface VesselSelectInputProps {
  defaultVesselId?: string
  vesselIds?: string[]
  setVesselIds?: (vesselIds: string[]) => void // Function to set vessel IDs, optional for controlled usage);
  isMulti?: boolean
  label?: string
  showIcon?: boolean
  vesselIdOptions?: string[]
  errorText?: string
  hasError?: boolean
  preserveLabelCase?: boolean // Optional prop for custom form usage
  disabled?: boolean // Optional prop to disable the input
}

export const VesselSelectInput = ({
  vesselIds = [],
  setVesselIds,
  isMulti = true,
  label = 'Vessels / Facilities',
  showIcon = false,
  vesselIdOptions,
  errorText,
  hasError,
  preserveLabelCase = false, // Default to false if not provided
  disabled = false, // Default to false if not provided
}: VesselSelectInputProps) => {
  // Shared Data
  const user = sharedState.user.use()
  const vessels = sharedState.vessels.use()

  // Vessel data for the Selector
  const vesselOptionsData = useMemo(() => {
    if (vesselIdOptions) {
      return vesselIdOptions.map((vesselId: string) => ({
        label: vessels?.byId[vesselId]?.name,
        value: vesselId,
      }))
    }

    return (
      user?.vesselIds?.map((vesselId: string) => ({
        label: vessels?.byId[vesselId]?.name,
        value: vesselId,
      })) ?? []
    )
  }, [vessels, user, vesselIdOptions])

  const onItemSelect = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      if (!setVesselIds) return

      switch (action) {
        case CheckBoxActions.SELECT: {
          const newVesselIds = [...vesselIds]

          newVesselIds.push(changedValue)
          setVesselIds(newVesselIds)
          return
        }
        case CheckBoxActions.DESELECT:
          const newVesselIds = vesselIds.filter(vesselId => vesselId !== changedValue)
          setVesselIds(newVesselIds)
          return
        default:
          return
      }
    },
    [vesselIds, setVesselIds]
  )

  const onSetItems = (action: CheckBoxActions, itemIds: string[]) => {
    if (!setVesselIds) return

    switch (action) {
      case CheckBoxActions.SELECT:
        setVesselIds(itemIds)
        return
      case CheckBoxActions.DESELECT:
      default:
        return
    }
  }

  return (
    <SeaSelectInput
      label={label}
      showIcon={showIcon}
      labelIconOptions={{ icon: 'directions_boat_filled' }}
      data={vesselOptionsData}
      onItemSelect={onItemSelect}
      onSetItems={onSetItems}
      selectedItemValues={vesselIds}
      primaryActionOnPress={() => {
        console.log('DO Nothing for now')
      }}
      isMulti={isMulti}
      style={{ width: '100%' }}
      showSearch={true}
      errorText={errorText}
      hasError={hasError}
      preserveLabelCase={preserveLabelCase}
      disabled={disabled}
      allSelectedText="All Vessels"
    />
  )
}
