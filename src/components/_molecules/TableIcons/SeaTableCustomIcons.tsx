import React from 'react'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

export interface SeaTableIconProps extends Omit<SeaIconProps, 'icon'> {}

export const SeaTableIconPerson = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'person'} {...props} />
}

export const SeaTableIconCalendar = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'calendar_month'} {...props} />
}

export const SeaTableIconInterval = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'update'} {...props} />
}

export const SeaTableIconVessel = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'directions_boat_filled'} {...props} />
}

export const SeaTableIconLocation = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'location_on'} {...props} />
}

export const SeaTableIconFlag = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'flag'} size={22} {...props} />
}

export const SeaTableIconEmail = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'email'} {...props} />
}

export const SeaTableIconPhone = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'phone'} {...props} />
}

export const SeaTableIconMail = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'mail'} size={22} {...props} />
}

export const SeaTableIconIssuedBy = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'fact_check'} {...props} />
}
