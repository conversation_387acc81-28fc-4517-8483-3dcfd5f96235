import { View, Text } from 'react-native'
import React, { useMemo, useState } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'

import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { MediaCardFile, SeaMediaCard } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaLabel, SeaLabelVariant } from '@src/components/_molecules/SeaLabels/SeaLabel'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

const DESKTOP_WIDTH = 1024
interface SeaMediaCardProps {
  title: string
  showIcon?: boolean
  labelIconOptions?: SeaIconProps
  files?: MediaCardFile[]
  type: 'manuals' | 'contacts' | 'links'
  preserveLabelCase?: boolean // Optional prop for custom form usage
  layout?: 'horizontal' | 'vertical'
}

export function SeaMedia({
  title,
  showIcon = false,
  labelIconOptions,
  files,
  type,
  preserveLabelCase = false,
  layout,
}: SeaMediaCardProps) {
  const [containerWidth, setContainerWidth] = useState(0)
  const { styles } = useStyles(styleSheet)
  const containerIsBiggerThanDesktop = useMemo(() => containerWidth > DESKTOP_WIDTH, [containerWidth])

  const direction = useMemo(() => {
    if (layout) {
      if (layout === 'horizontal') return 'row'
      return 'column'
    }

    if (containerIsBiggerThanDesktop) {
      return 'row'
    }
    return 'column'
  }, [layout, containerIsBiggerThanDesktop])

  return (
    <View style={styles.container} onLayout={event => setContainerWidth(event.nativeEvent.layout.width)}>
      <SeaLabel variant={SeaLabelVariant.H4} showIcon={showIcon} iconOptions={labelIconOptions}>
        {preserveLabelCase ? title : title.toUpperCase()}
      </SeaLabel>
      <SeaStack
        direction={direction}
        gap={8}
        justify="start"
        align="start"
        style={{
          width: '100%',
          flex: 1,
        }}>
        {files?.map((file, index) => (
          <SeaMediaCard
            key={index}
            title={file.title}
            subTitle={file.subTitle}
            file={file.file}
            actionButtons={file.actionButtons}
            style={containerIsBiggerThanDesktop ? { maxWidth: 360 } : {}}
          />
        ))}
      </SeaStack>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
  },
  title: {
    color: theme.colors.black,
  },
  subTitle: {
    fontSize: 12,
    color: '#616367',
  },
  filesContainer: {
    width: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: 8,
    borderRadius: 8,
  },
}))
