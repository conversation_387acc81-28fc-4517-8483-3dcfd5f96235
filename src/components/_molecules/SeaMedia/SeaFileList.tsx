import { useMemo } from 'react'
import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'

interface SeaFileListProps {
  files?: string[]
  label?: string
}

export const SeaFileList = ({ files = [], label = '' }: SeaFileListProps) => {
  const fileMediaCards = useMemo(() => {
    return files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('Coming soon!')} />],
    })) as MediaCardFile[]
  }, [files])

  if (fileMediaCards && fileMediaCards.length > 0) {
    return (
      <SeaMedia
        type="manuals"
        title={label}
        files={fileMediaCards}
        preserveLabelCase={true}
        showIcon={true}
        labelIconOptions={{ icon: 'file_download' }}
      />
    )
  }
}
