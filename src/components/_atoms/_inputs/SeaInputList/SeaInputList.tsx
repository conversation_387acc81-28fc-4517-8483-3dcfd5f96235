import { Pressable, View } from 'react-native'
import React, { useCallback } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTextInput } from '../SeaTextInput/SeaTextInput'
import { SeaTypography } from '../../SeaTypography/SeaTypography'
import { SeaStack } from '../../SeaStack/SeaStack'
import { SeaIcon } from '../../SeaIcon/SeaIcon'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'

export interface SeaInputListProps {
  onChange?: (value: string[]) => void
  label?: string
  options: string[] // List of options for the input
  addNewText?: string // Optional text for adding a new input
}

export function SeaInputList({ onChange, label, options, addNewText }: SeaInputListProps) {
  const { styles } = useStyles(styleSheet)

  const onInputChange = useCallback(
    (index: number, value: string) => {
      if (onChange) {
        const newOptions = [...options]
        newOptions[index] = value
        onChange(newOptions) // Join options with a comma or any other separator
      }
    },
    [onChange, options]
  )

  const onRemoveOption = useCallback(
    (index: number) => {
      if (onChange) {
        const newOptions = options.filter((_, i) => i !== index)
        onChange(newOptions) // Update the options list
      }
    },
    [options, onChange]
  )

  const onAddNewOption = useCallback(() => {
    if (onChange) {
      const newOptions = [...options, ''] // Add a new empty option
      onChange(newOptions) // Update the options list
    }
  }, [onChange, options])

  return (
    <View style={[styles.container]}>
      <SeaTypography variant="label">{label}</SeaTypography>
      <SeaStack direction="column" gap={10} style={{ width: '100%' }} align="start">
        {options.map((option, index) => (
          <SeaStack key={index} direction="row" gap={5} style={{ width: '100%' }} align="center" justify="center">
            <View style={{ flex: 1 }}>
              <SeaTextInput
                value={option}
                onChangeText={value => onInputChange(index, value)}
                placeholderText={option}
                style={{ marginBottom: 0 }}
                inputContainerStyle={{ marginBottom: 0 }}
                noValidation
              />
            </View>
            <Pressable
              style={{ flex: 1, maxWidth: 40, justifyContent: 'center', alignItems: 'center' }}
              onPress={() => onRemoveOption(index)}>
              <SeaIcon icon="delete" size={24} color="red" />
            </Pressable>
          </SeaStack>
        ))}
        <SeaAddButton label={addNewText ?? 'Add New'} onPress={onAddNewOption} />
      </SeaStack>
    </View>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    width: '100%',
    marginBottom: 12,
  },
}))
