import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react'
import {
  FlatList,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native'
import { colors } from '@src/theme/colors'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'
import { fontFamily } from '@src/theme/typography'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ScrollView } from 'react-native-gesture-handler'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabel, SeaLabelVariant } from '@src/components/_molecules/SeaLabels/SeaLabel'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

interface SeaTagsInputProps {
  options?: string[]
  sortOptions?: boolean
  tags: string[]
  setTags: (tags: string[]) => void
  label?: string
  labelIconOptions?: SeaIconProps
  showIcon?: boolean
  addTagText?: string
  editTagText?: string
  newTagPlaceholder?: string
  style?: ViewStyle
  preserveLabelCase?: boolean
}

export const SeaTagsInput: React.FC<SeaTagsInputProps> = ({
  options = [],
  sortOptions,
  tags,
  setTags,
  label,
  showIcon,
  labelIconOptions,
  addTagText = 'Add Tag',
  editTagText = 'Edit Tag',
  newTagPlaceholder = 'New tag...',
  style,
  preserveLabelCase = false, // Default to false if not provided
}) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editTag, setEditTag] = useState('')
  const [initialTag, setInitialTag] = useState('')
  const [filter, setFilter] = useState('')

  const { styles } = useStyles(styleSheet)

  const inputRef = useRef<TextInput>(null)

  const sortedOptions = useMemo(() => {
    return sortOptions ? [...options].sort() : options
  }, [options, sortOptions])

  const addTag = useCallback(
    (tag: string) => {
      const filtered = tags.filter(t => t !== tag)
      setTags([...filtered, tag])
    },
    [tags]
  )

  const deleteTag = useCallback(
    (tag: string) => {
      setTags(tags.filter(t => t !== tag))
    },
    [tags]
  )

  const replaceTag = useCallback(
    (oldTag: string, newTag: string) => {
      const updated = tags.map(t => (t === oldTag ? newTag : t))
      setTags(updated)
    },
    [tags]
  )

  const openModal = (tag = '') => {
    setInitialTag(tag)
    setEditTag(tag)
    setFilter('')
    setModalVisible(true)
    setTimeout(() => inputRef.current?.focus(), 100)
  }

  const submitTag = () => {
    const trimmed = editTag.trim()
    if (!trimmed) return
    if (initialTag) {
      replaceTag(initialTag, trimmed)
    } else {
      addTag(trimmed)
    }
    setModalVisible(false)
  }

  const filteredOptions = useMemo(() => {
    return sortedOptions.filter(opt => opt.toLowerCase().includes(filter.toLowerCase()))
  }, [sortedOptions, filter])

  return (
    <View style={[styles.container, style]}>
      {label && (
        <SeaLabel variant={SeaLabelVariant.H4} showIcon={showIcon} iconOptions={labelIconOptions}>
          {preserveLabelCase ? label : label.toUpperCase()}
        </SeaLabel>
      )}

      <TouchableOpacity style={styles.tagContainer} onPress={() => openModal()}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {tags.map(tag => (
            <View key={tag} style={styles.tag}>
              <TouchableOpacity onPress={() => openModal(tag)}>
                <Text style={styles.tagText}>{tag}</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => deleteTag(tag)}>
                <Text style={styles.delete}>×</Text>
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>
      </TouchableOpacity>
      <SeaModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        title={initialTag ? editTagText : addTagText}
        maxWidth={400}>
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <SeaStack direction={'row'} justify={'between'} align={'center'} style={styles.inputRow} gap={8}>
            <View
              style={{
                flex: 1,
              }}>
              <SeaTextInput
                value={editTag}
                onChangeText={text => {
                  setEditTag(text)
                  setFilter(text)
                }}
                placeholder={newTagPlaceholder}
              />
            </View>
            <SeaCheckButton key={'check'} onPress={submitTag} />
          </SeaStack>

          <FlatList
            data={filteredOptions}
            keyExtractor={item => item}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.option}
                onPress={() => {
                  initialTag ? replaceTag(initialTag, item) : addTag(item)
                  setModalVisible(false)
                }}>
                <Text style={styles.optionText}>{item}</Text>
              </TouchableOpacity>
            )}
          />
        </KeyboardAvoidingView>
      </SeaModal>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    marginBottom: 12,
    width: '100%',
  },
  labelContainer: {
    marginBottom: 4,
    height: 12,
  },
  label: {
    textTransform: 'uppercase',
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 11,
    fontWeight: '500',
    lineHeight: 12,
    letterSpacing: 0.75,
    color: colors.text.secondary,
  },
  tagContainer: {
    height: 40,
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 16,
    lineHeight: 24,
    fontWeight: 400,
    width: '100%',
    color: theme.colors.text.input,
    backgroundColor: theme.colors.white,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  tag: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background.primary,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    margin: 4,
  },
  tagText: {
    marginRight: 6,
  },
  delete: {
    fontSize: 16,
    color: theme.colors.grey,
  },
  inputRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  option: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  optionText: {
    fontSize: 16,
  },
}))
