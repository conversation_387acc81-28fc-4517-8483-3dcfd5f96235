import { Pressable, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import * as ImagePicker from 'expo-image-picker'
import * as DocumentPicker from 'expo-document-picker'

import { useDeviceWidth } from '@src/hooks/useDevice'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { useBottomSheet } from '@src/providers/BottomSheetProvider'

import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaMediaCard } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'
import { isNative } from '@src/lib/device'
import { convertUriToBase64, makeSeaFiles } from '@src/lib/files'
import { SeaFile } from '@src/lib/fileImports'

interface UploadedFile extends SeaFile {
  uri: string
  type: 'image' | 'file'
  size: number | undefined
}

interface SeaFileUploaderProps {
  label?: string
  initialFiles?: string[]
  files: SeaFile[]
  setFiles: (files: SeaFile[]) => void // Function to set files, should accept an array of SeaFile
  preserveLabelCase?: boolean // Optional prop for custom form usage
  disabled?: boolean // Optional prop to disable the uploader
}
export function SeaFileUploader({
  label = 'Images / Documents',
  initialFiles,
  files = [],
  setFiles,
  preserveLabelCase = false, // Default to false if not provided
  disabled = false, // Default to false if not provided
}: SeaFileUploaderProps) {
  const { styles, theme } = useStyles(styleSheet)

  const { show, hide } = useBottomSheet()
  const [showModal, setShowModal] = useState(false)

  useEffect(() => {
    if (initialFiles && initialFiles.length > 0) {
      setFiles(makeSeaFiles(initialFiles))
    }
  }, [initialFiles, setFiles])

  const onClose = useCallback(() => {
    if (isNative) {
      hide()
    } else {
      // For Desktop
      setShowModal(false)
    }
  }, [hide, setShowModal])

  const addFiles = useCallback(
    (newFiles: UploadedFile[]) => {
      setFiles([...files, ...newFiles])
      onClose()
    },
    [setFiles, files, onClose]
  )

  const handleFileUploadModal = useCallback(() => {
    if (disabled) {
      return
    }
    // For Native devices
    if (isNative) {
      show(<NativeFileUploadOptions addFiles={addFiles} onClose={onClose} showTitle={true} />)
    } else {
      // For Desktop
      setShowModal(true)
    }
  }, [disabled, show, addFiles, onClose])

  const getFileSize = (bytes: number) => {
    const mbs = bytes / (1024 * 1024)
    if (mbs <= 0.01) {
      const kbs = bytes / 1024
      return kbs.toFixed(2) + ' KB'
    }
    return mbs.toFixed(2) + ' MB'
  }

  // renders
  return (
    <View style={styles.container}>
      {label && <SeaTypography variant="label">{preserveLabelCase ? label : label.toUpperCase()}</SeaTypography>}
      <View
        style={[
          styles.uploaderWrapper,
          disabled
            ? {
                opacity: 0.5,
                borderColor: theme.colors.input.disabledBackground,
              }
            : {},
        ]}>
        {disabled ? (
          <View style={styles.uploader}>
            <SeaIcon icon="upload" size={30} />
            <SeaTypography variant="label">CLICK TO UPLOAD</SeaTypography>
          </View>
        ) : (
          <Pressable style={styles.uploader} onPress={handleFileUploadModal}>
            <SeaIcon icon="upload" size={30} />
            <SeaTypography variant="label">CLICK TO UPLOAD</SeaTypography>
          </Pressable>
        )}
        {files.length > 0 && (
          <SeaStack direction="column" gap={10} style={styles.fileList}>
            {files.map((file, index) => (
              <SeaMediaCard
                file={[file.uri as string]}
                key={index}
                title={file.name ?? 'Media'}
                subTitle={file.size ? getFileSize(file.size) : undefined}
                actionButtons={
                  !disabled
                    ? [
                        <SeaDeleteButton
                          key={'delete-' + file.uri}
                          onPress={() => {
                            setFiles(prev => prev.filter((_, i) => i !== index))
                          }}
                        />,
                      ]
                    : []
                }
              />
            ))}
          </SeaStack>
        )}
      </View>

      {/** Modal only shows up on Browser */}
      {showModal && (
        <SeaModal visible={showModal} onClose={() => setShowModal(false)} maxWidth={500} title={'Upload Files'}>
          <NativeFileUploadOptions addFiles={addFiles} onClose={onClose} />
        </SeaModal>
      )}
    </View>
  )
}

const NativeFileUploadOptions = ({
  addFiles,
  onClose,
  showTitle = false,
}: {
  addFiles: (addedFiles: UploadedFile[]) => void
  onClose?: () => void
  showTitle?: boolean
}) => {
  const { isDesktopWidth } = useDeviceWidth()

  const { styles } = useStyles(styleSheet)

  const onImageClick = useCallback(async () => {
    // Close the Bottom Sheet / Modal if passed in
    onClose?.()

    const files = await getFilesFromImagePicker()
    if (files?.length > 0) {
      addFiles(await Promise.all(files))
    }
  }, [addFiles, onClose])

  const onFileClick = useCallback(async () => {
    // Close the Bottom Sheet / Modal if passed in
    onClose?.()

    const result = await DocumentPicker.getDocumentAsync({
      type: [
        'application/pdf',
        'application/msword', // .doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/vnd.ms-excel', // .xls
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'text/plain', // .txt
        'text/csv', // .csv
      ],
      copyToCacheDirectory: true,
      multiple: true,
    })

    if (!result.canceled) {
      const files = result.assets.map(async asset => {
        const fileName = asset.name ?? undefined

        return {
          uri: asset.uri,
          type: 'file' as const,
          size: asset.size,

          // For Firebase
          name: fileName,
          ext: fileName?.substring(fileName?.lastIndexOf('.') + 1),
          contentType: asset.mimeType,
          lastModified: asset.file?.lastModified,
          unique: ('' + Math.random()).substring(2, 12),
          base64: await convertUriToBase64(asset.uri),
        }
      })

      addFiles(await Promise.all(files))
    }
  }, [addFiles, onClose])

  const onCameraClick = useCallback(async () => {
    // Close the Bottom Sheet / Modal if passed in
    onClose?.()

    // Request Permissions
    const { status } = await ImagePicker.requestCameraPermissionsAsync()

    if (status !== 'granted') {
      alert('Please grant permissions to access the camera from the settings.')
      return
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      aspect: [4, 3],
      quality: 1,
    })

    if (!result.canceled) {
      const files = result.assets.map(async asset => {
        const fileName = asset.fileName ?? undefined

        return {
          uri: asset.uri,
          type: 'image' as const,
          size: asset.fileSize,

          // For Firebase
          name: fileName,
          ext: fileName?.substring(fileName?.lastIndexOf('.') + 1),
          contentType: asset.mimeType,
          lastModified: asset.file?.lastModified,
          unique: ('' + Math.random()).substring(2, 12),
          base64: await convertUriToBase64(asset.uri),
        }
      })

      addFiles(await Promise.all(files))
    }
  }, [addFiles, onClose])

  return (
    <SeaStack direction="column" align="center" gap={20} style={styles.contentContainer}>
      {showTitle && <SeaTypography variant="subtitle">Upload File</SeaTypography>}
      <SeaStack direction="column" gap={5} style={{ width: '100%' }}>
        {!isDesktopWidth && (
          <SeaButton
            variant={SeaButtonVariant.Primary}
            label="Take a photo"
            iconOptions={{
              icon: 'photo_camera',
              size: 20,
            }}
            viewStyle={styles.uploadButton}
            onPress={onCameraClick}
          />
        )}
        <SeaButton
          variant={isDesktopWidth ? SeaButtonVariant.Primary : SeaButtonVariant.Tertiary}
          label="Upload Images"
          iconOptions={{
            icon: 'image',
            size: 20,
          }}
          viewStyle={styles.uploadButton}
          onPress={onImageClick}
        />
        <SeaButton
          variant={SeaButtonVariant.Tertiary}
          label="Upload Files"
          iconOptions={{
            icon: 'file_copy',
            size: 20,
          }}
          viewStyle={styles.uploadButton}
          onPress={onFileClick}
        />
      </SeaStack>
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
  },
  uploaderWrapper: {
    width: '100%',
    backgroundColor: theme.colors.white,
    borderWidth: 2,
    borderColor: theme.colors.borderColor,
    borderStyle: 'dashed',
    borderRadius: 8,
  },
  uploaderWrapperDisabled: {
    opacity: 0.5,
    backgroundColor: theme.colors.input.disabledBackground,
  },
  uploader: {
    width: '100%',
    height: 110,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    width: '100%',
    flex: 1,
  },
  uploadButton: {
    width: '100%',
  },
  fileList: {
    width: '100%',
    padding: 16,
  },
}))

/**
 * Open the image picker and return the selected files
 *
 * @returns Promise<UploadedFile[]>
 */
export const getFilesFromImagePicker = async (): Promise<UploadedFile[]> => {
  //No permission request is needed for launching the image library
  const result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ['images'],
    allowsEditing: false,
    allowsMultipleSelection: true,
    aspect: [4, 3],
    quality: 1,
  })

  if (!result.canceled) {
    const files = result.assets.map(async asset => {
      const fileName = asset.fileName ?? undefined

      return {
        uri: asset.uri,
        type: 'image' as const,
        size: asset.fileSize,

        // For Firebase
        name: fileName,
        ext: fileName?.substring(fileName?.lastIndexOf('.') + 1),
        contentType: asset.mimeType,
        lastModified: asset.file?.lastModified,
        unique: ('' + Math.random()).substring(2, 12),
        base64: await convertUriToBase64(asset.uri),
      }
    })
    return files
  }
  return []
}
