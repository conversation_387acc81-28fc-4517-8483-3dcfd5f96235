import React from 'react'
import {
  DimensionValue,
  KeyboardAvoidingView,
  Modal,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native'
import { SeaCloseButton } from '@src/components/_molecules/IconButtons/SeaCloseButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

export interface SeaModalProps {
  visible: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  maxHeight?: number | string
  maxWidth?: number | string
  style?: ViewStyle
}

export const SeaModal = ({
  visible,
  onClose,
  title,
  children,
  maxHeight = '80%',
  maxWidth = '90%',
  style,
}: SeaModalProps) => {
  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <Pressable style={styles.backdrop} onPress={onClose}>
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={styles.wrapper}>
          <Pressable
            style={[
              styles.modalBox,
              {
                maxHeight: maxHeight as DimensionValue,
                maxWidth: maxWidth as DimensionValue,
              },
              style,
            ]}
            onPress={() => console.log('Modal Pressed')}>
            {title && (
              <View style={styles.header}>
                <SeaTypography variant={'subtitle'} textStyle={styles.title} containerStyle={styles.titleContainer}>
                  {title}
                </SeaTypography>
                <SeaCloseButton key={'close'} onPress={onClose} />
              </View>
            )}
            <View style={styles.content}>{children}</View>
          </Pressable>
        </KeyboardAvoidingView>
      </Pressable>
    </Modal>
  )
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  wrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  modalBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  titleContainer: {
    flex: 1,
  },
  closeButton: {
    fontSize: 24,
    color: '#999',
    marginLeft: 12,
  },
  content: {
    flexShrink: 1,
  },
})
