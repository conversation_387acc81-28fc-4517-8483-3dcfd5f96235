import { Image, View, ViewStyle } from 'react-native'
import React from 'react'

interface SeaHeaderImageProps {
  mobile?: boolean
  showIcon?: boolean
  style?: ViewStyle
}

export const SeaHeaderImage = ({ mobile = false, showIcon = true }: SeaHeaderImageProps) => {
  if (mobile) {
    const scale = 1.3

    return (
      <View style={{ alignItems: 'center', paddingTop: 4 }}>
        <Image
          source={require('@assets/sea-flux-logo-full-blue.png')}
          resizeMode={'cover'}
          style={{
            height: 24 * scale,
            width: 144 * scale,
          }}
        />
      </View>
    )
  }
  return (
    <View style={{ alignItems: 'center' }}>
      {showIcon ? (
        <>
          <Image
            source={require('@assets/sea-flux-logo-full-blue.png')}
            resizeMode={'cover'}
            style={{
              height: 24,
              width: 144,
            }}
          />
        </>
      ) : (
        <>
          <Image
            source={require('@assets/sea-flux-logo-icon.svg')}
            resizeMode={'contain'}
            style={{
              height: 24,
              width: 24,
            }}
          />
        </>
      )}
    </View>
  )
}
