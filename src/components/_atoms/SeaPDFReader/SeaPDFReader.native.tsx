import React, { useEffect, useState } from 'react'
import { StyleSheet, Dimensions, View } from 'react-native'
import Pdf from 'react-native-pdf'
import { SeaPDFReaderProps } from './SeaPDFReader.web'
import { sharedState } from '@src/shared-state/shared-state'
import { getCachedFileSrc, getCachedFileUri } from '@src/shared-state/FileSyncSystem/cachedFiles'
import { getFileSrcFromString } from '@src/lib/files'
import { SeaLoadingSpinner } from '../SeaLoadingSpinner/SeaLoadingSpinner'

export const SeaPDFReader = ({ file }: SeaPDFReaderProps) => {
  const onlineStatus = sharedState.onlineStatus.use()
  const [fileUri, setFileUri] = useState<string>('')
  const [fileLoaded, setFileLoaded] = useState(false)

  useEffect(() => {
    if (!file || fileLoaded) return

    let isMounted = true
    let cancelFetch = false

    const loadFileUri = async () => {
      let _uri = ''

      try {
        if (file.startsWith('data:application/pdf')) {
          _uri = file
        } else if (!cancelFetch) {
          const uri = await getCachedFileUri(file)
          if (uri) {
            _uri = uri
          }
        }
      } catch (error) {
        if (onlineStatus?.isOnline && !cancelFetch) {
          try {
            _uri = await getFileSrcFromString(file)
          } catch (error) {
            console.debug('Failed to load PDF file from string', error)
          }
        } else {
          console.debug('Offline or file not cached')
        }
      } finally {
        if (isMounted && _uri) {
          setFileUri(_uri)
          setFileLoaded(true)
        }
      }
    }

    loadFileUri()

    return () => {
      isMounted = false
      cancelFetch = true
    }
  }, [file])

  return fileUri ? (
    <View style={styles.container}>
      <Pdf
        source={{ uri: fileUri }}
        style={styles.pdf}
        onError={error => {
          console.debug(error)
        }}
        renderActivityIndicator={() => (
          <View
            style={{
              flex: 1,
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
            }}>
            <SeaLoadingSpinner />
          </View>
        )}
      />
    </View>
  ) : (
    <></>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    aspectRatio: 4 / 5,
    margin: 'auto',
  },
  pdf: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
})
