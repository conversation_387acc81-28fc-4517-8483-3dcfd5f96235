import React, { useState } from 'react'
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { DragHandleIcon } from './DragHandleIcon'

interface SimpleSeaDraggablesProps {
  items: unknown[]
  onReorder?: (newOrder: unknown[]) => void
  children: React.ReactNode[] | undefined
}

export const SimpleSeaDraggable: React.FC<SimpleSeaDraggablesProps> = ({ items, onReorder, children }) => {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)

  const moveUp = (index: number) => {
    if (index > 0) {
      const newItems = [...items]
      const [item] = newItems.splice(index, 1)
      newItems.splice(index - 1, 0, item)
      onReorder?.(newItems)
    }
  }

  const moveDown = (index: number) => {
    if (index < items.length - 1) {
      const newItems = [...items]
      const [item] = newItems.splice(index, 1)
      newItems.splice(index + 1, 0, item)
      onReorder?.(newItems)
    }
  }

  return (
    <View style={styles.container}>
      {children?.map((child, index) => (
        <View key={index} style={styles.item}>
          <View style={styles.dragControls}>
            <TouchableOpacity
              style={[styles.button, index === 0 && styles.disabledButton]}
              onPress={() => moveUp(index)}
              disabled={index === 0}>
              <Text style={styles.buttonText}>↑</Text>
            </TouchableOpacity>
            <DragHandleIcon size={16} color="#666666" />
            <TouchableOpacity
              style={[styles.button, index === items.length - 1 && styles.disabledButton]}
              onPress={() => moveDown(index)}
              disabled={index === items.length - 1}>
              <Text style={styles.buttonText}>↓</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={[styles.content, selectedIndex === index && styles.selected]}
            onPress={() => setSelectedIndex(index)}>
            {child}
          </TouchableOpacity>
        </View>
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  dragControls: {
    flexDirection: 'column',
    alignItems: 'center',
    marginRight: 12,
    paddingVertical: 4,
  },
  button: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 2,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 8,
  },
  selected: {
    backgroundColor: '#f0f8ff',
    borderColor: '#007AFF',
    borderWidth: 2,
    borderRadius: 4,
  },
})
