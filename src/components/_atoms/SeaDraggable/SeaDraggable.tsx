import React, { useState, useRef, useEffect, useMemo } from 'react'
import {
  View,
  StyleSheet,
  TouchableOpacity,
  PanResponder,
  Animated,
  ScrollView,
  Platform,
  ViewProps,
} from 'react-native'
import { DragHandleIcon } from './DragHandleIcon'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { isNative } from '@src/lib/device'

type Layout = { x: number; y: number; width: number; height: number }

export interface SeaDraggablesProps {
  type?: 'tiles' | 'customForms' | 'list'
  items: unknown[]
  onReorder?: (newOrder: unknown[]) => void
  onDrop?: (initialIndex: number, newIndex: number) => void
  setIsInternalDrag?: (isInternalDrag: boolean) => void
  selectedIndex?: number | null
  setSelectedIndex?: (index: number | null) => void
  flexDirection?: 'row' | 'column'
  nonDraggableStartComponent?: React.ReactNode
  nonDraggableEndComponent?: React.ReactNode
  children?: React.ReactNode[]
  scrollViewRef?: React.RefObject<ScrollView> // Add optional ScrollView ref
  autoScrollThreshold?: number // Distance from edge to trigger auto-scroll
  autoScrollSpeed?: number // Speed of auto-scroll
  scrollOffset?: { x: number; y: number } // Current scroll position
  onDragStateChange?: (isDragging: boolean) => void // Callback when drag state changes
  isItemDraggable?: (item: unknown, index: number) => boolean // Callback to determine if item should be draggable
}

export const SeaDraggable: React.FC<SeaDraggablesProps> = ({
  type = 'tiles',
  items,
  onReorder,
  onDrop,
  setIsInternalDrag,
  selectedIndex,
  setSelectedIndex,
  flexDirection = 'row',
  nonDraggableStartComponent,
  nonDraggableEndComponent,
  children,
  scrollViewRef,
  autoScrollThreshold = 50, // Default 50px from edge
  autoScrollSpeed = 10, // Default scroll speed
  scrollOffset = { x: 0, y: 0 }, // Default scroll offset
  onDragStateChange, // Callback when drag state changes
  isItemDraggable, // Callback to determine if item should be draggable
}) => {
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null)
  const [highlightedIndex, setHighlightedIndex] = useState<number | null>(null)
  const [draggedOverIndex, setDraggedOverIndex] = useState<number | null>(null)
  const [isDragEnabled, setIsDragEnabled] = useState(false)
  const dragItem = useRef<number | null>(null)
  const itemLayouts = useRef<Layout[]>([])
  const dragStartPosition = useRef<{ x: number; y: number }>({ x: 0, y: 0 })
  const dragOffset = useRef<{ x: number; y: number }>({ x: 0, y: 0 })
  const autoScrollInterval = useRef<NodeJS.Timeout | null>(null)
  const containerLayout = useRef<Layout | null>(null)

  // Animation values for smooth dragging
  const draggedItemPosition = useRef(new Animated.ValueXY()).current
  const draggedItemScale = useRef(new Animated.Value(1)).current
  const draggedItemOpacity = useRef(new Animated.Value(1)).current

  const { isMobileWidth } = useDeviceWidth()

  // Only enable drag and drop on tablets and desktop, not on mobile phones
  const dragDropEnabled = !isMobileWidth

  const notEnoughDraggableItems = useMemo(() => {
    if (!Array.isArray(children) || children.length <= 1) {
      return true
    }
    const draggableChildren = children.filter(child => child && !(child as React.ReactElement).props.nonDraggable)
    return draggableChildren.length <= 1
  }, [children])

  // Create a single pan responder that works on the entire container
  const panResponder = useMemo(() => {
    return PanResponder.create({
      onStartShouldSetPanResponder: () => {
        const shouldSet = dragDropEnabled && isDragEnabled && dragItem.current !== null

        return shouldSet
      },
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        const shouldSet =
          dragDropEnabled &&
          isDragEnabled &&
          dragItem.current !== null &&
          (Math.abs(gestureState.dx) > 2 || Math.abs(gestureState.dy) > 2)
        return shouldSet
      },
      onPanResponderGrant: evt => {
        console.debug('Pan responder granted for dragging index:', dragItem.current)

        if (dragItem.current !== null) {
          // Don't set draggingIndex here since it's already set in handleDragStart
          // setDraggingIndex(dragItem.current)
          // setIsInternalDrag?.(true) // This is also already set

          // Get the initial touch position
          const initialTouchX = evt.nativeEvent.locationX
          const initialTouchY = evt.nativeEvent.locationY

          // Store the initial touch position
          dragStartPosition.current = { x: initialTouchX, y: initialTouchY }

          // Update the drag position and offset based on actual touch position
          const itemLayout = itemLayouts.current[dragItem.current]

          if (itemLayout) {
            // Calculate the offset from the touch point to the item's top-left corner
            dragOffset.current = {
              x: initialTouchX - itemLayout.x,
              y: initialTouchY - itemLayout.y,
            }

            // Position the floating dragged item relative to viewport
            // Subtract scroll offset to convert from content coordinates to viewport coordinates
            draggedItemPosition.setValue({
              x: itemLayout.x,
              y: itemLayout.y - scrollOffset.y,
            })
          } else {
            // Fallback: use touch position
            dragOffset.current = { x: 0, y: 0 }
            draggedItemPosition.setValue({
              x: initialTouchX,
              y: initialTouchY,
            })
          }
        }
      },
      onPanResponderMove: (evt, _gestureState) => {
        if (dragItem.current !== null) {
          // Use the cursor position relative to the container
          const cursorX = evt.nativeEvent.locationX
          const cursorY = evt.nativeEvent.locationY

          // Check if we need to auto-scroll
          if (scrollViewRef?.current) {
            // For auto-scroll, we need to check if the cursor is near the edges
            // of the visible viewport, not the container

            // Simple check: if cursor is near top or bottom of the container
            // We'll use a more direct approach - check if cursor is within threshold of edges
            if (cursorY < autoScrollThreshold && scrollOffset.y > 0) {
              console.debug('Near top edge, should scroll up')
              startAutoScroll('up')
            } else if (containerLayout.current && cursorY > containerLayout.current.height - autoScrollThreshold) {
              console.debug('Near bottom edge, should scroll down')
              startAutoScroll('down')
            } else {
              stopAutoScroll()
            }
          }

          // If we don't have enough layout data, refresh it
          const validLayouts = itemLayouts.current.filter(layout => layout != null)
          if (validLayouts.length < items.length * 0.8) {
            // If less than 80% of layouts are valid
            console.debug('Not enough valid layouts, refreshing...')
            refreshLayoutData()
          }

          // Update the dragged item position to follow the cursor
          // Use the stored offset to maintain the relative position
          // Position relative to viewport, not the scrolled content
          const newX = cursorX - dragOffset.current.x
          const newY = cursorY - dragOffset.current.y

          draggedItemPosition.setValue({
            x: newX,
            y: newY,
          })

          // For drop zone calculation, we need different coordinate systems for different platforms
          // On web, the cursor position is already in the correct coordinate system
          // On native (iOS/Android), we need to account for scroll offset
          const adjustedCursorX = cursorX
          const adjustedCursorY = isNative ? cursorY + scrollOffset.y : cursorY

          const overIndex = findIndexFromPosition(adjustedCursorX, adjustedCursorY)

          if (overIndex !== null && overIndex !== draggedOverIndex) {
            // Allow setting draggedOverIndex even if it equals dragItem.current
            // This helps with detecting when we want to move to adjacent positions
            setDraggedOverIndex(overIndex)
          }
        }
      },
      onPanResponderRelease: () => {
        stopAutoScroll()
        handleDragEnd()
      },
      onPanResponderTerminate: () => {
        stopAutoScroll()
        handleDragEnd()
      },
    })
  }, [
    isDragEnabled,
    draggedOverIndex,
    setIsInternalDrag,
    dragDropEnabled,
    scrollOffset,
    autoScrollThreshold,
    autoScrollSpeed,
    items.length,
  ])

  const findIndexFromPosition = (x: number, y: number): number | null => {
    if (itemLayouts.current.length === 0) {
      refreshLayoutData()
      return null
    }

    // Filter out null/undefined layouts
    const validLayouts = itemLayouts.current.filter((layout, index) => {
      if (!layout) {
        console.debug(`Layout for item ${index} is missing, skipping`)
        return false
      }
      return true
    })

    if (validLayouts.length === 0) {
      console.debug('No valid layouts found, refreshing...')
      refreshLayoutData()
      return null
    }

    // For customForms with dynamic widths, we need special handling
    if (type === 'customForms' && flexDirection === 'row') {
      return findIndexFromPositionForCustomForms(x, y)
    }

    // For vertical lists, we primarily care about the Y position
    if (flexDirection === 'column') {
      // First check if we're over an existing item
      for (let i = 0; i < itemLayouts.current.length; i++) {
        const layout = itemLayouts.current[i]
        if (!layout) continue

        const itemTop = layout.y
        const itemBottom = layout.y + layout.height
        const itemLeft = layout.x
        const itemRight = layout.x + layout.width

        // Check if cursor is over this item
        if (x >= itemLeft && x <= itemRight && y >= itemTop && y <= itemBottom) {
          // Check if we're in the top half (insert before) or bottom half (insert after)
          const itemMiddle = layout.y + layout.height / 2
          const targetIndex = y <= itemMiddle ? i : i + 1
          return findValidDropPosition(targetIndex)
        }
      }

      // If not over an item, check if we're in the vertical gap between items
      for (let i = 0; i < itemLayouts.current.length - 1; i++) {
        const currentLayout = itemLayouts.current[i]
        const nextLayout = itemLayouts.current[i + 1]
        if (!currentLayout || !nextLayout) continue

        const currentBottom = currentLayout.y + currentLayout.height
        const nextTop = nextLayout.y

        // Check if we're in the gap between items
        if (y >= currentBottom && y <= nextTop) {
          return findValidDropPosition(i + 1)
        }
      }

      // Check if we're above the first item
      if (itemLayouts.current[0] && y < itemLayouts.current[0].y) {
        return findValidDropPosition(0)
      }

      // Check if we're below the last item
      const lastLayout = itemLayouts.current[itemLayouts.current.length - 1]
      if (lastLayout && y > lastLayout.y + lastLayout.height) {
        return findValidDropPosition(itemLayouts.current.length)
      }
    } else {
      // For horizontal lists, we primarily care about the X position
      // First check if we're over an existing item
      for (let i = 0; i < itemLayouts.current.length; i++) {
        const layout = itemLayouts.current[i]
        if (!layout) continue

        const itemLeft = layout.x
        const itemRight = layout.x + layout.width
        const itemTop = layout.y
        const itemBottom = layout.y + layout.height

        // Check if cursor is over this item
        if (x >= itemLeft && x <= itemRight && y >= itemTop && y <= itemBottom) {
          // Check if we're in the left half (insert before) or right half (insert after)
          const itemMiddle = layout.x + layout.width / 2
          const targetIndex = x <= itemMiddle ? i : i + 1
          return findValidDropPosition(targetIndex)
        }
      }

      // If not over an item, check if we're in the horizontal gap between items
      for (let i = 0; i < itemLayouts.current.length - 1; i++) {
        const currentLayout = itemLayouts.current[i]
        const nextLayout = itemLayouts.current[i + 1]
        if (!currentLayout || !nextLayout) continue

        const currentRight = currentLayout.x + currentLayout.width
        const nextLeft = nextLayout.x

        // Check if we're in the gap between items
        if (x >= currentRight && x <= nextLeft) {
          return findValidDropPosition(i + 1)
        }
      }

      // Check if we're to the left of the first item
      if (itemLayouts.current[0] && x < itemLayouts.current[0].x) {
        return findValidDropPosition(0)
      }

      // Check if we're to the right of the last item
      const lastLayout = itemLayouts.current[itemLayouts.current.length - 1]
      if (lastLayout && x > lastLayout.x + lastLayout.width) {
        return findValidDropPosition(itemLayouts.current.length)
      }
    }

    // If cursor is not in any valid drop zone, return null
    return null
  }

  const findIndexFromPositionForCustomForms = (x: number, y: number): number | null => {
    // Filter out null/undefined layouts
    const validLayoutIndices = itemLayouts.current
      .map((layout, index) => ({ layout, index }))
      .filter(({ layout }) => layout != null)

    if (validLayoutIndices.length === 0) {
      refreshLayoutData()
      return null
    }

    // Group items by rows based on their Y position
    const rows: {
      items: { index: number; layout: Layout }[]
      y: number
    }[] = []

    for (const { layout, index } of validLayoutIndices) {
      // Find or create a row for this item (increased tolerance for row detection)
      let row = rows.find(r => Math.abs(r.y - layout.y) < 30)
      if (!row) {
        row = { items: [], y: layout.y }
        rows.push(row)
      }

      row.items.push({ index, layout })
    }

    // Sort rows by Y position
    rows.sort((a, b) => a.y - b.y)

    // Sort items within each row by X position
    rows.forEach(row => {
      row.items.sort((a, b) => a.layout.x - b.layout.x)
    })

    // Find which row we're in or closest to
    let targetRow = null
    let minDistance = Infinity

    for (const row of rows) {
      const rowTop = row.y
      const rowBottom = row.y + (row.items[0]?.layout.height || 0)

      // Check if we're within this row (with generous tolerance)
      if (y >= rowTop - 15 && y <= rowBottom + 15) {
        targetRow = row
        break
      }

      // Calculate distance to this row's center
      const rowCenter = rowTop + (rowBottom - rowTop) / 2
      const distance = Math.abs(y - rowCenter)

      if (distance < minDistance) {
        minDistance = distance
        targetRow = row
      }
    }

    if (!targetRow) {
      console.debug('No target row found')
      return null
    }

    // Within the target row, find the best position
    for (const item of targetRow.items) {
      const itemLeft = item.layout.x
      const itemRight = item.layout.x + item.layout.width
      const itemMiddle = item.layout.x + item.layout.width / 2

      // If we're over this item (with some tolerance)
      if (x >= itemLeft - 5 && x <= itemRight + 5) {
        const targetIndex = x <= itemMiddle ? item.index : item.index + 1
        return findValidDropPosition(targetIndex)
      }
    }

    // Check if we're in the gaps between items in this row
    for (let i = 0; i < targetRow.items.length - 1; i++) {
      const currentItem = targetRow.items[i]
      const nextItem = targetRow.items[i + 1]
      const currentRight = currentItem.layout.x + currentItem.layout.width
      const nextLeft = nextItem.layout.x

      if (x >= currentRight && x <= nextLeft) {
        return findValidDropPosition(nextItem.index)
      }
    }

    // Check if we're before the first item in this row
    if (x < targetRow.items[0].layout.x) {
      return findValidDropPosition(targetRow.items[0].index)
    }

    // Check if we're after the last item in this row
    const lastItem = targetRow.items[targetRow.items.length - 1]
    if (x > lastItem.layout.x + lastItem.layout.width) {
      return findValidDropPosition(lastItem.index + 1)
    }

    // If we can't determine a position, return null
    console.debug('Could not determine position in custom forms')
    return null
  }

  const refreshLayoutData = () => {
    console.debug('Refreshing layout data for all items')
    const containerRefCurrent = containerRef.current

    if (!containerRefCurrent) return

    for (let i = 0; i < itemRefs.current.length; i++) {
      const itemRef = itemRefs.current[i]
      if (itemRef) {
        itemRef.measureLayout(
          containerRefCurrent,
          (x, y, width, height) => {
            itemLayouts.current[i] = { x, y, width, height }
          },
          () => {
            console.error(`Failed to refresh layout for item ${i}`)
          }
        )
      }
    }
  }

  const startAutoScroll = (direction: 'up' | 'down' | 'left' | 'right') => {
    if (autoScrollInterval.current) {
      clearInterval(autoScrollInterval.current)
    }

    if (!scrollViewRef?.current) {
      return
    }

    console.debug('Starting auto-scroll:', direction)

    // Keep track of current scroll position
    let currentScrollY = scrollOffset.y
    let currentScrollX = scrollOffset.x

    autoScrollInterval.current = setInterval(() => {
      if (!scrollViewRef?.current) return

      // Calculate new scroll position
      if (direction === 'up') {
        currentScrollY = Math.max(0, currentScrollY - autoScrollSpeed)
      } else if (direction === 'down') {
        currentScrollY = currentScrollY + autoScrollSpeed
      } else if (direction === 'left') {
        currentScrollX = Math.max(0, currentScrollX - autoScrollSpeed)
      } else if (direction === 'right') {
        currentScrollX = currentScrollX + autoScrollSpeed
      }

      scrollViewRef.current.scrollTo({
        x: currentScrollX,
        y: currentScrollY,
        animated: false,
      })
    }, 16) // 60fps for smooth scrolling
  }

  const stopAutoScroll = () => {
    if (autoScrollInterval.current) {
      clearInterval(autoScrollInterval.current)
      autoScrollInterval.current = null
      console.debug('Stopped auto-scroll')
    }
  }

  const handleDragStart = (index: number) => {
    if (!dragDropEnabled) {
      console.debug('Drag disabled - not a tablet/desktop device')
      return
    }

    // Check if the item at this index is draggable
    if (isItemDraggable && !isItemDraggable(items[index], index)) {
      console.debug('Item at index', index, 'is not draggable')
      return
    }

    // Refresh layout data at the start of each drag to ensure accuracy
    refreshLayoutData()

    // Set up drag state
    dragItem.current = index
    setIsDragEnabled(true)
    setDraggingIndex(index)

    // Notify parent about drag state change
    onDragStateChange?.(true)

    if (setIsInternalDrag) {
      setIsInternalDrag(true)
    }

    // Reset animation values
    draggedItemScale.setValue(1)
    draggedItemOpacity.setValue(1)

    // Try to get layout info immediately
    let itemLayout = itemLayouts.current[index]

    // If we don't have layout info, try to get it from the DOM
    if (!itemLayout) {
      const itemRef = itemRefs.current[index]
      const containerRefCurrent = containerRef.current

      if (itemRef && containerRefCurrent) {
        // Try to get layout synchronously
        itemRef.measureLayout(
          containerRefCurrent,
          (x, y, width, height) => {
            itemLayouts.current[index] = { x, y, width, height }
            itemLayout = { x, y, width, height }
          },
          () => {
            console.error('Failed to get layout synchronously in handleDragStart')
          }
        )
      }
    }

    if (itemLayout) {
      // Pre-position the dragged item relative to viewport
      // Subtract scroll offset to convert from content coordinates to viewport coordinates
      draggedItemPosition.setValue({
        x: itemLayout.x,
        y: itemLayout.y - scrollOffset.y,
      })

      // Set initial offset to center of item
      dragOffset.current = {
        x: itemLayout.width / 2,
        y: itemLayout.height / 2,
      }

      // Immediate visual feedback
      Animated.parallel([
        Animated.timing(draggedItemScale, {
          toValue: 1.1,
          duration: 150,
          useNativeDriver: false,
        }),
        Animated.timing(draggedItemOpacity, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: false,
        }),
      ]).start()
    }
  }

  const handleDragEnd = () => {
    // Stop auto-scroll when drag ends
    stopAutoScroll()

    // Reset animations
    Animated.parallel([
      Animated.timing(draggedItemScale, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(draggedItemOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start()

    if (draggedOverIndex !== null && dragItem.current !== null) {
      // Allow reordering even when moving to adjacent positions
      // We check if the calculated insert index would actually change the order
      const draggedItemIndex = dragItem.current
      const targetIndex = draggedOverIndex

      // Create a copy of the items array
      const newItems = [...items]

      // Remove the dragged item from its original position
      const [draggedItem] = newItems.splice(draggedItemIndex, 1)

      // Calculate the correct insertion index
      // The targetIndex represents where we want the item to end up in the final array
      // After removing the dragged item, we need to adjust for the shifted indices

      let insertIndex = targetIndex

      if (draggedItemIndex < targetIndex) {
        // Moving forward: After removing an item from before the target position,
        // all indices shift down by 1, so we insert at targetIndex - 1
        insertIndex = targetIndex - 1

        // But if the target is immediately after the dragged item (adjacent move),
        // we need to insert at the target position to achieve the swap
        if (targetIndex === draggedItemIndex + 1) {
          insertIndex = targetIndex
        }
      } else {
        // Moving backward: Insert at targetIndex (no adjustment needed)
        insertIndex = targetIndex
      }

      // Ensure insertIndex is within bounds
      insertIndex = Math.max(0, Math.min(insertIndex, newItems.length))

      // Check if the insertion position would place the item at a non-draggable position
      // and adjust to find the nearest valid position
      if (isItemDraggable) {
        let finalInsertIndex = insertIndex

        // If we're trying to insert at a position where there's a non-draggable item,
        // we need to find a valid position
        if (finalInsertIndex < newItems.length && !isItemDraggable(newItems[finalInsertIndex], finalInsertIndex)) {
          // Look for the nearest valid position
          let searchForward = finalInsertIndex
          let searchBackward = finalInsertIndex - 1
          let validPosition = null

          // Search both directions to find the nearest valid position
          while (validPosition === null && (searchForward <= newItems.length || searchBackward >= 0)) {
            // Check forward direction first
            if (searchForward <= newItems.length) {
              if (searchForward === newItems.length || isItemDraggable(newItems[searchForward], searchForward)) {
                validPosition = searchForward
                break
              }
              searchForward++
            }

            // Check backward direction
            if (searchBackward >= 0) {
              if (isItemDraggable(newItems[searchBackward], searchBackward)) {
                validPosition = searchBackward + 1 // Insert after the draggable item
                break
              }
              searchBackward--
            }
          }

          if (validPosition !== null) {
            finalInsertIndex = validPosition
          }
        }

        insertIndex = finalInsertIndex
      }

      // Insert the dragged item at the new position
      newItems.splice(insertIndex, 0, draggedItem)

      if (onReorder) {
        onReorder(newItems)
      }
      if (onDrop) {
        onDrop(draggedItemIndex, insertIndex)
      }

      // Update the selected index to the new position
      setSelectedIndex?.(insertIndex)
      setHighlightedIndex(insertIndex)
    }

    // Reset all drag state
    setDraggingIndex(null)
    setDraggedOverIndex(null)
    setIsDragEnabled(false)
    dragItem.current = null

    // Notify parent about drag state change
    onDragStateChange?.(false)

    if (setIsInternalDrag) {
      setIsInternalDrag(false)
    }
  }

  const containerRef = useRef<View>(null)
  const itemRefs = useRef<(View | null)[]>([])

  // Initialize itemRefs array when children change
  useEffect(() => {
    const newLength = children?.length ?? 0
    if (itemRefs.current.length !== newLength) {
      itemRefs.current = new Array(newLength).fill(null)
    }
    // Only clear layout cache when children array length changes significantly
    if (itemLayouts.current.length !== newLength) {
      itemLayouts.current = []
    }
  }, [children?.length, itemRefs, itemLayouts])

  // Measure container layout for auto-scroll calculations
  const handleContainerLayout = (event: { nativeEvent: { layout: Layout } }) => {
    const { x, y, width, height } = event.nativeEvent.layout
    containerLayout.current = { x, y, width, height }
  }

  // Clear layout cache when items array length changes (not just content)
  useEffect(() => {
    if (itemLayouts.current.length !== items.length) {
      itemLayouts.current = []
    }
  }, [items, itemLayouts.current])

  const handleItemLayout = (event: { nativeEvent: { layout: Layout } }, index: number) => {
    const { width, height } = event.nativeEvent.layout

    // Get the absolute position relative to the container
    const itemRef = itemRefs.current[index]
    const containerRefCurrent = containerRef.current

    if (itemRef && containerRefCurrent) {
      // Use setTimeout to ensure the layout is settled
      setTimeout(() => {
        itemRef.measureLayout(
          containerRefCurrent,
          (x, y) => {
            itemLayouts.current[index] = { x, y, width, height }
          },
          () => {
            console.error(`Failed to measure layout for item ${index}`)
            // Fallback to relative positioning
            const { x, y } = event.nativeEvent.layout
            itemLayouts.current[index] = { x, y, width, height }
          }
        )
      }, 10) // Small delay to ensure layout is settled
    } else {
      // Fallback to relative positioning
      const { x, y } = event.nativeEvent.layout
      itemLayouts.current[index] = { x, y, width, height }
    }
  }

  useEffect(() => {
    if (selectedIndex !== undefined) {
      setHighlightedIndex(selectedIndex)
    }
  }, [selectedIndex])

  if (notEnoughDraggableItems) {
    return (
      <View style={styles.container}>
        {nonDraggableStartComponent && <View style={styles.nonDraggableItem}>{nonDraggableStartComponent}</View>}
        {children}
        {nonDraggableEndComponent && <View style={styles.nonDraggableItem}>{nonDraggableEndComponent}</View>}
      </View>
    )
  }
  const canAddDragHandle = (type: string, index: number) => {
    // Check if this child element is marked as non-draggable
    const childElement = children?.[index] as React.ReactElement
    if (childElement?.props?.nonDraggable) {
      return false
    }

    // Check if custom callback says this item is not draggable
    if (isItemDraggable && !isItemDraggable(items[index], index)) {
      return false
    }

    return (
      dragDropEnabled &&
      (['list', 'customForms'].includes(type) || highlightedIndex === index) &&
      !notEnoughDraggableItems
    )
  }

  const getDragHandle = (index: number) => {
    if (!dragDropEnabled) {
      return null // Don't show drag handle on mobile phones
    }

    return (
      <View style={styles.dragHandleWrapper}>
        <TouchableOpacity
          style={[
            styles.dragHandle,
            type === 'list' && styles.dragHandleList,
            type === 'customForms' && styles.dragHandleCustomForms,
            type === 'tiles' && styles.dragHandleTiles,
          ]}
          activeOpacity={0.7}
          onLongPress={() => {
            handleDragStart(index)
          }}
          delayLongPress={150} // Shorter delay for better responsiveness
        >
          <DragHandleIcon size={14} color="#666666" />
        </TouchableOpacity>
      </View>
    )
  }

  const getItemStyle = (index: number) => [
    styles.draggableItem,
    type === 'tiles' && styles.draggableItemTiles,
    type === 'list' && styles.draggableItemList,
    type === 'customForms' && styles.draggableItemCustomForms,
    draggedOverIndex === index && styles.draggingOver,
    draggingIndex === index && styles.dragging,
    draggingIndex === index && styles.draggingActive,
    type !== 'customForms' && styles.hasOutline,
    highlightedIndex === index && styles.highlighted,
  ]

  const getContainerStyle = () => [
    styles.container,
    flexDirection === 'row' ? styles.row : styles.column,
    type === 'list' && styles.listContainer,
    type === 'customForms' && styles.customFormsContainer,
  ]

  // Helper function to find a valid drop position that doesn't conflict with non-draggable items
  const findValidDropPosition = (targetIndex: number): number => {
    if (!isItemDraggable) {
      return targetIndex // If no callback provided, all positions are valid
    }

    // If the target position is valid (beyond array bounds or at a draggable item position)
    if (targetIndex >= items.length) {
      return targetIndex // End of list is always valid
    }

    // Check if the target position conflicts with a non-draggable item
    if (targetIndex < items.length && !isItemDraggable(items[targetIndex], targetIndex)) {
      // Find the nearest valid position
      let searchForward = targetIndex + 1
      let searchBackward = targetIndex - 1

      // Search forward for a valid position
      while (searchForward < items.length && !isItemDraggable(items[searchForward], searchForward)) {
        searchForward++
      }

      // Search backward for a valid position
      while (searchBackward >= 0 && !isItemDraggable(items[searchBackward], searchBackward)) {
        searchBackward--
      }

      // Choose the nearest valid position
      const forwardDistance = searchForward >= items.length ? Infinity : searchForward - targetIndex
      const backwardDistance = searchBackward < 0 ? Infinity : targetIndex - searchBackward

      if (forwardDistance <= backwardDistance) {
        return searchForward >= items.length ? items.length : searchForward
      } else {
        return searchBackward + 1
      }
    }

    return targetIndex
  }

  return (
    <View style={getContainerStyle()} {...panResponder.panHandlers} ref={containerRef} onLayout={handleContainerLayout}>
      {children?.map((child, index) => {
        const childElement = child as React.ReactElement
        const { style, ...otherProps } = childElement.props

        const dragHandleElement = canAddDragHandle(type, index) ? getDragHandle(index) : null

        return (
          <View style={[getItemStyle(index), style]} key={index}>
            <View
              style={[
                styles.itemContent,
                type === 'customForms' && styles.itemContentCustomForms,
                type !== 'customForms' && styles.itemContentDefault,
                // Hide the original item when dragging
                draggingIndex === index && { opacity: 0.3 },
              ]}
              ref={ref => {
                itemRefs.current[index] = ref
              }}
              onLayout={event => handleItemLayout(event, index)}>
              <TouchableOpacity
                style={{ flex: 1, width: '100%' }}
                onPress={() => {
                  setHighlightedIndex(index)
                  setSelectedIndex?.(index)
                }}
                activeOpacity={0.7}>
                {dragHandleElement}
                {React.cloneElement(childElement, {
                  ...otherProps,
                  style: {
                    width: '100%',
                    flex: 1,
                  },
                })}
              </TouchableOpacity>
            </View>
          </View>
        )
      })}

      {/* Floating dragged item */}
      {draggingIndex !== null && children && children[draggingIndex] && (
        <Animated.View
          style={[
            styles.floatingDraggedItem,
            {
              transform: [
                { translateX: draggedItemPosition.x },
                { translateY: draggedItemPosition.y },
                { scale: draggedItemScale },
              ],
              opacity: draggedItemOpacity,
              width: itemLayouts.current[draggingIndex]?.width || 'auto',
              height: itemLayouts.current[draggingIndex]?.height || 'auto',
            },
          ]}
          pointerEvents="none">
          <View
            style={[
              styles.itemContent,
              type === 'customForms' && styles.itemContentCustomForms,
              type !== 'customForms' && styles.itemContentDefault,
              styles.draggingActive,
            ]}>
            <View style={{ flex: 1, width: '100%' }}>
              {canAddDragHandle(type, draggingIndex) && getDragHandle(draggingIndex)}
              {React.cloneElement(children[draggingIndex] as React.ReactElement, {
                style: {
                  width: '100%',
                  flex: 1,
                },
              })}
            </View>
          </View>
        </Animated.View>
      )}
    </View>
  )
}

interface SeaDraggableView extends ViewProps {
  nonDraggable?: boolean
}

export const SeaDraggableView = ({ children, nonDraggable: _nonDraggable = true, ...props }: SeaDraggableView) => {
  return <View {...props}>{children}</View>
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  column: {
    flexDirection: 'column',
  },
  listContainer: {
    width: '100%',
    marginLeft: -20,
    paddingLeft: 20,
  },
  customFormsContainer: {
    width: '100%',
    alignItems: 'flex-start',
    alignContent: 'flex-start',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  draggableItem: {
    position: 'relative',
    flexDirection: 'row',
  },
  draggableItemTiles: {
    marginBottom: 8,
    width: '100%',
  },
  draggableItemList: {
    paddingVertical: 2,
    paddingHorizontal: 2,
    width: '100%',
  },
  draggableItemCustomForms: {
    marginBottom: 4,
    marginRight: 4,
    flexShrink: 0, // Don't shrink
    alignSelf: 'flex-start', // Don't stretch to fill width
    // Don't set width: '100%' for custom forms - let child determine width
  },
  itemContent: {
    // Base item content styles
  },
  itemContentDefault: {
    flex: 1,
    width: '100%',
  },
  itemContentCustomForms: {
    flex: 1,
    width: '100%',
    // For custom forms, don't use flex: 1 - let child determine width
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  elementWrapper: {
    flex: 1,
  },
  customFormElementWrapper: {
    // Let the child determine its own width but ensure it takes full width of parent
    flexShrink: 0,
    width: '100%',
  },
  dragging: {
    opacity: 0.9,
    zIndex: 999,
    elevation: 8,
  },
  draggingActive: {
    backgroundColor: '#ffffff',
    elevation: 12,
    zIndex: 1000,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    borderRadius: 8,
  },
  floatingDraggedItem: {
    position: 'absolute',
    zIndex: 1001,
    elevation: 15,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 10,
    borderRadius: 8,
  },
  draggingOver: {
    opacity: 0.5,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
    borderRadius: 3,
  },
  hasOutline: {
    // Base outline styles
  },
  highlighted: {},
  nonDraggableItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dragHandleWrapper: {
    // Wrapper for pan responder
  },
  dragHandle: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 24, // Increased touch target for iPad Pro
    height: '100%',
    paddingHorizontal: 4, // Increased padding for better touch response
    minHeight: 44, // Ensure minimum touch target size
  },
  dragHandleList: {
    position: 'relative',
    paddingVertical: 4,
    paddingHorizontal: 1,
    alignItems: 'center',
  },
  dragHandleCustomForms: {
    position: 'absolute',
    top: -10,
    left: -12,
    alignItems: 'center',
    justifyContent: 'center',
    width: 20,
    height: 20,
    borderRadius: 3,
    zIndex: 1000,
  },
  dragHandleTiles: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 1000,
  },
})
