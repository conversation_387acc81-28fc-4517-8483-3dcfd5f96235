import React, { useState } from 'react'
import { View, Text, StyleSheet, Alert } from 'react-native'
import { SimpleSeaDraggable } from './SimpleSeaDraggable'
import { SeaDraggable } from './SeaDraggable'

interface ExampleItem {
  id: string
  title: string
  description: string
}

export const SeaDraggableExample: React.FC = () => {
  const [items, setItems] = useState<ExampleItem[]>([
    { id: '1', title: 'Item 1', description: 'First draggable item' },
    { id: '2', title: 'Item 2', description: 'Second draggable item' },
    { id: '3', title: 'Item 3', description: 'Third draggable item' },
    { id: '4', title: 'Item 4', description: 'Fourth draggable item' },
  ])

  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)

  const handleReorder = (newOrder: unknown[]) => {
    setItems(newOrder as ExampleItem[])
    Alert.alert('Reordered', 'Items have been reordered!')
  }

  const handleDrop = (initialIndex: number, newIndex: number) => {
    console.debug('Item dropped from index:', initialIndex, 'to index:', newIndex)
    Alert.alert('Item Dropped', `Item moved from position ${initialIndex} to position ${newIndex}`)
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>SeaDraggable Example</Text>

      <Text style={styles.subtitle}>Simple Version (Up/Down Buttons)</Text>
      <SimpleSeaDraggable items={items} onReorder={handleReorder}>
        {items.map((item, _index) => (
          <View key={item.id}>
            <Text style={styles.itemTitle}>{item.title}</Text>
            <Text style={styles.itemDescription}>{item.description}</Text>
          </View>
        ))}
      </SimpleSeaDraggable>

      <Text style={styles.subtitle}>Advanced Version (Drag & Drop)</Text>
      <SeaDraggable
        type="list"
        items={items}
        onReorder={handleReorder}
        onDrop={handleDrop}
        selectedIndex={selectedIndex}
        setSelectedIndex={setSelectedIndex}
        flexDirection="column">
        {items.map((item, index) => (
          <View key={item.id} style={[styles.item, selectedIndex === index && styles.selectedItem]}>
            <Text style={styles.itemTitle}>{item.title}</Text>
            <Text style={styles.itemDescription}>{item.description}</Text>
          </View>
        ))}
      </SeaDraggable>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  headerText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  footer: {
    backgroundColor: '#34C759',
    padding: 15,
    borderRadius: 8,
    marginTop: 10,
  },
  footerText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  item: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedItem: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
  },
})
