import React from 'react'
import { View, StyleSheet } from 'react-native'

interface DragHandleIconProps {
  size?: number
  color?: string
}

export const DragHandleIcon: React.FC<DragHandleIconProps> = ({ size = 8, color = '#666666' }) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <View style={[styles.dot, { backgroundColor: color }]} />
      <View style={[styles.dot, { backgroundColor: color }]} />
      <View style={[styles.dot, { backgroundColor: color }]} />
      <View style={[styles.dot, { backgroundColor: color }]} />
      <View style={[styles.dot, { backgroundColor: color }]} />
      <View style={[styles.dot, { backgroundColor: color }]} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 2,
  },
  dot: {
    width: 2,
    height: 2,
    borderRadius: 2,
    margin: 1,
  },
})
