import { View, StyleSheet, ViewStyle, Text, Pressable } from 'react-native'
import React, { useCallback, useMemo } from 'react'
import { SeaTextInput } from '../_inputs/SeaTextInput/SeaTextInput'
import { SeaCheckbox } from '../_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTypography } from '../SeaTypography/SeaTypography'
import { SeaSpacer } from '../SeaSpacer/SeaSpacer'
import { SeaSelectInput } from '../_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions } from '../_inputs/SeaSelectModal/SeaSelectModal'
import { SeaDateTimeInput } from '../_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { makeDateTime } from '@src/lib/datesAndTime'
import { SeaFileUploader } from '../_inputs/SeaFileUploader/SeaFileUploader'
import { SeaYesNoInput } from '../_inputs/SeaYesNoInput/SeaYesNoInput'
import { SeaChecks } from '../_inputs/SeaChecks/SeaChecks'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { getFileNameWithExtension } from '@src/lib/files'
import { MediaCardFile } from '../SeaMediaCard/SeaMediaCard'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { toFloat } from '@src/lib/util'

export enum FormTextSize {
  LARGE = 'large',
  MEDIUM = 'medium',
  SMALL = 'small',
  TINY = 'tiny',
}
export interface CustomFormElementType {
  id: string
  width: string
  name?: string
  label?: string
  type?:
    | 'number'
    | 'search'
    | 'time'
    | 'text'
    | 'tel'
    | 'url'
    | 'email'
    | 'date'
    | 'datetime-local'
    | 'month'
    | 'password'
    | 'week'
    | undefined
  required?: boolean
  requiredError?: string
  description?: string
  options?: string[]
  lines?: number
  heading?: string
  size?: FormTextSize
  text?: string
  content?: string
  isSpecial?: boolean
  n?: number
  o?: number
  allowMultiple?: boolean
  forVesselIds?: string[]
  // vesselOptions?: VesselOptions[];
  height?: number
  error?: string
  value?: any
  initialValue?: any
  help?: any //TODO Add correct type for help
  setValue?: (value: any) => void
  onChange?: (value: any) => void
  onBlur?: (value: any) => void
  onFocus?: (value: any) => void
  onKeyUp?: (value: any) => void
  onEnter?: (value: any) => void
  onLeave?: (value: any) => void
  onInput?: (value: any) => void
}

interface Props {
  element: CustomFormElementType
  mode?: 'edit' | 'complete' | 'view' | 'pdf'
  hasSubmitted?: boolean
  setElement?: (element: CustomFormElementType) => void
  selectedElement?: CustomFormElementType
  onSelectElement?: (selectedElement: CustomFormElementType) => void
  isDraggable?: boolean
}

export const SeaCustomFormElement = ({
  element,
  mode = 'edit',
  hasSubmitted,
  setElement,
  selectedElement,
  onSelectElement,
}: Props) => {
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit'
  const isSelected = element === selectedElement

  const { styles } = useStyles(styleSheet)

  const changeValue = useCallback(
    (newValue: unknown) => {
      if (setElement) {
        setElement({
          ...element,
          value: newValue,
        })
      }
    },
    [element, setElement]
  )

  const onDropdownSelect = useCallback(
    (action: CheckBoxActions, changedValue: string[]) => {
      switch (action) {
        case CheckBoxActions.SELECT:
          changeValue(changedValue[0])
          return
        case CheckBoxActions.DESELECT:
          changeValue('')
          return
        default:
          return
      }
    },
    [changeValue]
  )

  const dropdownOptions = useMemo(() => {
    const data =
      element?.options?.map(option => ({
        label: option,
        value: option,
      })) ?? []

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...data,
    ]
  }, [element])

  const content = useMemo(() => {
    switch (element.id) {
      case 'heading':
        return (
          <SeaTypography
            variant="title"
            textStyle={StyleSheet.flatten(styles[`${element.size ?? 'default'}Title`] ?? styles['defaultTitle'])}>
            {element.heading}
          </SeaTypography>
        )

      case 'text':
        return (
          <SeaTypography
            variant="body"
            textStyle={StyleSheet.flatten(styles[`${element.size ?? 'default'}Text`] ?? styles['defaultText'])}>
            {element.text}
          </SeaTypography>
        )

      case 'line':
        return <View style={{ height: 1, backgroundColor: '#ccc', marginVertical: 10 }} />
      case 'spacer':
        return <SeaSpacer height={toFloat(element.height, 20)} />
      case 'checkbox':
        return (
          <SeaCheckbox
            label={element.label ?? ''}
            value={element.value}
            hasError={!!element.error}
            onChange={val => {
              changeValue(val)
            }}
            disabled={isViewMode || isEditMode}
            preserveLabelCase
          />
        )
      case 'input':
        return (
          <SeaTextInput
            label={element.label ?? undefined}
            value={element.value}
            onChangeText={val => {
              changeValue(val)
            }}
            errorText={element.error}
            hasError={!!element.error}
            preserveLabelCase
            disabled={isViewMode || isEditMode}
          />
        )

      case 'textarea':
        return (
          <SeaTextInput
            label={element.label ?? undefined}
            value={element.value}
            onChangeText={val => {
              changeValue(val)
            }}
            errorText={element.error}
            hasError={!!element.error}
            multiLine
            numberOfLines={element.lines ?? 3}
            preserveLabelCase
            disabled={isViewMode || isEditMode}
            placeholder={isViewMode || isEditMode ? '' : (element.label ?? 'Enter text here')}
          />
        )

      case 'dropdown':
        if (isViewMode || isEditMode) {
          return <DisabledSeaCustomFormElement label={element.label} value={element.value} />
        }

        return (
          <SeaSelectInput
            label={element.label}
            isMulti={false}
            showSelectAllOption={false}
            data={dropdownOptions}
            selectedItemValues={element.value ? [element.value] : []}
            style={{ width: '100%' }}
            onSetItems={onDropdownSelect}
            errorText={element.error}
            hasError={!!element.error}
            preserveLabelCase
            disabled={isViewMode || isEditMode}
          />
        )

      case 'yesno':
        return (
          <SeaYesNoInput
            label={element.label ?? 'Yes / No'}
            value={element.value}
            onChange={val => {
              changeValue(val)
            }}
            hasError={!!element.error}
            disabled={isViewMode || isEditMode}
          />
        )

      case 'checks': {
        const data = element.options?.map((option: string, index: number) => {
          return {
            label: option,
            value: element.value ? element.value[index] : '',
          }
        })
        return (
          <SeaChecks
            label={element.label ?? 'Checks'}
            data={data ?? []}
            disabled={isViewMode || isEditMode}
            setData={data => {
              const newValue = data.map(item => item.value)
              changeValue(newValue)
            }}
          />
        )
      }

      case 'date':
        return (
          <SeaDateTimeInput
            value={makeDateTime(element.value)}
            onChange={date => changeValue(date.toISODate())}
            type={'date'}
            label={element.label ?? 'Date'}
            style={{ flex: 1 }}
            errorText={element.error ?? undefined}
            hasError={!!element.error}
            preserveLabelCase
            disabled={isViewMode || isEditMode}
          />
        )

      case 'datetime':
        return (
          <SeaDateTimeInput
            value={makeDateTime(element.value)}
            onChange={date => changeValue(date.toISO())}
            type={'datetime'}
            label={element.label ?? 'Date'}
            style={{ flex: 1 }}
            errorText={element.error ?? undefined}
            hasError={!!element.error}
            disabled={isViewMode || isEditMode}
          />
        )

      case 'files': {
        if (isViewMode) {
          const uploadedFiles = element?.initialValue?.map((file: any) => ({
            title: getFileNameWithExtension(file.uri),
            file: [file.uri],
            actionButtons: [<SeaDownloadButton key={`download-${file.uri}`} onPress={() => alert('Coming soon!')} />],
          })) as MediaCardFile[]

          return <SeaMedia files={uploadedFiles} title={element.label ?? 'Files'} type="manuals" preserveLabelCase />
        }

        const uploadedFiles = element?.initialValue?.map((file: any) => file.uri ?? '')

        return (
          <SeaFileUploader
            initialFiles={uploadedFiles}
            files={element.value ?? []}
            setFiles={files => changeValue(files)}
            label={element.label ?? 'Upload Files'}
            disabled={isEditMode}
          />
        )
      }
      case 'vessels':
        return isViewMode || isEditMode ? (
          <DisabledSeaCustomFormElement label={element.label} value={element.value} />
        ) : (
          <VesselSelectInput label={element.label} vesselIds={element.value} disabled={isViewMode || isEditMode} />
        )

      case 'crew':
        if (isViewMode || isEditMode) {
          return <DisabledSeaCustomFormElement label={element.label} value={element.value} />
        }

        return (
          <SeaSelectInput
            label={element.label}
            isMulti={false}
            showSelectAllOption={false}
            data={dropdownOptions}
            selectedItemValues={element.value ? [element.value] : []}
            style={{ width: '100%' }}
            onSetItems={onDropdownSelect}
            errorText={element.error}
            hasError={!!element.error}
            preserveLabelCase
            disabled={isViewMode || isEditMode}
          />
        )

      case 'signature':
        return <Text>TODO SIGNATURE</Text>

      default:
        return <></>
    }
  }, [element, styles, isViewMode, isEditMode, dropdownOptions, onDropdownSelect, changeValue])

  return isEditMode && onSelectElement ? (
    <Pressable onPress={() => onSelectElement(element)} style={{ width: '100%' }}>
      <View style={[styles.elementWrapper, isSelected && styles.selectedElement]}>{content}</View>
    </Pressable>
  ) : (
    content
  )
}

interface DisabledSeaCustomFormElementProps {
  label?: string
  value: string
  style?: ViewStyle
  multiline?: boolean
}

export const DisabledSeaCustomFormElement = ({ label, value, style, multiline }: DisabledSeaCustomFormElementProps) => {
  const { styles } = useStyles(styleSheet)

  return (
    <View
      style={{
        width: '100%',
        flex: 1,
      }}>
      {label && <SeaTypography variant={'label'}>{label.toUpperCase()}</SeaTypography>}

      <View
        style={[
          styles.inputContainer,
          multiline ? { height: 100, alignItems: 'flex-start' } : { alignItems: 'center' },
        ]}>
        <SeaTypography
          variant="body"
          containerStyle={{
            width: '100%',
            flex: 1,
          }}>
          {value}
        </SeaTypography>
      </View>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  defaultTitle: {},
  largeTitle: {
    fontSize: 30,
  },
  mediumTitle: {
    fontSize: 24,
  },
  smallTitle: {
    fontSize: 18,
  },
  tinyTitle: {
    fontSize: 14,
  },
  defaultText: {},
  largeText: {
    fontSize: 16,
  },
  mediumText: {
    fontSize: 14,
  },
  smallText: {
    fontSize: 12,
  },
  tinyText: {
    fontSize: 10,
  },
  inputContainer: {
    minHeight: 40,
    borderRadius: 8,
    borderWidth: 1,
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.disabledBackground,
    minWidth: 100,
    paddingHorizontal: 16,
    opacity: 0.5,
  },

  elementWrapper: {
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: 6,
    padding: 4,
  },
  selectedElement: {
    borderColor: theme.colors.primary,
  },
}))
