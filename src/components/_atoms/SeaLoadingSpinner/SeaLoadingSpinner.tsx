import React, { useEffect, useMemo } from 'react'
import { StyleSheet, View } from 'react-native'
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withTiming, Easing } from 'react-native-reanimated'
import wheel from './wheel.png'
import cog from './cog.png'
import propeller from './propeller.png'
import lifeBuoy from './life-buoy.png'

export type SeaLoadingSpinnerProps = {
  variant?: 'wheel' | 'cog' | 'propellor' | 'lifeBuoy'
}

export const SeaLoadingSpinner = ({ variant }: SeaLoadingSpinnerProps) => {
  const rotation = useSharedValue(0)

  const source = useMemo(() => {
    if (variant === 'wheel') return wheel
    if (variant === 'cog') return cog
    if (variant === 'lifeBuoy') return lifeBuoy

    return propeller // Default
  }, [variant])

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 2000,
        easing: Easing.linear,
      }),
      -1 // infinite loop
    )
  }, [])

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        rotate: `-${rotation.value}deg`,
      },
    ],
  }))

  return (
    // <View style={styles.container}>
    <>
      {/*<SeaTypography variant={"title"}>Loading</SeaTypography>*/}
      <View style={styles.container}>
        <Animated.Image source={source} style={[styles.image, animatedStyle]} resizeMode="contain" />
        {/*</View>*/}
      </View>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: 150,
    // height: "100%",
    // backgroundColor: "magenta",
  },
  image: {
    width: 40,
    height: 40,
  },
})
