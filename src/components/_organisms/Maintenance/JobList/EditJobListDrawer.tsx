import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { jobPriorities } from '@src/shared-state/VesselMaintenance/jobs'
import { cleanupStringArray, toFloat } from '@src/lib/util'
import { makeDateTime, subtractInterval } from '@src/lib/datesAndTime'
import { FormikValues, useFormik } from 'formik'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTagsInput } from '@src/components/_atoms/_inputs/SeaTagsInput/SeaTagsInput'
import { SeaSelectCrewOrContact } from '@src/components/_atoms/SeaSelectCrewOrContact/SeaSelectCrewOrContact'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullName } from '@src/shared-state/Core/users'
import { EditSeaEquipment } from '../../SeaEquipment/EditSeaEquipment'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { UpdateJobListDto, UpdateJobListUseCase } from '@src/domain/use-cases/maintenance/UpdateJoblistUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { renderCategoryName } from '@src/lib/categories'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { CreateJobListDto, CreateJobListUseCase } from '@src/domain/use-cases/maintenance/CreateJobListUseCase'
import { SeaCurrencyInput } from '@src/components/_atoms/_inputs/SeaCurrencyInput/SeaCurrencyInput'
import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { Duration } from 'luxon'
import { SeaDurationInput } from '@src/components/_atoms/_inputs/SeaDurationInput/SeaDurationInput'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const validationSchema = Yup.object({
  task: Yup.string().max(500).required(),
  description: Yup.string().max(5000),
  priority: Yup.string().required(),
  assignedTo: Yup.object().nullable(),
  jobTags: Yup.array().of(Yup.string()),
  emailNotifications: Yup.array().of(Yup.string()),
  maintenanceTags: Yup.array().of(Yup.string()),
})

export interface EditJobListDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  jobId?: string
  mode?: DrawerMode
  equipmentId?: string
}

export const EditJobListDrawer = ({
  jobId,
  visible,
  onClose,
  style,
  mode = DrawerMode.Edit,
  equipmentId,
}: EditJobListDrawerProps) => {
  const jobs = sharedState.jobs.use(visible)
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const users = sharedState.users.use(visible)
  const userDetails = sharedState.userDetails.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const vesselSystems = sharedState.vesselSystems.use(visible)

  const user = sharedState.user.current

  // Hooks
  const services = useServiceContainer()
  const [files, setFiles] = useState<SeaFile[]>([])
  const [equipmentData, setEquipmentData] = useState<Partial<Equipment>>({})

  const selectedJob = useMemo(() => {
    if (!jobId || !jobs) {
      return undefined
    }

    return jobs.byId.all[jobId as string]
    // return jobs.byId.all[jobId as string];
  }, [jobId, jobs])

  const initialValues = useMemo(() => {
    return {
      task: selectedJob?.task ?? '',
      description: selectedJob?.description ?? '',
      priority: selectedJob?.priority ?? '',
      jobTags: selectedJob?.tags ?? [],
      assignedTo: selectedJob?.assignedTo ?? {
        userId: '',
        name: '',
      },
      dueDate: selectedJob?.dateDue ?? '',
      emailReminder: selectedJob?.emailReminder ?? '',
      estimatedCost: selectedJob?.estimatedCost ?? 0,
      estimatedTime: selectedJob?.estimatedTime ?? 0,
      maintenanceTags: selectedJob?.maintenanceTags ?? [],
      emailNotifications: [] as string[],
    }
  }, [selectedJob])

  useEffect(() => {
    if (!selectedJob) return

    setEquipmentData({
      id: selectedJob?.equipmentId,
      locationId: selectedJob?.locationId,
    })
  }, [selectedJob])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (values.dateDue && values.emailReminder) {
        const result = subtractInterval(values.dateDue, values.emailReminder)
        dateToRemind = result ? (result.toISODate() ?? undefined) : undefined
      }

      const newMaintenanceTags = values.maintenanceTags.reduce((acc: string[], tag: string) => {
        if (!vessel?.possibleMaintenanceTags?.includes(tag)) {
          acc.push(tag)
        }

        return acc
      }, [] as string[])

      const newTags = values.jobTags.reduce((acc: string[], tag: string) => {
        if (!vessel?.possibleTags?.includes(tag)) {
          acc.push(tag)
        }

        return acc
      }, [] as string[])

      const commonDto = {
        vesselId,
        licenseeId,
        task: values.task,
        description: values.description,
        priority: values.priority,
        assignedTo: {
          userId: values.assignedTo.userId ?? undefined,
          contactId: values.assignedTo.contactId ?? undefined,
          name: values.assignedTo.name ?? undefined,
        },
        tags: values.jobTags ? cleanupStringArray(values.jobTags) : undefined,
        maintenanceTags: values.maintenanceTags ? cleanupStringArray(values.maintenanceTags) : undefined,
        dateDue: values.dueDate ?? undefined,
        equipmentId: equipmentData.id ?? undefined,
        locationId: equipmentData.locationId ?? undefined,
        emailReminder: values.emailReminder ?? undefined,

        dateToRemind,
        files: files,

        estimatedTime: values.estimatedTime,
        newMaintenanceTags,
        newTags,

        jobNum: selectedJob?.jobNum ?? undefined,
        systemName: renderCategoryName(equipmentData.systemId, vesselSystems),
        equipmentName: equipmentData.equipment,
        locationName: renderCategoryName(equipmentData.locationId, vesselSystems),
        isCritical: equipmentData?.isCritical,
        userName: renderFullName(user),
        estimatedCost: toFloat(values.estimatedCost, undefined),
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateJobListDto = {
          ...commonDto,
        }

        const createJobList = services.get(CreateJobListUseCase)

        createJobList
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Job List\n ${err.message}`))
      } else {
        const dto: UpdateJobListDto = {
          ...commonDto,
          id: selectedJob?.id,
        }

        const updateJobList = services.get(UpdateJobListUseCase)

        updateJobList
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Job List\n ${err.message}`))
      }
    },
    [
      vesselId,
      licenseeId,
      userId,
      vessel,
      equipmentData,
      files,
      selectedJob,
      vesselSystems,
      user,
      mode,
      services,
      onClose,
    ]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched, values, dirty, setFieldValue } = formik

  const handleEmailToSelect = useCallback(
    async (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = [...values.emailNotifications]
          newIds.push(changedValue)
          setFieldValue('emailNotifications', newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = values.emailNotifications.filter((id: string) => id !== changedValue)
          setFieldValue('emailNotifications', newIds)
          return
        }

        default:
          return
      }
    },
    [setFieldValue, values.emailNotifications]
  )

  const emailToOptions = useMemo(() => {
    if (!users || !values.priority) return { options: [], userIds: [] }

    const formattedPriority = 'jobs' + values.priority.charAt(1).toUpperCase() + values.priority.slice(2)

    const userIds: string[] = []

    const options = users.all
      .filter(user => {
        const userHasAccess =
          user.id && user.state === 'active' && userDetails?.byId[user.id] && user.vesselIds?.includes(vesselId!)

        return userHasAccess
      })
      .map(user => {
        const details = userDetails?.byId[user.id!]

        let selected = false
        let required = false

        if (
          (details?.emailMe?.includes(formattedPriority) && details?.emailMe.includes('jobsUpdated')) ||
          (!selectedJob && details?.emailMe?.includes('jobsCreated'))
        ) {
          selected = true
          required = true
          userIds.push(user.id!)
          setFieldValue('emailNotifications', [user.id])
        } else if (user.id === values.assignedTo?.userId) {
          selected = true
          userIds.push(user.id!)
          setFieldValue('emailNotifications', [user.id])
        }

        return {
          value: user.id!,
          label: renderFullName(user),
          ...(selected && { selected }),
          ...(required && { required }),
        }
      })

    return { options, userIds }
  }, [users, values.priority, values.assignedTo?.userId, userDetails, vesselId, selectedJob, setFieldValue])

  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Job' : 'Edit Job'}
      visible={visible}
      onClose={onClose}
      style={style}
      isDirty={dirty}
      level={2}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Job'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'Task'}
            showIcon={true}
            value={values.task}
            onChangeText={formik.handleChange('task')}
            hasError={Boolean(errors.task && touched.task)}
            errorText={errors.task}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaTextInput
            label={'Description'}
            showIcon={true}
            value={values.description}
            multiLine={true}
            onChangeText={formik.handleChange('description')}
            hasError={Boolean(errors.description && touched.description)}
            errorText={errors.description}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaDropdown
            label={'Priority'}
            showIcon={true}
            items={Object.keys(jobPriorities).map(priorityId => ({
              label: jobPriorities[priorityId as keyof typeof jobPriorities],
              value: priorityId,
            }))}
            value={values.priority}
            onSelect={value => setFieldValue('priority', value)}
            hasError={Boolean(errors.priority && touched.priority)}
            errorText={errors.priority}
            style={{ flex: 1, width: '100%' }}
          />

          <SeaSelectCrewOrContact
            crew={users?.byVesselId[vesselId!]}
            value={values.assignedTo}
            setValue={value =>
              setFieldValue('assignedTo', {
                userId: value?.userId,
                contactId: value?.contactId,
                name: value?.name,
              })
            }
            label={'TO FIX: Assigned to'}
            style={{ flex: 1, width: '100%' }}
          />
        </DrawerRow>
        <DrawerRow>
          <DrawerColumn>
            <SeaTagsInput
              label={'Job Tags'}
              showIcon={true}
              tags={values?.jobTags ?? []}
              setTags={tags => setFieldValue('jobTags', tags)}
              options={vessel?.possibleTags}
            />
          </DrawerColumn>

          {/* TODO: To save the Email Notification */}
          <SeaSelectInput
            label={'Email Notifications'}
            showIcon={true}
            data={emailToOptions.options}
            selectedItemValues={values.emailNotifications}
            onItemSelect={handleEmailToSelect}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaFileUploader initialFiles={selectedJob?.files} files={files} setFiles={setFiles} />
        </DrawerRow>

        {/* TODO: Edit equipment */}
        <DrawerRow>
          <EditSeaEquipment
            equipmentId={equipmentData.id ?? equipmentId ?? ''}
            locationId={equipmentData.locationId}
            onChange={equipment => setEquipmentData(equipment)}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaTagsInput
            options={vessel?.possibleMaintenanceTags}
            tags={values.maintenanceTags}
            setTags={tags => setFieldValue('maintenanceTags', tags)}
            label={'Maintenance Tags'}
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
          />

          <SeaDateTimeInput
            value={makeDateTime(values.dueDate)}
            onChange={date => setFieldValue('dueDate', date.toISODate())}
            type={'date'}
            label={'Due Date'}
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
          />

          <SeaEmailReminderDropdown
            label="Email Reminder"
            showIcon={true}
            value={values.emailReminder}
            onSelect={value => setFieldValue('emailReminder', value)}
            style={{ flex: 1, width: '100%' }}
          />
        </DrawerRow>

        <DrawerRow>
          <DrawerColumn>
            <SeaCurrencyInput
              label={'Estimated Cost'}
              showIcon={true}
              value={values.estimatedCost}
              currency={'NZD'}
              onChange={value => setFieldValue('estimatedCost', value)}
              hasError={false}
            />
          </DrawerColumn>
          <DrawerColumn>
            <SeaDurationInput
              label="Estimated Time"
              showIcon={true}
              labelIconOptions={{
                icon: 'timer',
              }}
              value={Duration.fromMillis(values.estimatedTime)}
              onChange={duration => setFieldValue('estimatedTime', duration.toMillis())}
              style={{ width: '100%', flex: 1 }}
            />
          </DrawerColumn>
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
