import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import {
  UpdateJobListTaskDto,
  UpdateJobListTaskUseCase,
} from '@src/domain/use-cases/maintenance/UpdateJobListTaskUseCase'
import { formatCurrency, formatDatetime } from '@src/lib/util'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { renderFullName } from '@src/shared-state/Core/users'
import { sharedState } from '@src/shared-state/shared-state'
import { useFormik } from 'formik'
import { DateTime } from 'luxon'
import React, { useCallback, useMemo } from 'react'
import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'

const validationSchema = Yup.object({
  task: Yup.string().max(500).required(),
  description: Yup.string().max(5000),
  priority: Yup.string().required(),
  assignedTo: Yup.object().nullable(),
  dueDate: Yup.date(),
  estimatedCost: Yup.number().min(0),
  estimatedTime: Yup.string(),
  notes: Yup.string().max(5000),
  maintenanceTags: Yup.array().of(Yup.string()),
  jobTags: Yup.array().of(Yup.string()),
})

interface UpdateTaskDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  jobId: string
}
export const UpdateTaskDrawer = ({ visible, onClose, style, jobId }: UpdateTaskDrawerProps) => {
  const jobs = sharedState.jobs.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeSettings = sharedState.licenseeSettings.use(visible)
  const contacts = sharedState.contacts.use(visible)
  const users = sharedState.users.use(visible)
  const equipment = sharedState.equipment.use(visible)
  const userId = sharedState.userId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const user = sharedState.user.current

  const services = useServiceContainer()

  const selectedJob = useMemo(() => {
    if (!jobId || !jobs) {
      return undefined
    }

    return jobs.byId.all[jobId as string]
  }, [jobId, jobs])

  const handleSubmit = useCallback(
    async (values: any) => {
      if (!selectedJob || !userId || !licenseeId) {
        console.error('Missing userId or licenseeId')
        return
      }

      const { notes, ...rest } = values
      const description = `${selectedJob?.description ? selectedJob?.description.trim() + '\n\n' : ''}${notes.trim()}\n(${renderFullName()}, ${formatDatetime(null, ', ')})`

      const dto: UpdateJobListTaskDto = {
        ...rest,
        description,
        userName: renderFullName(user),
      }

      const createJobList = services.get(UpdateJobListTaskUseCase)

      createJobList
        .execute(dto, userId, licenseeId)
        .then(() => onClose())
        .catch(err => console.error(`Error updating Job List Task\n ${err.message}`))
    },
    [selectedJob, userId, licenseeId, user, services, onClose]
  )

  const initialValues = useMemo(() => {
    let assignedToName = ''
    if (selectedJob?.assignedTo?.userId) {
      assignedToName = renderFullName(users?.byId[selectedJob?.assignedTo.userId])
    } else if (selectedJob?.assignedTo?.contactId) {
      assignedToName = `${contacts?.byId[selectedJob?.assignedTo.contactId]?.name} (Contact)`
    }

    let equipmentItem = undefined
    if (selectedJob?.equipmentId && equipment?.byId?.[selectedJob.equipmentId]) {
      equipmentItem = equipment.byId[selectedJob.equipmentId]
    }

    return {
      vesselId,
      id: selectedJob?.id,
      equipmentId: selectedJob?.equipmentId,
      location: selectedJob?.location,
      isCritical: selectedJob?.isCritical ?? false,
      task: selectedJob?.task,
      notes: '',
      files: selectedJob?.files,

      spareParts: selectedJob?.spareParts,
      jobNum: selectedJob?.jobNum,
      actualTime: undefined,
      whenCompleted: DateTime.now().toISO(),

      priority: selectedJob?.priority,
      assignedToName: assignedToName ? assignedToName : undefined,
      tags: selectedJob?.tags ? selectedJob?.tags?.join(', ') : undefined,
      system: equipmentItem?.system ?? undefined,
      equipment: equipmentItem?.equipment ?? undefined,

      dateDue: selectedJob?.dateDue ?? undefined,
      estimatedCost: selectedJob?.estimatedCost
        ? formatCurrency(selectedJob?.estimatedCost, 2, licenseeSettings?.region)
        : undefined,
    }
  }, [selectedJob, equipment, vesselId, licenseeSettings, contacts, users])

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched, dirty } = formik

  return (
    <SeaDrawer
      title={`Update Task: ${selectedJob?.task}`}
      visible={visible}
      onClose={onClose}
      isDirty={dirty}
      style={style}
      level={1}
      primaryAction={
        <SeaButton variant={SeaButtonVariant.Primary} onPress={formik.handleSubmit} label={'Update Task With Notes'} />
      }>
      <SeaStack direction="column" gap={20} style={{ width: '100%' }}>
        <SeaStack
          direction={'column'}
          justify={'center'}
          align={'start'}
          style={{
            width: '100%',
          }}>
          <SeaTextInput
            label={'Notes'}
            multiLine={true}
            value={formik.values.notes ?? ''}
            onChangeText={formik.handleChange('notes')}
          />
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}
