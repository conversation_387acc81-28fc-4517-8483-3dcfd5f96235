import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { EditSeaEquipment } from '@src/components/_organisms/SeaEquipment/EditSeaEquipment'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { DateTime, Duration } from 'luxon'
import { FormikValues, useFormik } from 'formik'
import { SeaTagsInput } from '@src/components/_atoms/_inputs/SeaTagsInput/SeaTagsInput'
import { addInterval, toMillis } from '@src/lib/datesAndTime'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaIntervalDropdown } from '@src/components/_atoms/SeaIntervalDropdown/SeaIntervalDropdown'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { preventMultiTap, toInt } from '@src/lib/util'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  UpdateMaintenanceScheduleDto,
  UpdateMaintenanceScheduleUseCase,
} from '@src/domain/use-cases/maintenance/UpdateMaintenanceScheduleUseCase'
import {
  CreateMaintenanceScheduleDto,
  CreateMaintenanceScheduleUseCase,
} from '@src/domain/use-cases/maintenance/CreateMaintenanceScheduleUseCase'
import { SeaDurationInput } from '@src/components/_atoms/_inputs/SeaDurationInput/SeaDurationInput'
import Yup from '@src/lib/yup'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const validationSchema = Yup.object({
  task: Yup.string().max(500).required(),
  description: Yup.string().max(5000),
  intervalType: Yup.string().required(),
  intervalWeekMonth: Yup.string().when('intervalType', {
    is: (intervalType: string) => intervalType === 'weekMonth' || intervalType === 'weekMonthAndHours',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  intervalEngineHours: Yup.string().when('intervalType', {
    is: (intervalType: string) => intervalType === 'engineHours' || intervalType === 'weekMonthAndHours',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  engineId: Yup.string().when('intervalType', {
    is: (intervalType: string) => intervalType === 'engineHours' || intervalType === 'weekMonthAndHours',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  engineHoursLastService: Yup.string().when('intervalType', {
    is: (intervalType: string) => intervalType === 'engineHours' || intervalType === 'weekMonthAndHours',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  assignedTo: Yup.object().nullable(),
  equipmentId: Yup.string(),
  locationId: Yup.string(),
  isCritical: Yup.boolean(),
  estimatedTime: Yup.string(),
  estimatedCost: Yup.number().min(0),
  maintenanceTags: Yup.array().of(Yup.string()),
})

interface ModifyMaintenanceScheduleDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedScheduleId?: string
  mode: DrawerMode
  equipmentId?: string
}

const ENGINE_INTERVALS_OPTIONS = [
  { value: '', label: 'Not Set' },
  { value: '50', label: '50' },
  { value: '20', label: '20' },
  { value: '100', label: '100' },
  { value: '125', label: '125' },
  { value: '150', label: '150' },
  { value: '200', label: '200' },
  { value: '250', label: '250' },
  { value: '300', label: '300' },
  { value: '350', label: '350' },
  { value: '375', label: '375' },
  { value: '400', label: '400' },
  { value: '450', label: '450' },
  { value: '500', label: '500' },
  { value: '600', label: '600' },
  { value: '700', label: '700' },
  { value: '750', label: '750' },
  { value: '1000', label: '1,000' },
  { value: '1200', label: '1,200' },
  { value: '1250', label: '1,250' },
  { value: '1500', label: '1,500' },
  { value: '2000', label: '2,000' },
  { value: '2500', label: '2,500' },
  { value: '3000', label: '3,000' },
  { value: '4000', label: '4,000' },
  { value: '4500', label: '4,500' },
  { value: '4800', label: '4,800' },
  { value: '5000', label: '5,000' },
  { value: '6000', label: '6,000' },
  { value: '7000', label: '7,000' },
  { value: '7250', label: '7,250' },
  { value: '7500', label: '7,500' },
  { value: '8000', label: '8,000' },
  { value: '9000', label: '9,000' },
  { value: '10000', label: '10,000' },
  { value: '11000', label: '11,000' },
  { value: '12000', label: '12,000' },
  { value: '13000', label: '13,000' },
  { value: '14000', label: '14,000' },
  { value: '14500', label: '14,500' },
  { value: '15000', label: '15,000' },
  { value: '16000', label: '16,000' },
  { value: '17000', label: '17,000' },
  { value: '18000', label: '18,000' },
  { value: '19000', label: '19,000' },
  { value: '20000', label: '20,000' },
  { value: '21000', label: '21,000' },
  { value: '22000', label: '22,000' },
  { value: '23000', label: '23,000' },
  { value: '24000', label: '24,000' },
  { value: '25000', label: '25,000' },
  { value: '30000', label: '30,000' },
  { value: '35000', label: '35,000' },
  { value: '36000', label: '36,000' },
]
/**
 * TODO: Add Links. Work on the Date and Time Component and also Time Component based on the Ionic project. Edit
 * Equipment.
 *
 */
export const ModifyMaintenanceScheduleDrawer = ({
  visible,
  onClose,
  style,
  selectedScheduleId,
  mode,
  equipmentId,
}: ModifyMaintenanceScheduleDrawerProps) => {
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use()

  const licenseeId = sharedState.licenseeId.use()
  const vessel = sharedState.vessel.use()
  const engines = sharedState.engines.use()
  const licenseeSettings = sharedState.licenseeSettings.use()
  const vesselId = sharedState.vesselId.use()
  const userId = sharedState.userId.use()

  const [equipmentData, setEquipmentData] = useState<Partial<Equipment>>({})

  const services = useServiceContainer()

  const selectedSchedule = useMemo(() => {
    if (!selectedScheduleId || !scheduledMaintenanceTasks) {
      return undefined
    }

    return scheduledMaintenanceTasks.byId[selectedScheduleId]
  }, [selectedScheduleId, scheduledMaintenanceTasks])

  const initialValues = useMemo(() => {
    if (selectedSchedule) {
      return {
        task: selectedSchedule.task ? '' + selectedSchedule.task : '',
        description: selectedSchedule.description ? '' + selectedSchedule.description : '',
        intervalType: selectedSchedule.intervalType ? '' + selectedSchedule.intervalType : '',
        intervalWeekMonth: selectedSchedule.intervalWeekMonth ? '' + selectedSchedule.intervalWeekMonth : '',
        whenLastService: selectedSchedule.whenLastService ? selectedSchedule.whenLastService : 0,
        intervalEngineHours: selectedSchedule.intervalEngineHours ? '' + selectedSchedule.intervalEngineHours : '',
        engineHoursLastService:
          selectedSchedule?.engineHoursLastService !== undefined &&
          (selectedSchedule.engineHoursLastService as unknown) !== ''
            ? '' + selectedSchedule.engineHoursLastService
            : '',
        engineId: selectedSchedule.engineId ? '' + selectedSchedule.engineId : '',
        estimatedTime: selectedSchedule.estimatedTime ?? 0,
        maintenanceTags: selectedSchedule?.maintenanceTags ?? [],
      }
    } else {
      return {
        task: '',
        description: '',
        intervalType: 'weekMonth',
        intervalWeekMonth: '',
        whenLastService: DateTime.now(),
        intervalEngineHours: '',
        engineHoursLastService: '',
        engineId: '',
        estimatedTime: 0,
        maintenanceTags: [],
      }
    }
  }, [selectedSchedule])

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (preventMultiTap('maintenanceSchedule') || !equipmentData?.id || !userId || !vesselId || !licenseeId) {
        console.error('Error: Missing required data for submission')
        return
      }

      console.debug(values)

      const newMaintenanceTags = values.maintenanceTags.reduce((acc: string[], tag: string) => {
        if (!vessel?.possibleMaintenanceTags?.includes(tag)) {
          acc.push(tag)
        }

        return acc
      }, [] as string[])

      const useWeekMonth = values.intervalType === 'weekMonth' || values.intervalType === 'weekMonthAndHours'

      const useEngineHours = values.intervalType === 'engineHours' || values.intervalType === 'weekMonthAndHours'

      const commonDto = {
        equipmentId: equipmentData.id,
        locationId: equipmentData.locationId ?? '',
        task: values.task ?? undefined,
        description: values.description ?? undefined,
        maintenanceTags: values.maintenanceTags ?? undefined,
        intervalType: values.intervalType,
        intervalWeekMonth: useWeekMonth ? values.intervalWeekMonth : undefined,
        dateDue:
          useWeekMonth && values.whenLastService
            ? (addInterval(values.whenLastService, values.intervalWeekMonth).toISODate() ?? undefined)
            : undefined,
        intervalEngineHours: useEngineHours ? toInt(values.intervalEngineHours) : undefined,
        engineId: useEngineHours ? values.engineId : undefined,
        engineHoursDue: useEngineHours
          ? toInt(values.engineHoursLastService, 0) + toInt(values.intervalEngineHours, 0)
          : undefined,
        estimatedTime: values.estimatedTime,
        newMaintenanceTags: newMaintenanceTags ?? undefined,
        vesselId,
      }

      if (mode === DrawerMode.Create) {
        const createMaintenanceScheduleDto: CreateMaintenanceScheduleDto = {
          ...commonDto,
          whenLastService: values.whenLastService ? toMillis(values.whenLastService) : 0,
          engineHoursLastService: values.engineHoursLastService ? toInt(values.engineHoursLastService) : undefined,
          updateEngineData:
            values.engineHoursLastService !== undefined &&
            values.engineHoursLastService !== '' &&
            values.engineId &&
            engines?.byId[values.engineId] &&
            toInt(values.engineHoursLastService, 0) > engines.byId[values.engineId].hours,
          engineName: engines?.byId[values.engineId]?.name,
        }

        const createMaintenanceSchedule = services.get(CreateMaintenanceScheduleUseCase)

        createMaintenanceSchedule
          .execute(createMaintenanceScheduleDto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Maintenance Schedule\n ${err.message}`))
      } else {
        const dto: UpdateMaintenanceScheduleDto = {
          ...commonDto,
          id: selectedSchedule?.id ?? '',
        }
        const updateMaintenanceHistory = services.get(UpdateMaintenanceScheduleUseCase)

        updateMaintenanceHistory
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
      }
    },
    [equipmentData, userId, vesselId, licenseeId, mode, vessel, engines, services, onClose, selectedSchedule?.id]
  )

  const engineDropdownItems = useMemo(() => {
    if (engines?.all) {
      return engines.all.map(engine => ({
        label: engine.name,
        value: engine.id,
      }))
    }
    return []
  }, [engines])

  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Maintenance Task' : 'Edit Maintenance Task'}
      visible={visible}
      onClose={onClose}
      isDirty={formik.dirty}
      style={style}
      level={1}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Task'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <EditSeaEquipment
            equipmentId={selectedSchedule?.equipment?.id ?? equipmentId ?? ''}
            locationId={selectedSchedule?.equipment?.locationId}
            onChange={equipment => setEquipmentData(equipment)}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaTextInput
            label={'Task Title'}
            showIcon={true}
            value={formik.values.task}
            onChangeText={formik.handleChange('task')}
            hasError={Boolean(errors.task && touched.task)}
            errorText={errors.task}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaTextInput
            label={'Description'}
            showIcon={true}
            multiLine={true}
            value={formik.values.description}
            onChangeText={formik.handleChange('description')}
            hasError={Boolean(errors.description && touched.description)}
            errorText={errors.description}
          />
        </DrawerRow>

        <DrawerRow>
          {!vessel?.isShoreFacility && (
            <SeaDropdown
              label={'Interval Type'}
              showIcon={true}
              labelIconOptions={{ icon: 'update' }}
              items={[
                { label: 'Week / Month', value: 'weekMonth' },
                { label: 'Hours', value: 'engineHours' },
                { label: 'Week / Month & Hours', value: 'weekMonthAndHours' },
              ]}
              onSelect={value => formik.setFieldValue('intervalType', value)}
              style={{ width: '100%', flex: 1 }}
              value={formik.values.intervalType}
              disabled={!!selectedSchedule}
              hasError={Boolean(errors.intervalType && touched.intervalType)}
              errorText={errors.intervalType}
            />
          )}

          <SeaTagsInput
            label={'Maintenance Tags'}
            showIcon={true}
            tags={formik.values.maintenanceTags}
            setTags={tags => formik.setFieldValue('maintenanceTags', tags)}
            options={vessel?.possibleMaintenanceTags}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {(formik.values.intervalType === 'engineHours' || formik.values.intervalType === 'weekMonthAndHours') && (
          <DrawerRow>
            <SeaDropdown
              value={formik.values.intervalEngineHours}
              showIcon={true}
              items={ENGINE_INTERVALS_OPTIONS}
              onSelect={value => formik.setFieldValue('intervalEngineHours', value)}
              style={{ width: '100%', flex: 1 }}
              label={'Interval Hours'}
              hasError={Boolean(errors.intervalEngineHours && touched.intervalEngineHours)}
              errorText={errors.intervalEngineHours}
            />

            <SeaDropdown
              items={engineDropdownItems}
              onSelect={value => formik.setFieldValue('engineId', value)}
              label="Connect to Engine / Generator"
              showIcon={true}
              style={{ width: '100%', flex: 1 }}
              value={formik.values.engineId}
              hasError={Boolean(errors.engineId && touched.engineId)}
              errorText={errors.engineId}
            />
          </DrawerRow>
        )}

        <DrawerRow>
          {(formik.values.intervalType === 'weekMonth' || formik.values.intervalType === 'weekMonthAndHours') && (
            <SeaIntervalDropdown
              value={formik.values.intervalWeekMonth}
              onSelect={value => formik.setFieldValue('intervalWeekMonth', value)}
              style={{ width: '100%', flex: 1 }}
              initialValue={formik.values.intervalType}
              label={'Interval Week / Month'}
              showIcon={true}
              hasError={Boolean(errors.intervalWeekMonth && touched.intervalWeekMonth)}
              errorText={errors.intervalWeekMonth}
            />
          )}

          <SeaDateTimeInput
            value={DateTime.fromMillis(
              typeof formik.values.whenLastService === 'number'
                ? formik.values.whenLastService
                : Number(formik.values.whenLastService)
            )}
            onChange={value => formik.setFieldValue('whenLastService', value.toMillis())}
            type={'date'}
            label="Last Service"
            showIcon={true}
            disabled={!!selectedSchedule}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {(formik.values.intervalType === 'engineHours' || formik.values.intervalType === 'weekMonthAndHours') && (
          <DrawerRow>
            <SeaTextInput
              label="Engine Hours When Last Serviced"
              showIcon={true}
              value={formik.values.engineHoursLastService}
              onChangeText={formik.handleChange('engineHoursLastService')}
              style={{ width: '100%', flex: 1 }}
              hasError={Boolean(errors.engineHoursLastService && touched.engineHoursLastService)}
              errorText={errors.engineHoursLastService}
            />
            <DrawerColumn>
              {licenseeSettings?.hasMaintenanceTaskTime && (
                <SeaDurationInput
                  label="Estimated Time"
                  showIcon={true}
                  labelIconOptions={{
                    icon: 'timer',
                  }}
                  value={Duration.fromMillis(formik.values.estimatedTime)}
                  onChange={duration => formik.setFieldValue('estimatedTime', duration.toMillis())}
                  style={{ width: '100%', flex: 1 }}
                />
              )}
            </DrawerColumn>
          </DrawerRow>
        )}
      </DrawerContent>
    </SeaDrawer>
  )
}
