import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime, Duration } from 'luxon'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useFormik } from 'formik'
import { addInterval, formatSeaDatetime, toMillis } from '@src/lib/datesAndTime'
import { getEngineName } from '@src/shared-state/Core/vessel'
import {
  CompleteMaintenanceScheduleDto,
  CompleteMaintenanceScheduleUseCase,
} from '@src/domain/use-cases/maintenance/CompleteMaintenanceScheduleUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { toInt } from '@src/lib/util'

import { SeaFile } from '@src/lib/fileImports'
import { SeaDurationInput } from '@src/components/_atoms/_inputs/SeaDurationInput/SeaDurationInput'
import Yup from '@src/lib/yup'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

interface CompleteMaintenanceScheduleDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedScheduleId: string
  editHistory?: boolean
}

const validationSchema = Yup.object({
  task: Yup.string().max(500).required(),
  description: Yup.string().max(5000),
  whenCompleted: Yup.date().required(),
  completedBy: Yup.string().required(),
  notes: Yup.string().max(5000),
  actualTime: Yup.string(),
  maintenanceTags: Yup.array().of(Yup.string()),
})

export function CompleteMaintenanceScheduleDrawer({
  visible,
  onClose,
  style,
  selectedScheduleId,
  editHistory = false,
}: CompleteMaintenanceScheduleDrawerProps) {
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use(visible)
  const engines = sharedState.engines.use(visible)
  const licenseeSettings = sharedState.licenseeSettings.use()
  const vesselId = sharedState.vesselId.use()

  const licenseeId = sharedState.licenseeId.use()
  const userId = sharedState.userId.use()

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const selectedSchedule = useMemo(() => {
    if (!selectedScheduleId || !scheduledMaintenanceTasks) {
      return undefined
    }

    return scheduledMaintenanceTasks.byId[selectedScheduleId]
  }, [selectedScheduleId, scheduledMaintenanceTasks])

  const initialValues = useMemo(() => {
    //TODO: Edit history
    if (editHistory) {
      return {}
      // return {
      //     whenCompleted: historyItemToUpdate.whenCompleted ? formatSeaDatetime(historyItemToUpdate.whenCompleted) : '',
      //     engineHours: historyItemToUpdate.engineHours ? '' + historyItemToUpdate.engineHours : '',
      //     notes: historyItemToUpdate.notes ? '' + historyItemToUpdate.notes : '',
      //     task: historyItemToUpdate.task ? '' + historyItemToUpdate.task : selectedItem?.task ? '' + selectedItem.task : '',
      //     actualTime: historyItemToUpdate.actualTime ? formatShortTimeDurationHrsMinsOnly(historyItemToUpdate.actualTime) : ''
      // };
    } else {
      return {
        whenCompleted: formatSeaDatetime(),
        engineHours: selectedSchedule?.engineId ? engines?.byId[selectedSchedule.engineId]?.hours : '',
        notes: '',
        task: selectedSchedule?.task ? '' + selectedSchedule.task : '',
        actualTime: selectedSchedule?.estimatedTime,
        whenLastService: '',
      }
    }
  }, [editHistory, selectedSchedule, engines])

  const handleSubmit = useCallback(
    (values: any) => {
      if (editHistory) {
        return
      }

      if (!selectedSchedule || !vesselId || !licenseeId || !userId) {
        console.error('Vessel ID, user ID or licensee ID is not defined')
        return
      }

      let whenLastService = toMillis(values.whenCompleted) as number
      let engineHoursLastService = toInt(values.engineHours, undefined)

      if (selectedSchedule?.whenLastService && selectedSchedule.whenLastService > whenLastService) {
        // The most recent history item IS the latest
        whenLastService = selectedSchedule.whenLastService
        engineHoursLastService = selectedSchedule.engineHoursLastService
      }

      const useWeekMonth =
        selectedSchedule?.intervalType === 'weekMonth' || selectedSchedule?.intervalType === 'weekMonthAndHours'
      const useEngineHours =
        selectedSchedule?.intervalType === 'engineHours' || selectedSchedule?.intervalType === 'weekMonthAndHours'

      const dateDue = useWeekMonth
        ? addInterval(whenLastService, selectedSchedule?.intervalWeekMonth).toISODate()
        : undefined
      const engineHoursDue = useEngineHours
        ? Number(engineHoursLastService) + (selectedSchedule?.intervalEngineHours ?? 0)
        : undefined

      const dto: CompleteMaintenanceScheduleDto = {
        vesselId,
        maintenanceTaskId: selectedSchedule.id,
        equipmentName: selectedSchedule.equipment?.equipment ?? '',
        equipmentId: selectedSchedule.equipment?.id,
        location: selectedSchedule.location ?? undefined,
        task: selectedSchedule.task,
        whenCompleted: values.whenCompleted ? toMillis(values.whenCompleted) : undefined,
        files: files,
        notes: values.notes,
        engineHours: values.engineHours ? parseInt(values.engineHours) : 0,
        engineId: selectedSchedule.engineId ?? undefined,
        spareParts: {},
        actualTime: values.actualTime ?? 0,
        updateEngineData:
          selectedSchedule?.engineId &&
          values.engineHours &&
          engines?.byId[selectedSchedule.engineId]?.hours &&
          toInt(values.engineHours) > engines?.byId[selectedSchedule.engineId]?.hours &&
          vesselId,
        engineName: selectedSchedule.engineId ? engines?.byId[selectedSchedule.engineId]?.name : undefined,

        whenLastService: whenLastService ?? undefined,
        engineHoursLastService: engineHoursLastService ?? undefined,
        dateDue: dateDue ?? undefined,
        engineHoursDue,
      }

      const updateMaintenanceHistory = services.get(CompleteMaintenanceScheduleUseCase)

      updateMaintenanceHistory
        .execute(dto, userId, licenseeId)
        .then(() => onClose())
        .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
    },
    [editHistory, selectedSchedule, vesselId, licenseeId, userId, files, engines, services, onClose]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched, dirty } = formik

  return (
    <SeaDrawer
      title={
        editHistory
          ? `TODO: Edit Completed Task: ${selectedSchedule?.task}`
          : `Complete Task: ${selectedSchedule?.task}`
      }
      visible={visible}
      onClose={onClose}
      isDirty={dirty}
      style={style}
      level={1}
      primaryAction={
        !editHistory ? (
          <SeaButton
            variant={SeaButtonVariant.Primary}
            onPress={formik.handleSubmit}
            label={editHistory ? 'Save Changes' : 'Submit'}
          />
        ) : undefined
      }>
      <DrawerContent>
        <DrawerRow>
          <DrawerColumn>
            <SeaDateTimeInput
              value={
                typeof formik.values.whenCompleted === 'string'
                  ? DateTime.fromISO(formik.values.whenCompleted)
                  : (formik.values.whenCompleted ?? DateTime.now())
              }
              label={'When Completed'}
              showIcon={true}
              onChange={value => formik.setFieldValue('whenCompleted', value)}
              type={'date'}
              style={{ width: '100%' }}
            />
          </DrawerColumn>

          <DrawerColumn>
            {licenseeSettings?.hasMaintenanceTaskTime && (
              <SeaDurationInput
                label={'Actual Time'}
                labelIconOptions={{
                  icon: 'timer',
                }}
                showIcon={true}
                value={Duration.fromMillis(formik.values.actualTime ?? 0)}
                onChange={duration => formik.setFieldValue('actualTime', duration.toMillis())}
                style={{ width: '100%', flex: 1 }}
              />
            )}
          </DrawerColumn>
        </DrawerRow>

        {(selectedSchedule?.intervalType === 'engineHours' || selectedSchedule?.intervalType === 'weekMonthAndHours') &&
          selectedSchedule.engineId && (
            <DrawerRow>
              <SeaTextInput
                label={`Hours when completed (${getEngineName(selectedSchedule?.engineId ?? '')})`}
                showIcon={true}
                value={formik.values.whenLastService ?? ''}
                onChangeText={formik.handleChange('whenLastService')}
                keyboardType={'numeric'}
              />
            </DrawerRow>
          )}

        <DrawerRow>
          <SeaTextInput
            label={'Notes'}
            showIcon={true}
            multiLine={true}
            value={formik.values.notes ?? ''}
            onChangeText={formik.handleChange('notes')}
          />
        </DrawerRow>

        <SeaFileUploader initialFiles={selectedSchedule?.files} files={files} setFiles={setFiles} />

        {/* TODO: Add Spareparts */}
      </DrawerContent>
    </SeaDrawer>
  )
}
