import React, { useMemo } from 'react'
import { KeyboardAvoidingView, StyleSheet, ViewStyle } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { ADD_NEW_DROPDOWN_VALUE, AddNewDropdownItem, SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { Contact } from '@src/shared-state/Crew/contacts'
import { sharedState } from '@src/shared-state/shared-state'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateContactUseCase } from '@src/domain/use-cases/crew/CreateContactUseCase'
import { useFormik } from 'formik'
import { UpdateContactUseCase } from '@src/domain/use-cases/crew/UpdateContactUseCase'
import Yup from '@src/lib/yup'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { MaterialIconName } from '@src/types/MaterialIcons'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'

interface ModifyContactDrawerProps {
  visible: boolean
  onClose: () => void
  mode: DrawerMode
  contactId?: string
  style?: ViewStyle
}

const validationSchema = Yup.object({
  name: Yup.string().required('Contact name is required'),
  company: Yup.string().optional(),
  email: Yup.string().email('Invalid email format').optional(),
  number: Yup.string().optional(),
  address: Yup.string().optional(),
  vendorNumber: Yup.string().optional(),
  categoryId: Yup.string().optional(),
  notes: Yup.string().optional(),
  newCategoryName: Yup.string().when('categoryId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a category name'),
    otherwise: schema => schema.optional(),
  }),
})

export const useInitialContactValues = (contact?: Contact) => {
  return useMemo(() => {
    return {
      name: contact?.name ?? '',
      company: contact?.company ?? '',
      email: contact?.email ?? '',
      number: contact?.number ?? '',
      address: contact?.address ?? '',
      vendorNumber: contact?.vendorNumber ?? '',
      categoryId: contact?.categoryId ?? '',
      notes: contact?.notes ?? '',
      newCategoryName: '',
    }
  }, [contact])
}

export const ModifyContactDrawer: React.FC<ModifyContactDrawerProps> = ({
  visible,
  onClose,
  mode,
  contactId,
  style,
}) => {
  const title = useMemo(() => (mode === DrawerMode.Edit ? 'Update Contact' : 'Add Contact'), [mode])

  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const logger = useLogger(`ModifyContactDrawer:${mode}`, { userId, licenseeId })

  // Hooks
  const contacts = sharedState.contacts.use()
  const contactCategories = sharedState.contactCategories.use()

  const { isMobileWidth } = useDeviceWidth()

  const selectedContact = useMemo(() => {
    if (!contactId || !contacts) {
      return undefined
    }

    return contacts.byId[contactId]
  }, [contacts, contactId])

  const initialValues = useInitialContactValues(selectedContact)

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched, dirty } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  // Category options
  const categoryOptions = useMemo(() => {
    if (!contactCategories) return []

    const result = Object.entries(contactCategories.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => ({ label: value.name, value: value.id }))

    return [AddNewDropdownItem, ...result]
  }, [contactCategories])

  const isNewCategoryItem = useMemo(() => values.categoryId === ADD_NEW_DROPDOWN_VALUE, [values.categoryId])
  const serviceContainer = useServiceContainer()

  const executeCreate = (values: typeof initialValues, userId: string, licenseeId: string) => {
    logger.debug('Submitting Create Contact form', { values })

    const createContactUseCase = serviceContainer.get(CreateContactUseCase)

    const dto = {
      name: values.name,
      company: values.company,
      email: values.email,
      number: values.number,
      address: values.address,
      vendorNumber: values.vendorNumber,
      categoryId: values.categoryId === ADD_NEW_DROPDOWN_VALUE ? undefined : values.categoryId,
      notes: values.notes,
      newCategoryName: values.categoryId === ADD_NEW_DROPDOWN_VALUE ? values.newCategoryName : undefined,
    }

    createContactUseCase
      .execute(dto, userId, licenseeId)
      .then(_ => logger.debug('Form submitted successfully'))
      .catch(err => logger.error('Error submitting contact form', err))
  }

  const executeUpdate = (values: typeof initialValues, userId: string, licenseeId: string) => {
    if (!contactId) {
      throw new Error('Cannot update contact without contact ID')
    }
    logger.debug('Submitting Update Contact form', { values })

    const updateContactUseCase = serviceContainer.get(UpdateContactUseCase)

    const dto = {
      contactId: contactId,
      name: values.name,
      company: values.company,
      email: values.email,
      number: values.number,
      address: values.address,
      vendorNumber: values.vendorNumber,
      categoryId: values.categoryId === ADD_NEW_DROPDOWN_VALUE ? undefined : values.categoryId,
      notes: values.notes,
      newCategoryName: values.categoryId === ADD_NEW_DROPDOWN_VALUE ? values.newCategoryName : undefined,
    }

    updateContactUseCase
      .execute(dto, userId, licenseeId)
      .then(_ => logger.debug('Form submitted successfully'))
      .catch(err => logger.error('Error submitting contact form', err))
  }

  const doSubmit = async (values: typeof initialValues) => {
    if (!userId || !licenseeId) {
      throw new Error('Missing Licensee or User')
    }

    mode === DrawerMode.Create ? executeCreate(values, userId, licenseeId) : executeUpdate(values, userId, licenseeId)

    resetForm()
    onClose()
  }

  return (
    <SeaDrawer
      visible={visible}
      onClose={onClose}
      title={title}
      style={style}
      isDirty={dirty}
      primaryAction={
        <SeaButton
          onPress={handleSubmit}
          variant={SeaButtonVariant.Primary}
          label={mode === DrawerMode.Edit ? 'Update' : 'Add'}
          iconOptions={{ icon: mode === DrawerMode.Edit ? ('edit_square' as MaterialIconName) : 'add' }}
        />
      }
      secondaryAction={<SeaButton onPress={onClose} variant={SeaButtonVariant.Tertiary} label={'Cancel'} />}>
      <KeyboardAvoidingView style={styles.container} behavior="padding">
        <SeaStack direction={'column'} align={'start'} gap={10} style={styles.column}>
          {/* Row 1 - Contact Name and Company */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <SeaTextInput
              label="Contact Name"
              value={values.name}
              onChangeText={handleChange('name')}
              hasError={Boolean(errors.name) && touched.name}
              errorText={errors.name}
              style={{ flex: 1, width: '100%' }}
            />
            <SeaTextInput
              label="Company"
              value={values.company}
              onChangeText={handleChange('company')}
              hasError={Boolean(errors.company) && touched.company}
              errorText={errors.company}
              style={{ flex: 1, width: '100%' }}
            />
          </SeaStack>

          {/* Row 2 - Contact Number and Email */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <SeaTextInput
              label="Contact Number"
              value={values.number}
              onChangeText={handleChange('number')}
              hasError={Boolean(errors.number) && touched.number}
              errorText={errors.number}
              keyboardType="phone-pad"
              style={{ flex: 1, width: '100%' }}
            />
            <SeaTextInput
              label="Email"
              value={values.email}
              onChangeText={handleChange('email')}
              hasError={Boolean(errors.email) && touched.email}
              errorText={errors.email}
              keyboardType="email-address"
              autoCapitalize="none"
              style={{ flex: 1, width: '100%' }}
            />
          </SeaStack>

          {/* Row 3 - Address (full width) */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <SeaTextInput
              label="Address"
              value={values.address}
              onChangeText={handleChange('address')}
              hasError={Boolean(errors.address) && touched.address}
              errorText={errors.address}
              style={{ width: '100%' }}
            />
          </SeaStack>

          {/* Row 4 - Vendor Number and Category */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <SeaTextInput
              label="Vendor Number"
              value={values.vendorNumber}
              onChangeText={handleChange('vendorNumber')}
              hasError={Boolean(errors.vendorNumber) && touched.vendorNumber}
              errorText={errors.vendorNumber}
              style={{ flex: 1, width: '100%' }}
            />
            <SeaStack
              direction={'column'}
              gap={5}
              style={{ flex: isMobileWidth ? 1 : 0.5, width: isMobileWidth ? '100%' : '50%' }}>
              <SeaDropdown<string>
                label="Category"
                value={values.categoryId}
                items={categoryOptions}
                onSelect={value => setFieldValue('categoryId', value)}
                hasError={Boolean(errors.categoryId) && touched.categoryId}
                errorText={errors.categoryId}
                style={{ flex: 1, width: '100%' }}
              />
              {/* New Category Name (conditional) */}
              {isNewCategoryItem && (
                <SeaTextInput
                  label="New Category Name"
                  value={values.newCategoryName}
                  onChangeText={handleChange('newCategoryName')}
                  hasError={Boolean(errors.newCategoryName) && touched.newCategoryName}
                  errorText={errors.newCategoryName}
                  style={{ flex: 1, width: '100%' }}
                />
              )}
            </SeaStack>
          </SeaStack>

          {/* Row 5 - Notes (full width) */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <SeaTextInput
              multiLine
              label="Notes"
              value={values.notes}
              onChangeText={handleChange('notes')}
              hasError={Boolean(errors.notes) && touched.notes}
              errorText={errors.notes}
              style={{ width: '100%' }}
            />
          </SeaStack>

          <SeaSpacer height={50} />
        </SeaStack>
      </KeyboardAvoidingView>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  column: {
    padding: 16,
  },
  row: {
    width: '100%',
  },
})
