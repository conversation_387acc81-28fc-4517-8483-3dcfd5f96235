import React, { useEffect, useMemo } from 'react'
import { StyleSheet, useWindowDimensions, View } from 'react-native'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { Contact } from '@src/shared-state/Crew/contacts'
import { CategoriesData } from '@src/lib/categories'
import { formatValue } from '@src/lib/util'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import {
  SeaTableIconEmail,
  SeaTableIconFlag,
  SeaTableIconPerson,
  SeaTableIconPhone,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface ContactsTableProps {
  contacts: Contact[]
  contactCategories: CategoriesData
  onContactSelect: (selectedContactId: string) => void
  style?: React.CSSProperties
}

export const ContactsTable: React.FC<ContactsTableProps> = ({
  contacts,
  contactCategories,
  style,
  onContactSelect,
}) => {
  const { isTabletWidth } = useDeviceWidth()
  const { width } = useWindowDimensions()

  useEffect(() => {
    console.debug('CONTACTS', { contacts, contactCategories })
  }, [contacts, contactCategories])

  const seaTableColumns = useMemo(() => buildColumns(isTabletWidth, width), [contactCategories, isTabletWidth, width])

  const handlePress = (contact: Contact) => {
    onContactSelect(contact.id)
  }

  const seaTableRows = useMemo(() => buildRows(contacts, handlePress), [contacts, handlePress])

  return (
    <View style={[styles.container, style]}>
      <SeaTable
        scrollable
        columns={seaTableColumns}
        rows={seaTableRows}
        style={{
          marginBottom: 50,
        }}
      />
    </View>
  )
}

const buildColumns = (isTabletWidth: boolean, width: number): SeaTableColumn<Contact>[] => {
  return [
    {
      label: 'Company',
      value: x => formatValue(x.company),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Name',
      value: x => x.name,
      icon: x => <SeaTableIconPerson />,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Contact No.',
      value: x => formatValue(x.number),
      icon: x => <SeaTableIconPhone />,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
      isHidden: isTabletWidth && width < 800,
    },
    {
      label: 'Email',
      value: x => formatValue(x.email),
      icon: x => <SeaTableIconEmail />,
      widthPercentage: 0.5,
      compactModeOptions: {
        hideRow: true,
      },
      isHidden: width < 1000,
    },
    // {
    //   label: 'Address',
    //   value: x => formatValue(x.address),
    //   compactModeOptions: {
    //     hideRow: true,
    //   },
    //   isHidden: width < 1200,
    // },
  ]
}

const buildRows = (contacts: Contact[], handlePress: (contact: Contact) => void): SeaTableRow<Contact>[] => {
  return contacts.map(contact => ({
    data: contact,
    onPress: () => handlePress(contact),
  }))
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
})
