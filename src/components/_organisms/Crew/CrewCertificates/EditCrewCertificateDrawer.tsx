import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { FormikValues, useFormik } from 'formik'
import { renderFullName, renderFullNameForUserId } from '@src/shared-state/Core/users'
import { formatSeaDate, subtractInterval } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'

import { makeDateTime } from '@src/lib/util'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import Yup, { notTooOld } from '@src/lib/yup'

import { SeaFile } from '@src/lib/fileImports'
import { CrewCertificate } from '@src/shared-state/Crew/crewCertificates'
import { renderCategoryName } from '@src/lib/categories'
import {
  UpdateCrewCertificateDto,
  UpdateCrewCertificateUseCase,
} from '@src/domain/use-cases/crewCertificates/UpdateCrewCertificateUseCase'
import {
  CreateCrewCertificateDto,
  CreateCrewCertificateUseCase,
} from '@src/domain/use-cases/crewCertificates/CreateCrewCertificateUseCase'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const MISSING_STATE = 'missing'

const validationSchema = Yup.object({
  titleId: Yup.string().max(500).required(),
  heldBy: Yup.string().max(500).required(),
  issuedBy: Yup.string().max(500),
  dateIssued: Yup.date()
    .max(formatSeaDate())
    .required()
    .min(...notTooOld),
  type: Yup.string().max(200).required(),
  dateExpires: Yup.date().when('type', {
    is: 'renewable',
    then: schema => schema.required().min(...notTooOld),
  }),
  emailReminder: Yup.string().when('type', {
    is: 'renewable',
    then: schema => schema.max(200),
  }),
})

export interface EditVesselCertificateDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: CrewCertificate
  mode?: DrawerMode
}
export function EditCrewCertificateDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditVesselCertificateDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const crewCertificateTitles = sharedState.crewCertificateTitles.use(visible)
  const users = sharedState.users.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const certTypeOptions = useMemo(() => {
    return [
      {
        label: `Renewable Certificate`,
        value: 'renewable',
      },
      {
        label: `Non-Expiring Certificate`,
        value: 'nonExpiring',
      },
    ]
  }, [])

  const certificateTitlesOptions = useMemo(() => {
    if (!crewCertificateTitles) return []

    return crewCertificateTitles.ids.map(option => {
      const category = crewCertificateTitles?.byId[option]

      return {
        label: category.name,
        value: category.id,
      }
    })
  }, [crewCertificateTitles])

  const crewMemberOptions = useMemo(() => {
    if (!users) return []

    return users.staff.map(user => ({
      label: renderFullNameForUserId(user.id),
      value: user.id,
    }))
  }, [users])

  const initialValues = useMemo(() => {
    return {
      type: selectedItem?.state && selectedItem?.state !== MISSING_STATE ? selectedItem?.type : 'renewable',
      dateExpires: selectedItem?.dateExpires ?? '',
      emailReminder: selectedItem?.emailReminder ?? '',
      titleId: selectedItem?.titleId ?? '',
      heldBy: selectedItem?.heldBy ?? '',
      issuedBy: selectedItem?.state === MISSING_STATE ? renderFullName() : selectedItem?.issuedBy,
      dateIssued: selectedItem?.state === MISSING_STATE ? formatSeaDate() : selectedItem?.dateIssued,
    }
  }, [selectedItem])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      console.debug(licenseeId, userId)
      if (!licenseeId || !userId) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (values.type === 'renewable' && values.dateExpires && values.emailReminder) {
        dateToRemind = subtractInterval(values.dateExpires, values.emailReminder).toISODate() ?? undefined
      }

      const commonDto = {
        vesselId: vesselId ?? '',
        title: renderCategoryName(values.titleId, crewCertificateTitles),
        titleId: values.titleId,
        issuedBy: values.issuedBy ?? undefined,
        dateIssued: formatSeaDate(values.dateIssued) ?? '',
        dateExpires: formatSeaDate(values.dateExpires) ?? undefined,
        emailReminder: values.emailReminder ?? undefined,
        dateToRemind,
        files: files,
      }

      if (mode === DrawerMode.Edit && selectedItem?.state !== MISSING_STATE) {
        const dto: UpdateCrewCertificateDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateCrewCertificate = services.get(UpdateCrewCertificateUseCase)

        updateCrewCertificate
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Crew Certificate\n ${err.message}`))
      } else {
        const dto: CreateCrewCertificateDto = {
          ...commonDto,
          type: values.type,
          heldBy: values.heldBy,
        }
        const createVesselCertificate = services.get(CreateCrewCertificateUseCase)
        createVesselCertificate
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Vessel Certificate\n ${err.message}`))
      }
    },
    [
      licenseeId,
      userId,
      vesselId,
      crewCertificateTitles,
      files,
      mode,
      selectedItem?.state,
      selectedItem?.id,
      services,
      onClose,
    ]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched, dirty } = formik

  return (
    <SeaDrawer
      title={`${selectedItem?.state === MISSING_STATE ? 'Add Missing' : mode === DrawerMode.Create ? 'Add' : 'Edit'} Certification`}
      visible={visible}
      onClose={onClose}
      style={style}
      isDirty={dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Crew Certificate'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaDropdown
            label={'Certificate Title'}
            showIcon={true}
            items={certificateTitlesOptions}
            style={{ flex: 1, width: '100%' }}
            onSelect={value => formik.setFieldValue('titleId', value)}
            value={formik.values.titleId}
            errorText={errors.titleId}
            hasError={!!errors.titleId}
          />

          <SeaDropdown
            label={'Crew Member'}
            showIcon={true}
            labelIconOptions={{ icon: 'person' }}
            items={crewMemberOptions}
            style={{ flex: 1, width: '100%' }}
            onSelect={value => formik.setFieldValue('heldBy', value)}
            value={formik.values.heldBy}
            disabled={mode === DrawerMode.Edit || selectedItem?.state === MISSING_STATE}
            errorText={errors.heldBy}
            hasError={!!errors.heldBy}
          />
        </DrawerRow>

        <DrawerRow>
          <DrawerColumn>
            <SeaTextInput
              label="Issued By"
              showIcon={true}
              labelIconOptions={{ icon: 'person' }}
              value={formik.values.issuedBy ?? ''}
              onChangeText={formik.handleChange('issuedBy')}
              errorText={errors.issuedBy}
              hasError={!!errors.issuedBy}
            />
          </DrawerColumn>
          <SeaDateTimeInput
            value={makeDateTime(formik.values.dateIssued)}
            onChange={date => formik.setFieldValue('dateIssued', date)}
            type={'date'}
            label="Issue Date"
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
            errorText={errors.dateIssued}
            hasError={!!errors.dateIssued}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaDropdown
            label={'Certificate Type'}
            showIcon={true}
            items={certTypeOptions}
            style={{ flex: 1, width: '100%' }}
            onSelect={value => formik.setFieldValue('type', value)}
            value={formik.values.type}
            disabled={mode === DrawerMode.Edit}
            errorText={errors.type}
            hasError={!!errors.type}
          />
          <DrawerColumn />
        </DrawerRow>

        {formik.values.type.includes('renewable') && (
          <DrawerRow>
            <SeaDateTimeInput
              value={makeDateTime(formik.values.dateExpires)}
              onChange={date => formik.setFieldValue('dateExpires', date)}
              type={'date'}
              label="Expiry Date"
              showIcon={true}
              style={{ flex: 1, width: '100%' }}
              errorText={errors.dateExpires}
              hasError={!!errors.dateExpires}
            />

            <SeaEmailReminderDropdown
              label="Email Reminder"
              showIcon={true}
              value={formik.values.emailReminder}
              onSelect={value => formik.setFieldValue('emailReminder', value)}
              style={{ flex: 1, width: '100%' }}
              errorText={errors.emailReminder}
              hasError={!!errors.emailReminder}
            />
          </DrawerRow>
        )}
        <DrawerRow>
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
