import { CorrectiveAction, CorrectiveActionState } from '@src/shared-state/HealthSafety/correctiveActions'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import Yup from '@src/lib/yup'
import { sharedState } from '@src/shared-state/shared-state'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import React, { useCallback, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent, HALF_WIDTH_DESKTOP } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import {
  CompleteCorrectiveActionDto,
  CompleteCorrectiveActionUseCase,
} from '@src/domain/use-cases/healthSafety/correctiveAction/CompleteCorrectiveActionUseCase'
import { SeaFile } from '@src/lib/fileImports'
import { makeDateTime } from '@src/lib/datesAndTime'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaSectionSubtitle } from '@src/components/_molecules/SeaSectionSubtitle/SeaSectionSubtitle'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'
import useForm from '@src/hooks/useForm'

interface CompleteCorrectiveActionDescriptionProps {
  correctiveAction?: CorrectiveAction
  visible?: boolean
  onClose?: () => void
  mode: DrawerMode
}

const validationSchema = Yup.object({
  notes: Yup.string().min(1).max(5000),
})

export const CompleteCorrectiveActionDescriptionDrawer = ({
  correctiveAction,
  visible = false,
  onClose = () => {},
  mode = DrawerMode.Complete,
}: CompleteCorrectiveActionDescriptionProps) => {
  if (!correctiveAction) return <></>

  // Shared Data
  const user = sharedState.user.use()
  const licenseeId = sharedState.licenseeId.use()

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  // Validation
  const initialValues = useMemo(() => {
    return {
      whenCompleted: correctiveAction.whenCompleted ?? makeDateTime().toMillis(),
      completedNotes: correctiveAction.completedNotes ?? '',
      completedBy: correctiveAction.completedBy ?? '',
    }
  }, [correctiveAction])

  const onSubmit = useCallback(
    async (submittedValues: typeof initialValues) => {
      if (!user?.id || !licenseeId) {
        throw new Error('Missing Licensee or User')
      }
      if (!correctiveAction.vesselIds) {
        throw new Error('Missing Vessels')
      }
      setSavingStateManager({ action: SaveStateAction.SAVING })

      const dto: CompleteCorrectiveActionDto = {
        ...correctiveAction,
        whenCompleted: submittedValues.whenCompleted,
        completedNotes: submittedValues.completedNotes,
        completedBy: submittedValues.completedBy,
        completedFiles: files ?? [],
        state: CorrectiveActionState.Completed,
      }

      const completeCorrectiveActionUseCase = services.get(CompleteCorrectiveActionUseCase)
      completeCorrectiveActionUseCase
        .execute(dto, user.id, licenseeId)
        .then(() => {
          setSavingStateManager({ action: SaveStateAction.SAVED, onCloseTimeout: 1000 })
          onClose()
        })
        .catch(err => {
          setSavingStateManager({ action: SaveStateAction.ERROR })
          console.error(`Error completing Corrective Action\n ${err.message}`)
        })
    },
    [correctiveAction, files, licenseeId, onClose, services, user?.id]
  )

  const { values, handleChange, setFieldValue, errors, handleSubmit, resetForm } = useForm({
    initialValues,
    validationSchema,
    onSubmit,
  })

  const title = mode === DrawerMode.Complete ? 'Complete Corrective Action' : 'Update Completed Information'
  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={''}
          complete={{ onSubmit: handleSubmit }}
          editCompleted={{ onSubmit: handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow width={HALF_WIDTH_DESKTOP}>
          <SeaTextInput label={'Title'} showIcon={true} value={correctiveAction.title} disabled={true} />
        </DrawerRow>
        <DrawerRow width={HALF_WIDTH_DESKTOP}>
          <SeaDateTimeInput
            value={makeDateTime(values.whenCompleted)}
            onChange={date => setFieldValue('whenCompleted', date.toMillis())}
            type={'datetime'}
            label="When Completed"
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
            errorText={errors.whenCompleted}
            hasError={!!errors.whenCompleted}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaTextInput
            label={'Notes'}
            showIcon={true}
            multiLine={true}
            value={values.completedNotes}
            onChangeText={handleChange('completedNotes')}
            errorText={errors.completedNotes}
            hasError={!!errors.completedNotes}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaFileUploader initialFiles={correctiveAction?.completedFiles} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
