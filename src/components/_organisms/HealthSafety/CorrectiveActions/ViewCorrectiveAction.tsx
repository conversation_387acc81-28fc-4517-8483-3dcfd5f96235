import { CorrectiveAction, CorrectiveActionState } from '@src/shared-state/HealthSafety/correctiveActions'
import { sharedState } from '@src/shared-state/shared-state'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import React, { useCallback, useState } from 'react'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { ScrollView } from 'react-native'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { formatDate, formatValue } from '@src/lib/util'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { renderVesselsList } from '@src/shared-state/Core/vessels'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { formatDateShort, formatDatetime, formatEmailReminder } from '@src/lib/datesAndTime'
import { EditCorrectiveActionDrawer } from '@src/components/_organisms/HealthSafety/CorrectiveActions/EditCorrectiveActionDrawer'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'
import { UpdateCorrectiveActionDescriptionDrawer } from '@src/components/_organisms/HealthSafety/CorrectiveActions/UpdateCorrectiveActionDescriptionDrawer'
import { CompleteCorrectiveActionDescriptionDrawer } from '@src/components/_organisms/HealthSafety/CorrectiveActions/CompleteCorrectiveActionDrawer'
import { SeaFileList } from '@src/components/_molecules/SeaMedia/SeaFileList'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  DeleteCorrectiveActionDto,
  DeleteCorrectiveActionUseCase,
} from '@src/domain/use-cases/healthSafety/correctiveAction/DeleteCorrectiveActionUseCase'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

interface ViewCorrectiveActionsProps {
  correctiveAction?: CorrectiveAction
}

const DESKTOP_ITEMS_WIDTH = '100%'

export const ViewCorrectiveAction = ({ correctiveAction }: ViewCorrectiveActionsProps) => {
  if (!correctiveAction) return <></>

  // Shared Data
  const vesselId = sharedState.vesselId.use()
  const vessels = sharedState.vessels.use()
  const divisions = sharedState.divisions.use()
  const user = sharedState.user.use()
  const licenseeId = sharedState.licenseeId.use()

  // Hooks
  const { styles } = useStyles(styleSheet)
  const [editDrawerVisible, setEditDrawerVisible] = useState(false)
  const [updateDrawerVisible, setUpdateDrawerVisible] = useState(false)
  const [completeDrawerVisible, setCompleteDrawerVisible] = useState(false)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const router = useRouter()

  // Services
  const services = useServiceContainer()

  const handleDelete = useCallback(() => {
    if (!correctiveAction || !user.id || !licenseeId) {
      throw new Error('Corrective Action, User ID, or Licensee ID is not available')
    }

    const dto: DeleteCorrectiveActionDto = {
      ...correctiveAction,
    }

    const deleteCorrectiveAction = services.get(DeleteCorrectiveActionUseCase)

    deleteCorrectiveAction
      .execute(dto, user.id, licenseeId)
      .then(() => {
        console.debug('Corrective Action deleted successfully')
        router.navigate({
          pathname: getRoutePath(Routes.CORRECTIVE_ACTION_LIST),
          params: {
            vesselId,
          },
        })
      })
      .catch(err => console.error(`Error deleting Corrective Action\n ${err.message}`))
  }, [correctiveAction, user.id, licenseeId, services, router, vesselId])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  return (
    <ScrollView key={`correctiveAction-${correctiveAction.id}`} style={styles.container}>
      <SeaPageCard
        titleComponent={
          <SeaPageCardTitle
            title={correctiveAction.title}
            additionalElements={
              <SeaTypography variant={'value'}>
                Action #: {formatValue(correctiveAction.correctiveActionNum)}
              </SeaTypography>
            }
            files={correctiveAction.files}
          />
        }
        secondaryActionButton={[
          <RequirePermissions key={'Delete'} role="correctiveActions" level={permissionLevels.FULL}>
            {correctiveAction.state !== CorrectiveActionState.Deleted && <SeaDeleteButton onPress={onDelete} />}
          </RequirePermissions>,
          <RequirePermissions key={'Edit'} role="correctiveActions" level={permissionLevels.EDIT}>
            <SeaEditButton label={'Edit'} onPress={() => setEditDrawerVisible(true)} />
          </RequirePermissions>,
          <>
            {correctiveAction?.state !== CorrectiveActionState.Completed ? (
              <RequirePermissions key={'update-description'} role="correctiveActions" level={permissionLevels.EDIT}>
                <SeaEditButton label={'Note'} onPress={() => setUpdateDrawerVisible(true)} />
              </RequirePermissions>
            ) : (
              <RequirePermissions role="correctiveActions" level={permissionLevels.COMPLETE}>
                <SeaEditButton
                  key={'complete-edit'}
                  label={'Complete'}
                  onPress={() => setCompleteDrawerVisible(true)}
                  variant={SeaButtonVariant.Secondary}
                />
              </RequirePermissions>
            )}
          </>,
        ]}
        primaryActionButton={
          <RequirePermissions role="correctiveActions" level={permissionLevels.COMPLETE}>
            {correctiveAction.state !== CorrectiveActionState.Completed && (
              <SeaCheckButton
                key={'Complete'}
                label={'Complete'}
                onPress={() => setCompleteDrawerVisible(true)}
                variant={SeaButtonVariant.Primary}
              />
            )}
          </RequirePermissions>
        }>
        <SeaPageCardContentSection>
          <SeaStack
            direction={isLargeDesktopWidth ? 'row' : 'column'}
            gap={10}
            justify="start"
            align="start"
            width={'100%'}
            style={{ flex: 1 }}>
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 10}
              align={'start'}
              width={isLargeDesktopWidth ? '70%' : '100%'}>
              {correctiveAction.state === CorrectiveActionState.Deleted && (
                <SeaTypography variant={'label'} color={'red'}>
                  Corrective Action has been marked as deleted.
                </SeaTypography>
              )}

              <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                <SeaLabelValue label={'Description'} value={correctiveAction.description} layout={'vertical'} />
              </SeaStack>
            </SeaStack>
          </SeaStack>
        </SeaPageCardContentSection>
        <SeaEmptyDivider />

        <SeaPageCardContentSection>
          <SeaStack
            direction={isLargeDesktopWidth ? 'row' : 'column'}
            gap={10}
            justify="start"
            align="start"
            width={'100%'}
            style={{ flex: 1 }}>
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 10}
              align={'start'}
              width={isLargeDesktopWidth ? '70%' : '100%'}>
              <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    iconOptions={{ icon: 'directions_boat_filled' }}
                    showIcon={true}
                    label={'Vessel/Facility'}
                    value={renderVesselsList(correctiveAction?.vesselIds, vessels, divisions, '-')}
                  />
                  <SeaLabelValue
                    label={'Assigned To'}
                    iconOptions={{ icon: 'person' }}
                    showIcon={true}
                    value={formatValue(renderFullNameForUserId(correctiveAction?.assignedTo))}
                  />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Tags'}
                    showIcon={true}
                    value={formatValue(correctiveAction?.tags?.join(', '))}
                  />
                  <SeaLabelValue
                    label={'Email Reminder'}
                    iconOptions={{ icon: 'mail' }}
                    showIcon={true}
                    value={formatValue(formatEmailReminder(correctiveAction?.emailReminder))}
                  />
                </SeaStack>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'When Added'}
                    iconOptions={{ icon: 'calendar_month' }}
                    showIcon={true}
                    value={correctiveAction?.whenAdded ? formatDatetime(correctiveAction?.whenAdded, ', ') : '-'}
                  />
                  <SeaLabelValue
                    label={'Added By'}
                    iconOptions={{ icon: 'person' }}
                    showIcon={true}
                    value={
                      correctiveAction?.addedBy ? formatValue(renderFullNameForUserId(correctiveAction.addedBy)) : '-'
                    }
                  />
                </SeaStack>
                <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Due Date'}
                    iconOptions={{ icon: 'calendar_month' }}
                    showIcon={true}
                    value={correctiveAction?.dateDue ? formatDate(correctiveAction?.dateDue) : '-'}
                  />
                </SeaStack>
              </SeaStack>
              <SeaFileList label={'Files'} files={correctiveAction.files} />
            </SeaStack>
          </SeaStack>
        </SeaPageCardContentSection>

        {correctiveAction?.state === CorrectiveActionState.Completed && (
          <>
            <SeaEmptyDivider />
            <ViewCompletedDetails correctiveAction={correctiveAction} />
          </>
        )}
      </SeaPageCard>

      {editDrawerVisible && (
        <EditCorrectiveActionDrawer
          visible={editDrawerVisible}
          correctiveAction={correctiveAction}
          onClose={() => setEditDrawerVisible(false)}
          mode={DrawerMode.Edit}
        />
      )}
      {updateDrawerVisible && (
        <UpdateCorrectiveActionDescriptionDrawer
          visible={updateDrawerVisible}
          correctiveAction={correctiveAction}
          onClose={() => setUpdateDrawerVisible(false)}
          mode={DrawerMode.Edit}
        />
      )}
      {completeDrawerVisible && (
        <CompleteCorrectiveActionDescriptionDrawer
          correctiveAction={correctiveAction}
          visible={completeDrawerVisible}
          onClose={() => setCompleteDrawerVisible(false)}
          mode={
            correctiveAction.state === CorrectiveActionState.Active ? DrawerMode.Complete : DrawerMode.EditCompleted
          }
        />
      )}
    </ScrollView>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
}))

const ViewCompletedDetails = ({ correctiveAction }: { correctiveAction: CorrectiveAction }) => {
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  return (
    <>
      <SeaPageCardContentSection>
        <SeaStack
          direction={isLargeDesktopWidth ? 'row' : 'column'}
          gap={10}
          justify="start"
          align="start"
          width={'100%'}
          style={{ flex: 1 }}>
          <SeaStack
            direction="column"
            gap={isDesktopWidth ? 20 : 10}
            align={'start'}
            width={isLargeDesktopWidth ? '70%' : '100%'}>
            <SeaTypography variant={'subtitle'}>Completed Details</SeaTypography>
            <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                <SeaLabelValue
                  label={'Completed By'}
                  iconOptions={{ icon: 'person' }}
                  showIcon={true}
                  value={formatValue(renderFullNameForUserId(correctiveAction?.completedBy))}
                />
                <SeaLabelValue
                  label={'When Completed'}
                  iconOptions={{ icon: 'calendar_month' }}
                  showIcon={true}
                  value={formatDate(correctiveAction?.whenCompleted)}
                />
              </SeaStack>
            </SeaStack>
            <SeaLabelValue
              label={'Completed Notes'}
              value={formatValue(correctiveAction?.completedNotes) ?? '-'}
              layout={'vertical'}
            />
            <SeaFileList label={'Completed Files'} files={correctiveAction.completedFiles} />
          </SeaStack>
        </SeaStack>
      </SeaPageCardContentSection>
    </>
  )
}
