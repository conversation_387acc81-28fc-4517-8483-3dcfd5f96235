import { Drawer<PERSON><PERSON>, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { CorrectiveAction } from '@src/shared-state/HealthSafety/correctiveActions'
import Yup from '@src/lib/yup'
import { sharedState } from '@src/shared-state/shared-state'
import React, { useCallback, useMemo } from 'react'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useFormik } from 'formik'
import {
  UpdateCorrectiveActionDescriptionDto,
  UpdateCorrectiveActionDescriptionUseCase,
} from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionDescriptionUseCase'
import useForm from '@src/hooks/useForm'
import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'

interface UpdateCorrectiveActionDescriptionProps {
  correctiveAction?: CorrectiveAction
  visible?: boolean
  onClose?: () => void
  mode?: DrawerMode
}

const validationSchema = Yup.object({
  additionalDescription: Yup.string().max(5000),
})

export const UpdateCorrectiveActionDescriptionDrawer = ({
  correctiveAction,
  visible = false,
  onClose = () => {},
  mode = DrawerMode.Edit,
}: UpdateCorrectiveActionDescriptionProps) => {
  if (!correctiveAction) return <></>

  // Shared Data
  const user = sharedState.user.use()
  const licenseeId = sharedState.licenseeId.use()

  // Services
  const services = useServiceContainer()

  // Validation
  const initialValues = useMemo(() => {
    return {
      additionalDescription: '',
    }
  }, [])

  const onSubmit = useCallback(
    async (submittedValues: typeof initialValues) => {
      if (!user?.id || !licenseeId) {
        // This should never happen at this point
        throw new Error('Missing Licensee or User')
      }
      setSavingStateManager({ action: SaveStateAction.SAVING })

      const dto: UpdateCorrectiveActionDescriptionDto = {
        ...correctiveAction,
        id: correctiveAction.id,
        additionalDescription: submittedValues.additionalDescription,
      }

      const updateCorrectiveAction = services.get(UpdateCorrectiveActionDescriptionUseCase)
      updateCorrectiveAction
        .execute(dto, user.id, licenseeId)
        .then(() => {
          setSavingStateManager({ action: SaveStateAction.SAVED, onCloseTimeout: 1000 })
          onClose()
        })
        .catch(err => {
          setSavingStateManager({ action: SaveStateAction.ERROR })
          console.error(`Error updating Corrective Action\n ${err.message}`)
        })
    },
    [correctiveAction, licenseeId, onClose, services, user?.id]
  )

  const { values, handleChange, errors, handleSubmit } = useForm({
    initialValues,
    validationSchema,
    onSubmit,
  })

  return (
    <SeaDrawer
      title={'Add Notes'}
      visible={visible}
      onClose={onClose}
      primaryAction={<DrawerPrimaryAction mode={mode} itemName={'Description'} edit={{ onSubmit: handleSubmit }} />}>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'Notes to Add'}
            showIcon={true}
            multiLine={true}
            value={values.additionalDescription}
            onChangeText={handleChange('additionalDescription')}
            errorText={errors.additionalDescription}
            hasError={!!errors.additionalDescription}
          />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
