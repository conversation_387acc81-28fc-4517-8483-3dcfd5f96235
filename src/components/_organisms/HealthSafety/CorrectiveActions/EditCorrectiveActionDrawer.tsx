import { CorrectiveAction } from '@src/shared-state/HealthSafety/correctiveActions'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useFormik } from 'formik'
import Yup, { notTooOld } from '@src/lib/yup'
import { cleanupStringArray, makeDateTime, preventMultiTap } from '@src/lib/util'
import React, { useCallback, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaFile } from '@src/lib/fileImports'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { CrewSelectInput } from '@src/components/_molecules/CrewSelectInput/CrewSelectInput'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { renderFullName } from '@src/shared-state/Core/users'
import { SeaTagsInput } from '@src/components/_atoms/_inputs/SeaTagsInput/SeaTagsInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions, LabelAndValue } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import {
  CorrectiveActionDto,
  CreateCorrectiveActionUseCase,
} from '@src/domain/use-cases/healthSafety/correctiveAction/CreateCorrectiveActionUseCase'
import { DateTime } from 'luxon'
import { formatSeaDate } from '@src/lib/datesAndTime'
import {
  UpdateCorrectiveActionDto,
  UpdateCorrectiveActionUseCase,
} from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionUseCase'
import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'
import { debounce } from 'lodash'
import useForm from '@src/hooks/useForm'

interface EditCorrectiveActionsProps {
  correctiveAction?: CorrectiveAction
  visible?: boolean
  onClose?: () => void
  mode?: DrawerMode
}

const validationSchema = Yup.object({
  title: Yup.string().max(5000).required(),
  description: Yup.string().max(5000),
  dateDue: Yup.date().when('emailReminder', {
    is: (value: string) => value,
    then: schema => schema.required('This is required for email reminders').min(...notTooOld),
  }),
  emailReminder: Yup.string().max(200),
  vesselIds: Yup.array().of(Yup.string()).min(1, 'At least one vessel must be selected').required(),
})

export const EditCorrectiveActionDrawer = ({
  correctiveAction,
  visible = false,
  onClose = () => {},
  mode = DrawerMode.Edit,
}: EditCorrectiveActionsProps) => {
  if (!correctiveAction) return <></>

  // Shared Data
  const user = sharedState.user.use()
  const users = sharedState.users.use()
  const vesselId = sharedState.vesselId.use()
  const licenseeId = sharedState.licenseeId.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  // Hooks
  const [emailToUserIds, setEmailToUserIds] = useState<string[]>([])
  const [files, setFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  // Validation
  const initialValues = useMemo(() => {
    return {
      title: correctiveAction?.title ?? '',
      description: correctiveAction?.description ?? '',
      dateDue: correctiveAction?.dateDue ?? makeDateTime(''),
      emailReminder: correctiveAction?.emailReminder ?? '',
      assignedTo: correctiveAction?.assignedTo ?? '',
      tags: correctiveAction.tags ?? [],
      vesselIds: correctiveAction.vesselIds ?? [vesselId],
    }
  }, [correctiveAction, vesselId])

  const onSubmit = useCallback(
    async (submittedValues: typeof initialValues) => {
      if (preventMultiTap('correctiveActions')) {
        return
      }
      setSavingStateManager({ action: SaveStateAction.SAVING })

      const dto: CorrectiveActionDto = {
        vesselIds: submittedValues.vesselIds ?? [],
        licenseeId: licenseeId,
        title: submittedValues.title,
        description: submittedValues.description,
        assignedTo: submittedValues.assignedTo ?? undefined,
        tags: cleanupStringArray(submittedValues.tags) ?? [],
        dateDue: submittedValues.dateDue as string,
        emailReminder: submittedValues.emailReminder ?? undefined,
        files,
        state: 'active',
      }

      if (mode === DrawerMode.Create) {
        const createCorrectiveAction = services.get(CreateCorrectiveActionUseCase)
        createCorrectiveAction
          .execute(dto, user.id, licenseeId, emailToUserIds)
          .then(() => {
            setSavingStateManager({ action: SaveStateAction.SAVED, onCloseTimeout: 1000 })
            onClose()
          })
          .catch(err => {
            setSavingStateManager({ action: SaveStateAction.ERROR })
            console.error(`Error creating Corrective Action\n ${err.message}`)
          })
      } else {
        const updateCorrectiveAction = services.get(UpdateCorrectiveActionUseCase)
        const updateDto: UpdateCorrectiveActionDto = {
          ...dto,
          id: correctiveAction.id,
          state: correctiveAction.state,
        }
        updateCorrectiveAction
          .execute(updateDto, user.id, licenseeId, emailToUserIds, correctiveAction.correctiveActionNum)
          .then(() => {
            setSavingStateManager({ action: SaveStateAction.SAVED, onCloseTimeout: 1000 })
            onClose()
          })
          .catch(err => {
            setSavingStateManager({ action: SaveStateAction.ERROR })
            console.error(`Error updating Corrective Action\n ${err.message}`)
          })
      }
    },
    [correctiveAction, emailToUserIds, files, licenseeId, mode, onClose, services, user?.id]
  )

  const { values, handleChange, setFieldValue, handleSubmit, errors } = useForm({
    initialValues,
    validationSchema,
    onSubmit,
  })

  const assignedToOptions = useMemo(() => {
    return users?.all
      .filter(user => user.id && user.state === 'active')
      .map(user => ({
        label: renderFullName(user),
        value: user.id,
      }))
  }, [users])

  // Who can be emailed for this corrective action
  const emailToOptions = useMemo(() => {
    const options: LabelAndValue[] = []
    const userIds: string[] = []

    if (users) {
      users?.all.forEach(user => {
        if (!user.id || user.state !== 'active') {
          return
        }
        // If the user is the assigned to, or the user wants to be notified of this priority then add them to the list
        if (
          user.id === values.assignedTo ||
          user.emailMe?.includes('correctiveActionsUpdated') ||
          (!correctiveAction && user.emailMe?.includes('correctiveActionsCreated'))
        ) {
          userIds.push(user.id)
          options.push({
            label: renderFullName(user),
            value: user.id,
            // Disable the record from being deselected
            disabled: true,
          })
        } else {
          options.push({
            label: renderFullName(user),
            value: user.id,
          })
        }
      })
    }
    setEmailToUserIds(userIds)
    return options
  }, [users, values.assignedTo, correctiveAction])

  const handleEmailToSelect = useCallback(
    async (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = emailToUserIds ?? []
          newIds.push(changedValue)
          setEmailToUserIds(newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = emailToUserIds.filter((id: string) => id !== changedValue)
          setEmailToUserIds(newIds)
          return
        }

        default:
          return
      }
    },
    [emailToUserIds]
  )

  const title = `${mode === DrawerMode.Create ? 'Add' : 'Update'} Corrective Action`
  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={onClose}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Corrective Action'}
          edit={{ onSubmit: handleSubmit }}
          create={{ onSubmit: handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'Title'}
            showIcon={true}
            value={values.title}
            onChangeText={handleChange('title')}
            errorText={errors.title}
            hasError={!!errors.title}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaTextInput
            label={'Description'}
            showIcon={true}
            multiLine={true}
            value={values.description}
            onChangeText={handleChange('description')}
            errorText={errors.description}
            hasError={!!errors.description}
          />
        </DrawerRow>

        <DrawerRow>
          <VesselSelectInput
            showIcon={true}
            vesselIds={values.vesselIds}
            setVesselIds={ids => {
              setFieldValue('vesselIds', ids)
            }}
            isMulti={true}
            errorText={errors.vesselIds}
            hasError={!!errors.vesselIds}
          />

          <SeaDropdown
            items={assignedToOptions}
            value={values.assignedTo}
            onSelect={handleChange('assignedTo')}
            label={'Assigned To'}
            showIcon={true}
            labelIconOptions={{ icon: 'person' }}
            style={{ flex: 1, width: '100%' }}
          />
        </DrawerRow>

        <DrawerRow>
          <DrawerColumn>
            <SeaTagsInput
              label={'Tags'}
              showIcon={true}
              tags={values?.tags ?? []}
              setTags={tags => setFieldValue('tags', tags)}
              options={licenseeSettings?.correctiveActionTags}
            />
          </DrawerColumn>

          <SeaDateTimeInput
            value={DateTime.fromISO(formatSeaDate(values.dateDue ?? ''))}
            onChange={date => setFieldValue('dateDue', date.toISODate())}
            type={'date'}
            label="Date Due"
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
            errorText={errors.dateDue}
            hasError={!!errors.dateDue}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaEmailReminderDropdown
            label="Email Reminder"
            showIcon={true}
            value={values.emailReminder}
            onSelect={value => setFieldValue('emailReminder', value)}
            style={{ flex: 1, width: '100%' }}
            errorText={errors.emailReminder}
            hasError={!!errors.emailReminder}
          />

          <SeaSelectInput
            label={'To Notify'}
            showIcon={true}
            data={emailToOptions}
            selectedItemValues={emailToUserIds}
            showSelectAllOption={false}
            onItemSelect={handleEmailToSelect}
            style={{ flex: 1, width: '100%' }}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaFileUploader initialFiles={correctiveAction?.files} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
