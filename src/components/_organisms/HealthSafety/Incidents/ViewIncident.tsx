import React, { useState } from 'react'
import {
  authoritiesNotified,
  Incident,
  IncidentState,
  IncidentType,
  IncidentWhoInvolvedTypes,
  Injury,
  InjuryOutcomes,
} from '@src/shared-state/HealthSafety/incidents'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { IncidentStatus } from '@src/components/_molecules/IncidentStatus/IncidentStatus'
import { SeaAuthorship } from '@src/components/_atoms/SeaAuthorship/SeaAuthorship'
import { Keyboard, ScrollView, TouchableWithoutFeedback } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { sharedState } from '@src/shared-state/shared-state'
import { formatValue } from '@src/lib/util'
import { renderCategoryName, renderCategoryNames } from '@src/lib/categories'
import { formatDatetime } from '@src/lib/datesAndTime'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { ViewIncidentReview } from '@src/components/_organisms/HealthSafety/Incidents/ViewIncidentReview'
import { canView } from '@src/shared-state/Core/userPermissions'
import { SeaSignatureImage } from '@src/components/_atoms/SeaSignatureImage/SeaSignatureImage'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { EditIncident } from '@src/components/_organisms/HealthSafety/Incidents/EditIncident'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaSectionSubtitle } from '@src/components/_molecules/SeaSectionSubtitle/SeaSectionSubtitle'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { useHealthAndSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'

interface ViewIncidentProps {
  incident?: Incident
}

const DESKTOP_ITEMS_WIDTH = '100%'
export const ViewIncident = ({ incident }: ViewIncidentProps) => {
  // Render
  if (!incident) {
    return <></>
  }

  // Shared Data
  const vessels = sharedState.vessels.use()
  const incidentCategories = sharedState.incidentCategories.use()
  const incidentCauses = sharedState.incidentCauses.use()

  // Hooks
  const { styles } = useStyles(styleSheet)
  const [editIncidentVisible, setEditIncidentVisible] = useState(false)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const vesselId = sharedState.vesselId.use()

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        Keyboard.dismiss()
        console.log('Touch detected')
      }}>
      <ScrollView
        key={`incident-${incident.id}`}
        style={styles.container}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ flexGrow: 1 }}>
        <SeaPageCard
          titleComponent={
            <SeaPageCardTitle
              title={`Report - ${incident.name}`}
              additionalElements={<IncidentStatus status={incident.state} />}
              files={incident?.files}
            />
          }
          secondaryActionButton={[
            <SeaEditButton
              key={'Edit'}
              onPress={() => {
                setEditIncidentVisible(true)
              }}
            />,
            // TODO: Add export pdf
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not implemented yet')} />,
          ]}
          subNav={useHealthAndSafetySubNav(vesselId, Routes.INCIDENT_REPORT)}>
          <SeaPageCardContentSection>
            <SeaAuthorship item={incident} />
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaTypography variant={'subtitle'}>Type</SeaTypography>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'directions_boat_filled' }}
                      showIcon={true}
                      label={'Vessel/Facility'}
                      value={vessels?.byId && incident?.vesselId && vessels.byId[incident?.vesselId]?.name}
                    />
                    <SeaLabelValue label={'Type of Event'} value={IncidentType[incident?.type]} />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Event Category'}
                      value={formatValue(renderCategoryName(incident?.categoryId, incidentCategories))}
                    />
                    <SeaLabelValue label={'Report #'} value={formatValue(incident?.reportNum)} />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaSectionSubtitle>PERSONNEL</SeaSectionSubtitle>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon={true}
                      label={'Types of Personnel Involved'}
                      value={formatValue(
                        incident?.whoInvolvedTypes.map(type => IncidentWhoInvolvedTypes[type]).join(', ')
                      )}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon={true}
                      label={'First Reported By'}
                      value={formatValue(incident.reportedBy)}
                    />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Role in the Event'} value={formatValue(incident.role)} />
                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon={true}
                      label={'Person(s) Involved'}
                      value={formatValue(incident?.whoInvolved)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon={true}
                      label={'Witnesses'}
                      value={formatValue(incident.witnesses)}
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaTypography variant={'subtitle'}>Event</SeaTypography>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon={true}
                      label={'Date & Time'}
                      value={formatDatetime(incident?.whenEvent, ', ')}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'location_on' }}
                      showIcon={true}
                      label={'Event Location'}
                      value={formatValue(incident.location)}
                    />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Environmental Conditions'} value={formatValue(incident.conditions)} />
                    <SeaLabelValue
                      label={'Reported to Authorities?'}
                      value={formatValue(
                        incident?.notifiedAuthorities && authoritiesNotified[incident?.notifiedAuthorities]
                      )}
                    />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Believed causes of this event'}
                      value={formatValue(renderCategoryNames(incident?.causeIds, incidentCauses))}
                    />
                    <SeaLabelValue label={'Damage to Property'} value={formatValue(incident?.propertyDamage)} />
                  </SeaStack>
                </SeaStack>
                <SeaStack
                  isCollapsible={true}
                  width={DESKTOP_ITEMS_WIDTH}
                  direction={'row'}
                  gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue label={'Description'} value={formatValue(incident.description)} layout={'vertical'} />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Initial Action Taken'}
                    value={formatValue(incident.initialActions)}
                    layout={'vertical'}
                  />
                  <SeaLabelValue
                    label={'Corrective Actions Suggested'}
                    value={formatValue(incident.prevention)}
                    layout={'vertical'}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack direction={'column'} align={'start'} gap={10}>
              {IncidentType[incident?.type] === IncidentType.injury &&
                incident?.injuries &&
                incident?.injuries?.length > 0 && <ViewIncidentInjury key={'sss'} injuries={incident?.injuries} />}

              {/** Card 5 - START */}
              {/* TODO: Signature details */}
              {/* TODO: Signature details */}
              {/* TODO: Signature details */}
              <SeaSpacer height={5} />
              <>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaSignatureImage />
                </SeaStack>
                {/*  Edit & Delete Report */}
                <SeaStack direction={'row'} align={'start'}>
                  <SeaDeleteButton
                    variant={SeaButtonVariant.Link}
                    label={'Delete Report'}
                    onPress={() => alert('This functionality is not implemented yet')}
                  />
                </SeaStack>
              </>
            </SeaStack>
          </SeaPageCardContentSection>

          {/** Review Incident */}
          {incident.state !== IncidentState.draft && canView('incidentAccidentMedicalRegister') && (
            <>
              <SeaEmptyDivider />
              <SeaPageCardContentSection>
                <SeaStack
                  direction={isLargeDesktopWidth ? 'row' : 'column'}
                  gap={10}
                  justify="start"
                  align="start"
                  width={'100%'}
                  style={{ flex: 1 }}>
                  <SeaStack
                    direction="column"
                    gap={isDesktopWidth ? 20 : 10}
                    align={'start'}
                    width={isLargeDesktopWidth ? '70%' : '100%'}>
                    <SeaStack
                      direction="column"
                      align={'start'}
                      width={DESKTOP_ITEMS_WIDTH}
                      gap={isDesktopWidth ? 5 : 0}>
                      <ViewIncidentReview incident={incident} itemState={incident.state} itemId={incident.id} />
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
              </SeaPageCardContentSection>
            </>
          )}
        </SeaPageCard>
        {editIncidentVisible && (
          <EditIncident
            incident={incident}
            visible={editIncidentVisible}
            onClose={() => setEditIncidentVisible(false)}
            mode={DrawerMode.Edit}
          />
        )}
      </ScrollView>
    </TouchableWithoutFeedback>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
}))

interface ViewIncidentInjuryProps {
  injuries?: Injury[]
}

export const ViewIncidentInjury = ({ injuries = [] }: ViewIncidentInjuryProps) => {
  // Shared Data
  const injuryTypes = sharedState.injuryTypes.use()
  const injuryLocations = sharedState.injuryLocations.use()

  // Render
  const title = injuries.length <= 1 ? 'Injury' : 'Injuries'

  const renderInjuryDetails = (injury: Injury, index: number) => {
    const injuryCount = injuries.length <= 1 ? '' : ` (${index + 1})`

    return (
      <>
        <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
          <SeaLabelValue label={`Person Injured${injuryCount}`} value={formatValue(injury?.whoInjured)} />
          <SeaLabelValue label={'Type'} value={formatValue(renderCategoryNames(injury?.typeIds, injuryTypes))} />
        </SeaStack>
        <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
          <SeaLabelValue
            label={'Location'}
            value={formatValue(renderCategoryNames(injury?.locationIds, injuryLocations))}
          />
          <SeaLabelValue label={'Outcome'} value={formatValue(InjuryOutcomes[injury?.outcome])} />
        </SeaStack>
      </>
    )
  }

  return (
    <>
      <SeaTypography variant={'subtitle'}>{title}</SeaTypography>
      {injuries.map((injury: Injury, index: number) => renderInjuryDetails(injury, index))}
    </>
  )
}
