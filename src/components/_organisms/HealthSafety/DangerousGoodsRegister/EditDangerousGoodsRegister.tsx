import React, { useMemo, useState } from 'react'
import { DangerousGood } from '@src/shared-state/HealthSafety/dangerousGoods'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { preventMultiTap } from '@src/lib/util'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useFormik } from 'formik'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { DateTime } from 'luxon'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { sharedState } from '@src/shared-state/shared-state'
import Yup, { notTooOld } from '@src/lib/yup'
import {
  CreateDangerousGoodDto,
  CreateDangerousGoodsUseCase,
  DangerousGoodDto,
} from '@src/domain/use-cases/dangerousGoods/CreateDangerousGoodsUseCase'
import {
  UpdateDangerousGoodDto,
  UpdateDangerousGoodsUseCase,
} from '@src/domain/use-cases/dangerousGoods/UpdateDangerousGoodsUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { formatSeaDate } from '@src/lib/datesAndTime'
import { SeaFile } from '@src/lib/fileImports'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

interface EditDangerousGoodsRegisterProps {
  visible?: boolean
  dangerousGood?: DangerousGood
  onClose?: () => void
  mode?: DrawerMode
}

const validationSchema = Yup.object({
  name: Yup.string().max(500).required(),
  quantity: Yup.string().max(200),
  location: Yup.string().max(500),
  class: Yup.string().max(500),
  isHazardous: Yup.boolean().required(),
  dateExpires: Yup.date().when('type', {
    is: 'renewable',
    then: schema => schema.required().min(...notTooOld),
  }),
})

export const EditDangerousGoodsRegister = ({
  visible = false,
  mode = DrawerMode.Edit,
  dangerousGood,
  onClose = () => {},
}: EditDangerousGoodsRegisterProps) => {
  if (!dangerousGood) {
    return <></>
  }

  const isNew = mode === DrawerMode.Create

  // Shared Data
  const user = sharedState.user.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [vesselIds, setVesselIds] = useState<string[]>(dangerousGood.vesselIds)
  const [imageFiles, setImageFiles] = useState<SeaFile[]>([])
  const [msdsFiles, setMsdsFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      name: dangerousGood.name,
      quantity: dangerousGood.quantity,
      location: dangerousGood?.location,
      isHazardous: dangerousGood.isHazardous,
      class: dangerousGood.class,
      dateExpires: dangerousGood?.dateExpires,
      imageFiles: dangerousGood.imageFiles,
      msdsFiles: dangerousGood.msdsFiles,
    }
  }, [dangerousGood])

  const onSubmit = (values: typeof initialValues) => {
    if (preventMultiTap('dangerousGood')) {
      return
    }

    // TODO: Cater for:
    //  - signature

    const dangerousGoodDto: DangerousGoodDto = {
      vesselIds: vesselIds,
      name: values.name,
      quantity: values.quantity,
      location: values.location,
      isHazardous: values.isHazardous,
      class: values.class,
      dateExpires: values.dateExpires,
      imageFiles: imageFiles,
      msdsFiles: msdsFiles,
    }

    if (isNew) {
      /** Create New Dangerous Good */
      const createDangerousGoodDto: CreateDangerousGoodDto = {
        ...dangerousGoodDto,
        state: 'active',
      }

      const createDangerousGoodUseCase = services.get(CreateDangerousGoodsUseCase)
      createDangerousGoodUseCase
        .execute(createDangerousGoodDto, user?.id, licenseeId)
        .then(() => {
          alert('Dangerous Good created successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    } else {
      /** Update Dangerous Good */
      const updateDangerousGoodDto: UpdateDangerousGoodDto = {
        ...dangerousGoodDto,
        id: dangerousGood.id,
        state: dangerousGood.state,
      }

      const updateDangerousGoodUseCase = services.get(UpdateDangerousGoodsUseCase)
      updateDangerousGoodUseCase
        .execute(updateDangerousGoodDto, user?.id, licenseeId)
        .then(() => {
          alert('Dangerous Good Updated successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    }
  }

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => onSubmit(values),
  })

  const { errors, touched, dirty } = formik

  return (
    <SeaDrawer
      title={isNew ? 'Create Dangerous Good' : `Edit - ${dangerousGood.name}`}
      visible={visible}
      isDirty={dirty}
      onClose={() => onClose()}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Dangerous Good'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        {/** Row 1 */}
        <DrawerRow>
          <VesselSelectInput showIcon={true} vesselIds={vesselIds} setVesselIds={setVesselIds} />

          <DrawerColumn>
            <SeaTextInput
              label={'Chemical Name'}
              showIcon={true}
              value={formik.values.name}
              onChangeText={formik.handleChange('name')}
              hasError={Boolean(errors.name)}
              errorText={errors.name}
            />
          </DrawerColumn>
        </DrawerRow>

        {/** Row 2 */}
        <DrawerRow>
          <DrawerColumn>
            <SeaTextInput
              label={'Quantity'}
              showIcon={true}
              value={formik.values.quantity}
              onChangeText={formik.handleChange('quantity')}
            />
          </DrawerColumn>

          <DrawerColumn>
            <SeaTextInput
              label={'Location'}
              showIcon={true}
              labelIconOptions={{ icon: 'location_on' }}
              value={formik.values.location}
              onChangeText={formik.handleChange('location')}
            />
          </DrawerColumn>
        </DrawerRow>

        {/** Row 3 */}
        <DrawerRow>
          <DrawerColumn>
            <SeaDropdown
              label={'Hazardous Substance'}
              showIcon={true}
              value={formik.values.isHazardous}
              items={[
                { label: 'Not Set', value: undefined },
                { label: 'Yes', value: true },
                { label: 'No', value: false },
              ]}
              hasError={Boolean(errors.isHazardous)}
              errorText={errors.isHazardous}
              onSelect={value => formik.setFieldValue('isHazardous', value)}
              style={{ flex: 1, width: '100%' }}
            />
          </DrawerColumn>

          <DrawerColumn>
            <SeaTextInput
              label={'DG Class'}
              showIcon={true}
              value={formik.values.class}
              onChangeText={formik.handleChange('class')}
            />
          </DrawerColumn>
        </DrawerRow>

        {/** Row 4 */}
        <DrawerRow>
          <DrawerColumn>
            <SeaDateTimeInput
              label={'Renewal Date'}
              showIcon={true}
              value={DateTime.fromISO(formatSeaDate(formik.values.dateExpires ?? ''))}
              onChange={(date: DateTime) => {
                formik.setFieldValue('dateExpires', date)
              }}
              type={'date'}
              style={{ flex: 1, width: '100%' }}
            />
          </DrawerColumn>
          <DrawerColumn />
        </DrawerRow>

        {/** Row 5 */}
        <DrawerRow>
          <SeaFileUploader
            label={'Image / Files'}
            initialFiles={dangerousGood.imageFiles}
            files={imageFiles}
            setFiles={setImageFiles}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaFileUploader
            label={'SDS Document'}
            initialFiles={dangerousGood.msdsFiles}
            files={msdsFiles}
            setFiles={setMsdsFiles}
          />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
