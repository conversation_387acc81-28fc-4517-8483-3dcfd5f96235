import React, { useMemo, useState } from 'react'
import { SafetyMeetingReport } from '@src/shared-state/HealthSafety/safetyMeetingReports'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { StyleSheet } from 'react-native'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { useFormik } from 'formik'
import { formatVessels, preventMultiTap } from '@src/lib/util'
import { makeDateTime, toMillis } from '@src/lib/datesAndTime'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { PersonnelPresentSelectInput } from '@src/components/_molecules/PersonnelPresentSelectInput/PersonnelPresentSelectInput'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import Yup, { notTooOld } from '@src/lib/yup'
import {
  CreateSafetyMeetingReportDto,
  CreateSafetyMeetingReportUseCase,
  SafetyMeetingReportDto,
} from '@src/domain/use-cases/safetyMeetingReports/CreateSafetyMeetingReportUseCase'
import {
  UpdateSafetyMeetingReportDto,
  UpdateSafetyMeetingReportUseCase,
} from '@src/domain/use-cases/safetyMeetingReports/UpdateSafetyMeetingReportUseCase.ts'
import { sharedState } from '@src/shared-state/shared-state'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { DateTime } from 'luxon'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'

import { SeaFile } from '@src/lib/fileImports'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { HALF_WIDTH_DESKTOP, DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

interface EditHealthSafetyMeetingsProps {
  visible?: boolean
  mode?: DrawerMode
  safetyMeetingReport?: SafetyMeetingReport
  onClose?: () => void
}

const validationSchema = Yup.object({
  type: Yup.string(),
  whenMeeting: Yup.date()
    .required()
    .min(...notTooOld),
  notes: Yup.string().max(10000),
  sendToCrew: Yup.boolean(),
  // vesselIds: Yup.array().of(Yup.string()).required(),
})

export const EditHealthSafetyMeetings = ({
  visible = false,
  mode = DrawerMode.Edit,
  safetyMeetingReport,
  onClose = () => {},
}: EditHealthSafetyMeetingsProps) => {
  if (!safetyMeetingReport) {
    return <></>
  }

  const isNew = mode === DrawerMode.Create

  // Shared Data
  const users = sharedState.users.use(visible)
  const user = sharedState.user.use(visible)
  const vessels = sharedState.vessels.use()
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [vesselIds, setVesselIds] = useState<string[]>(safetyMeetingReport.vesselIds)
  const [personnelPresentIds, setPersonnelPresentIds] = useState<string[]>(
    safetyMeetingReport.personnelPresentIds ?? []
  )
  const [files, setFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      whenMeeting: safetyMeetingReport.whenMeeting,
      type: safetyMeetingReport.type ?? '',
      notes: safetyMeetingReport.notes ?? '',
      sendToCrew: safetyMeetingReport.sendToCrew ?? false,
      signature: safetyMeetingReport.signature,
    }
  }, [safetyMeetingReport])

  const onSubmit = (values: typeof initialValues) => {
    if (preventMultiTap('healthSafetyMeeting')) {
      return
    }

    // TODO: Cater for:
    //  - signature

    const filteredPersonnelIds = personnelPresentIds.filter(id => {
      return users?.byId[id] && users.byId[id].state === 'active'
    })

    const safetyMeetingReportDto: SafetyMeetingReportDto = {
      vesselIds: vesselIds,
      jobIds: safetyMeetingReport.jobIds ?? [],
      notes: values.notes,
      personnelPresentIds: filteredPersonnelIds,
      personnelPresentNames: filteredPersonnelIds.map(id => {
        return renderFullNameForUserId(id)
      }),
      sendToCrew: values.sendToCrew ?? undefined,
      // TODO: Signature
      signature: values.signature ?? '',
      state: safetyMeetingReport.state,
      type: values.type,
      whenMeeting: toMillis(values.whenMeeting) ?? '',
      files: files,
    }

    if (isNew) {
      /** Create New Safety Meeting Report */
      const createSafetyMeetingReportDto: CreateSafetyMeetingReportDto = {
        ...safetyMeetingReportDto,
        state: 'active',
      }

      const createSafetyMeetingReportUseCase = services.get(CreateSafetyMeetingReportUseCase)
      createSafetyMeetingReportUseCase
        .execute(createSafetyMeetingReportDto, user?.id, licenseeId)
        .then(() => {
          alert('Safety Meeting Report created successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    } else {
      /** Update Safety Meeting Report */
      const updateSafetyMeetingReportDto: UpdateSafetyMeetingReportDto = {
        ...safetyMeetingReportDto,
        id: safetyMeetingReport.id,
        state: safetyMeetingReport.state,
      }

      const updateSafetyMeetingReportUseCase = services.get(UpdateSafetyMeetingReportUseCase)
      updateSafetyMeetingReportUseCase
        .execute(updateSafetyMeetingReportDto, user?.id, licenseeId)
        .then(() => {
          alert('Safety Meeting Report Updated successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    }
  }

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => onSubmit(values),
  })

  const { errors, touched, dirty } = formik

  return (
    <SeaDrawer
      title={`${isNew ? 'Create' : 'Edit'} Meeting Report`}
      visible={visible}
      onClose={onClose}
      isDirty={dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Meeting Report'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        {/** Row 1 */}
        <>
          {/* This is intentional because Vessel Information is not allowed to be updated after creation */}
          {isNew && <VesselSelectInput showIcon={true} vesselIds={vesselIds} setVesselIds={setVesselIds} />}

          {!isNew && (
            <SeaLabelValue
              label={'Vessel/Facility'}
              iconOptions={{ icon: 'directions_boat_filled' }}
              showIcon={true}
              value={formatVessels(safetyMeetingReport?.vesselIds, vessels)}
              layout={'vertical'}
            />
          )}
        </>

        {/** Row 2 */}
        <DrawerRow>
          <SeaDateTimeInput
            showIcon={true}
            value={makeDateTime(formik.values.whenMeeting)}
            onChange={(date: DateTime) => {
              formik.setFieldValue('whenMeeting', date)
            }}
            type={'datetime'}
            style={styles.column}
          />

          <SeaDropdown
            label={'Type'}
            showIcon={true}
            value={formik.values.type}
            items={[
              { label: 'Not Set', value: 'Not Set' },
              { label: 'Vessel', value: 'Vessel' },
              { label: 'Shore Facility', value: 'Shore Facility' },
              { label: 'Company', value: 'Company' },
              { label: 'Shared Duties', value: 'Shared Duties' },
            ]}
            onSelect={value => formik.setFieldValue('type', value)}
            style={styles.column}
          />
        </DrawerRow>

        {/** Row 2 */}
        <DrawerRow width={HALF_WIDTH_DESKTOP}>
          <PersonnelPresentSelectInput
            showIcon={true}
            vesselIds={vesselIds}
            personnelPresentIds={personnelPresentIds}
            setPersonnelPresentIds={setPersonnelPresentIds}
            style={styles.column}
          />
        </DrawerRow>

        {/** Row 3 */}
        <DrawerRow>
          <SeaTextInput
            label={'Meeting Notes'}
            showIcon={true}
            multiLine={true}
            value={formik.values.notes}
            onChangeText={formik.handleChange('notes')}
          />
        </DrawerRow>

        {/** Row 4 */}
        <DrawerRow>
          <SeaFileUploader initialFiles={safetyMeetingReport.files} files={files} setFiles={setFiles} />
        </DrawerRow>

        {/** Row 5 */}
        <DrawerRow>
          <SeaCheckbox
            label={'Send meeting notes to crew'}
            showIcon={true}
            value={formik.values.sendToCrew}
            onChange={formik.setFieldValue}
          />
        </DrawerRow>

        {/** Row 6 */}
        <DrawerRow>
          {/* TODO: Setup File Uploader */}
          <SeaLabelValue label={'TODO: Signature'} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  column: {
    flex: 1,
    width: '100%',
  },
})
