import { useCallback, useEffect, useMemo, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams } from 'expo-router'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { formatDatetime } from '@src/lib/datesAndTime'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { View } from 'react-native'
import { FormBuilder } from './FormBuilder'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { makeSeaFiles } from '@src/lib/files'
import { formArrayToElements, formElementsToArray } from '@src/lib/customForms'
import { haveObjectsChanged } from '@src/lib/util'
import {
  UpdateCustomFormDto,
  UpdateCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/UpdateCustomFormUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'
import { seaFilesToValue } from '@src/lib/fileImports'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { EditCustomFormDrawer } from './EditCustomFormDrawer'

export function EditWithFormBuilder() {
  const customForms = sharedState.customForms.use()
  const customFormCategories = sharedState.customFormCategories.use()
  const customFormVersions = sharedState.customFormVersions.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const [formElements, setFormElements] = useState<CustomFormElementType[]>([])
  const [formElementsOriginal, setFormElementsOriginal] = useState<CustomFormElementType[]>([])
  const [historyElementN, setHistoryElementN] = useState(0)
  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)

  const { formId } = useGlobalSearchParams()
  const { styles, theme } = useStyles(styleSheet)
  const { isDesktopWidth } = useDeviceWidth()
  const services = useServiceContainer()

  const selectedItem = useMemo(() => {
    if (!customForms || !formId) return undefined

    return customForms.byId[Array.isArray(formId) ? formId[0] : formId]
  }, [formId, customForms])

  const selectedCategory = useMemo(() => {
    if (!selectedItem || !customFormCategories) return undefined
    return customFormCategories.byId[selectedItem.categoryId].name
  }, [selectedItem, customFormCategories])

  const handleSave = useCallback(() => {
    if (!selectedItem || !userId || !licenseeId || !formElements || formElements.length === 0) {
      console.error('Selected item, userId, licenseeId or formElements are not defined')
      return
    }

    if (!haveObjectsChanged(formElements, formElementsOriginal)) {
      alert('No changes made to the form elements.')
      //TODO Show the error in a more user-friendly way
      return
    }

    let error: string | undefined = undefined

    formElements.forEach(element => {
      if (element.isSpecial) {
        if (element.id === 'vessels') {
          if (element.forVesselIds && element.forVesselIds.length === 0) {
            error = 'You must allow at least one vessel or facility'
          }
        }
      }
      if (element?.help?.files) {
        element.help.files = seaFilesToValue(element.help.files)
      }
    })

    if (error) {
      alert(error)
      //TODO Show the error in a more user-friendly way
      return
    }

    const whenNewVersion = Date.now()
    const _vesselsElement = formElements.find(element => element.isSpecial && element.id === 'vessels')
    const crewElement = formElements.find(element => element.isSpecial && element.id === 'crew')

    const _formElements = formElements.filter(
      element => !element.isSpecial || (element.isSpecial && element.id !== 'vessels' && element.id !== 'crew')
    )

    const { forVesselIds, ...vesselsElement } = _vesselsElement ?? {}

    const dto: UpdateCustomFormDto = {
      customFormId: selectedItem.id,
      title: selectedItem.title,
      version: whenNewVersion,
      forVesselIds: forVesselIds?.length ? forVesselIds : undefined,
      forVessels: selectedItem.forVesselIds?.length > 0,
      forCrew: selectedItem.forCrew,
      categoryId: selectedItem.categoryId,
      vesselsElement,
      crewElement,
      form: formArrayToElements(_formElements),
      historyElementN,
      formIsDraft: selectedItem.state === 'draft',
    }

    const updateCustomForm = services.get(UpdateCustomFormUseCase)

    updateCustomForm
      .execute(dto, userId, licenseeId)
      .then(() => {
        setSavingStateManager({
          action: SaveStateAction.SAVED,
          onCloseTimeout: 1000,
        })
      })
      .catch(err => {
        setSavingStateManager({
          action: SaveStateAction.ERROR,
          onCloseTimeout: 1000,
          message: `Error submitting form: ${err.message}`,
        })
        console.error(`Error submitting form\n ${err.message}`)
      })
  }, [formElements, formElementsOriginal, selectedItem, historyElementN, userId, licenseeId, services])

  useEffect(() => {
    if (!selectedItem || !customFormVersions) return

    const version = customFormVersions?.byFormIdAndVersion?.[selectedItem.id]?.[selectedItem.latestVersion]
    let _formElements = [] as CustomFormElementType[]

    const existingForm = formElementsToArray(version?.form)
    if (selectedItem.forVesselIds?.length > 0 && !selectedItem.forVesselIds.includes('none')) {
      _formElements.push({
        isSpecial: true,
        id: 'vessels',
        n: -2,
        o: -2,
        label: selectedItem.vesselsElement?.label ?? 'SELECT VESSELS / FACILITIES',
        width: (selectedItem.vesselsElement?.width ?? 100).toString(),
        allowMultiple: selectedItem.vesselsElement?.allowMultiple ?? true,
        forVesselIds: [...selectedItem.forVesselIds],
        //value: forVesselIds,
      })
    }

    if (selectedItem.forCrew) {
      _formElements.push({
        isSpecial: true,
        id: 'crew',
        n: -1,
        o: -1,
        label: selectedItem.crewElement?.label ?? 'SELECT PERSONNEL',
        width: (selectedItem.crewElement?.width ?? 100).toString(),
        allowMultiple: selectedItem.crewElement?.allowMultiple ?? true,
      })
    }

    _formElements = version?.form ? [..._formElements, ...existingForm] : formElements
    setHistoryElementN(version?.historyElementN ?? 0)
    _formElements.forEach((element: CustomFormElementType) => {
      if (element?.help?.files) {
        element.help.files = makeSeaFiles(element.help.files)
      }
    })

    setFormElements(_formElements)
    setFormElementsOriginal(_formElements)
  }, [selectedItem, customFormVersions])

  return (
    <RequirePermissions role="customForms" level={permissionLevels.CREATE} showDenial={true}>
      <View style={styles.container}>
        <SeaPageCard
          titleComponent={
            <SeaPageCardTitle
              customTitleRow={
                <SeaStack
                  direction="column"
                  gap={0}
                  align="start"
                  style={{
                    marginTop: 5,
                  }}>
                  {selectedCategory && (
                    <SeaTypography variant="caption" textStyle={{ margin: 0 }} containerStyle={{ margin: 0 }}>
                      {selectedCategory}
                    </SeaTypography>
                  )}
                  <SeaStack
                    direction={!isDesktopWidth ? 'column' : 'row'}
                    align={!isDesktopWidth ? 'start' : 'center'}
                    gap={10}>
                    <SeaTypography variant="title" textStyle={{ margin: 0 }} containerStyle={{ margin: 0 }}>
                      {selectedItem?.title}
                    </SeaTypography>

                    <SeaStack direction="row" align="center" gap={2}>
                      <SeaIcon icon="save" fill={false} color={theme.colors.grey} />
                      <SeaTypography
                        variant="body"
                        textStyle={{ margin: 0 }}
                        containerStyle={{ margin: 0 }}
                        color={theme.colors.grey}>
                        {selectedItem?.state === 'draft'
                          ? `Created on ${formatDatetime(selectedItem.whenAdded, ' ')}`
                          : `All changes saved on ${formatDatetime(selectedItem?.whenUpdated, ' ')}`}
                      </SeaTypography>
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
              }
            />
          }
          primaryActionButton={
            isDesktopWidth ? (
              <SeaButton key={'Save'} label="Save" variant={SeaButtonVariant.Primary} onPress={() => handleSave()} />
            ) : undefined
          }
          secondaryActionButton={[<SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />]}
        />

        <View style={styles.formBuilderContainer}>
          {!isDesktopWidth ? (
            <SeaTypography variant="body" color={theme.colors.status.critical}>
              To edit forms/checklists you need be using the desktop app or a tablet with at least 1000 pixels of screen
              width.
            </SeaTypography>
          ) : selectedItem ? (
            <FormBuilder
              customForm={selectedItem}
              formElements={formElements}
              setFormElements={setFormElements}
              historyElementN={historyElementN}
              setHistoryElementN={setHistoryElementN}
            />
          ) : (
            <SeaTypography variant="body" color={theme.colors.status.critical}>
              No form selected. Please select a form to edit.
            </SeaTypography>
          )}
        </View>
      </View>

      {isVisibleEditDrawer && (
        <EditCustomFormDrawer
          selectedForm={selectedItem}
          onClose={() => setIsVisibleEditDrawer(false)}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: '100%',
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  formBuilderContainer: {
    flex: 1,
    padding: 14,
  },
}))
