import { View } from 'react-native'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { renderFullName, renderFullNameForUserId } from '@src/shared-state/Core/users'
import { calculateWidth } from '@src/lib/customForms'
import {
  CustomFormElementType,
  SeaCustomFormElement,
} from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { FormikValues, useFormik } from 'formik'
import Yup from '@src/lib/yup'
import { CustomFormVersion } from '@src/shared-state/CompanyDocuments/CustomForms/customFormVersions'
import {
  CompleteCustomFormDto,
  CompleteCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/CompleteCustomFormUseCase'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { formatDatetime, toMillis } from '@src/lib/datesAndTime'

import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { CustomFormCompleted } from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormsCompleted'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useStyles } from '@src/theme/styles'
import {
  UpdateCompletedCustomFormDto,
  UpdateCompletedCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/UpdateCompletedCustomFormUseCase'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { canAccessAllVessels, canComplete, canDelete, canEdit, canView } from '@src/shared-state/Core/userPermissions'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import {
  DeleteCompletedCustomFormDto,
  DeleteCompletedCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/DeleteCompletedCustomFormUseCase'

enum CompleteCustomFormDrawerType {
  COMPLETE = 'complete',
  EDIT = 'edit',
  VIEW = 'view',
}

type InitialValues = {
  formData: Record<string, any>
  vesselIds?: string[]
  personnel?: string[]
  isDraft: boolean // Default value for isDraft
}

export interface CompleteCustomFormDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  customForm?: CustomForm
  formElements?: CustomFormElementType[]
  selectedCompletedForm?: CustomFormCompleted
  type: CompleteCustomFormDrawerType
  toggleEdit?: () => void
}

export function CompleteCustomFormDrawer({
  customForm,
  selectedCompletedForm,
  formElements = [],
  visible,
  onClose,
  style,
  type,
  toggleEdit,
}: CompleteCustomFormDrawerProps) {
  const users = sharedState.users.use(visible)
  const customFormVersions = sharedState.customFormVersions.use(visible)
  const userId = sharedState.userId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const user = sharedState.user.use(visible)

  const [selectedVersion, setSelectedVersion] = useState<CustomFormVersion | undefined>()

  const services = useServiceContainer()
  const { isMobileWidth } = useDeviceWidth()
  const { theme } = useStyles()
  const logger = useLogger('CustomFormDrawer')

  const userCanEdit = useMemo(() => {
    if (!selectedCompletedForm) return false
    const isCustomFormEditor =
      canEdit('customForms') &&
      (selectedCompletedForm?.vesselIds[0] === 'none' || canAccessAllVessels(selectedCompletedForm?.vesselIds))

    const isCustomFormCompleter = canComplete('customForms') && selectedCompletedForm.isDraft

    const isVoyageEditor = selectedCompletedForm.attachTo === 'voyage' && canEdit('logbook')

    const isTrainingViewer = selectedCompletedForm.attachTo === 'trainingTaskReport' && canView('crewTraining')

    return isCustomFormEditor || isCustomFormCompleter || isVoyageEditor || isTrainingViewer
  }, [selectedCompletedForm])

  const userCanDelete = useMemo(() => {
    if (!selectedCompletedForm) return false

    const isCustomFormDeleter = canDelete('customForms')

    const isCustomFormCompleterDeleter =
      canComplete('customForms') && selectedCompletedForm.isDraft && selectedCompletedForm.addedBy === user?.id

    const isVoyageEditor = selectedCompletedForm.attachTo === 'voyage' && canEdit('logbook')

    const isTrainingEditor = selectedCompletedForm.attachTo === 'trainingTaskReport' && canEdit('crewTraining')

    return isCustomFormDeleter || isCustomFormCompleterDeleter || isVoyageEditor || isTrainingEditor
  }, [selectedCompletedForm, user])

  const handleDelete = useCallback(() => {
    if (!selectedCompletedForm || !selectedVersion || !userId || !licenseeId) {
      throw new Error('Version ID, Licensee ID or User ID is not available')
    }

    const dto: DeleteCompletedCustomFormDto = {
      id: selectedCompletedForm.id,
      title: customForm?.title ?? '',
      customFormVersionId: selectedVersion?.id ?? '',
      vesselIds: selectedCompletedForm.vesselIds,
      personnelIds: selectedCompletedForm.personnelIds,
      personnel: selectedCompletedForm.personnelIds?.map(id => renderFullNameForUserId(id)) ?? [],
    }

    const deleteCompletedCustomForm = services.get(DeleteCompletedCustomFormUseCase)

    deleteCompletedCustomForm
      .execute(dto, userId, licenseeId)
      .then(() => {
        setSavingStateManager({
          action: SaveStateAction.SUCCESS,
          onCloseTimeout: 1000,
        })
        onClose && onClose()
      })
      .catch(err => logger.error(`Error deleting Company Document\n ${err.message}`))
  }, [selectedCompletedForm, selectedVersion, userId, licenseeId, customForm?.title, services, onClose, logger])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  const secondaryActionButton = useMemo(() => {
    const actions = []

    if (userCanEdit) {
      actions.push(
        <SeaButton
          key="edit"
          label={`Edit ${selectedCompletedForm?.isDraft ? 'Draft' : 'Completed Form'} `}
          variant={SeaButtonVariant.Secondary}
          onPress={toggleEdit}
        />
      )
    }

    if (userCanDelete) {
      actions.push(<SeaDeleteButton onPress={onDelete} />)
    }

    return actions
  }, [userCanEdit, userCanDelete, selectedCompletedForm, toggleEdit, onDelete])

  const initialValues = useMemo(() => {
    const formData = formElements.reduce(
      (acc, el) => {
        if (el.n !== undefined) {
          if (el.id === 'checks' || el.id === 'files') {
            acc[`e${el.n}`] = Array.isArray(el.value) ? el.value : []
          } else if (el.type === 'number') {
            acc[`e${el.n}`] = typeof el.value === 'number' ? el.value : ''
          } else if (el.id === 'signature') {
            acc[`e${el.n}`] = el.value ?? undefined
          } else {
            acc[`e${el.n}`] = el.value ?? ''
          }
        }
        return acc
      },
      {} as Record<string, any>
    )

    return {
      formData,
      ...(customForm?.forVesselIds && customForm.forVesselIds[0] !== 'none'
        ? {
            vesselIds: !selectedCompletedForm?.vesselIds.includes('none') ? selectedCompletedForm?.vesselIds : [],
          }
        : {}),
      ...(customForm?.forCrew
        ? {
            personnel: selectedCompletedForm?.personnelIds ?? [],
          }
        : {}),
      vesselIds:
        selectedCompletedForm?.vesselIds && !selectedCompletedForm.vesselIds.includes('none')
          ? selectedCompletedForm.vesselIds
          : [],
      personnel: selectedCompletedForm?.personnelIds ?? [],
      isDraft: false, // Default value for isDraft
    } as InitialValues
  }, [formElements, selectedCompletedForm, customForm])

  const validationSchema = useMemo(() => {
    const formDataShape: Record<string, any> = {}

    formElements.forEach(el => {
      if (el.n === undefined) return

      // Use a base shape, will apply .when later
      let validator: any = Yup.string()

      if (el.type === 'number') {
        validator = Yup.number().typeError('Must be a number')
      }

      if (el.id === 'textarea') {
        validator = Yup.string().max(500, 'Max 500 characters')
      }

      if (el.id === 'checks') {
        validator = Yup.array().of(Yup.string().nullable()).nullable()
      } else if (el.id === 'files') {
        validator = Yup.array().of(Yup.object().nullable()).nullable()
      }

      // Now apply .when if required
      if (el.required) {
        validator = validator.when('$isDraft', {
          is: false,
          then: (schema: any) => schema.required('This field is required'),
          otherwise: (schema: any) => schema.nullable(),
        })
      } else {
        validator = validator.nullable()
      }

      formDataShape[`e${el.n}`] = validator
    })

    const shape: Record<string, any> = {
      formData: Yup.object().shape(formDataShape),
    }

    if (customForm?.forVesselIds && customForm.forVesselIds[0] !== 'none') {
      shape.vesselIds = Yup.array().when('$isDraft', {
        is: false,
        then: (schema: any) =>
          schema.of(Yup.string().required('Vessel ID required')).min(1, 'At least one vessel must be selected'),
        otherwise: (schema: any) => schema.of(Yup.string()).nullable(),
      })
    }

    if (customForm?.forCrew) {
      shape.personnel = Yup.array().when('$isDraft', {
        is: false,
        then: (schema: any) =>
          schema.of(Yup.string().required('Personnel ID required')).min(1, 'At least one person must be selected'),
        otherwise: (schema: any) => schema.of(Yup.string()).nullable(),
      })
    }

    return Yup.object().shape(shape)
  }, [formElements, customForm])

  const handleSubmit = useCallback(
    (values: FormikValues): void => {
      if (!customForm || !licenseeId || !userId || !selectedVersion) {
        console.error('Custom Form, Licensee ID, User ID, or Selected Version is not available')
        return
      }

      setSavingStateManager({
        action: SaveStateAction.SAVING,
      })

      const formTitle = customForm?.title ? `${customForm?.title} ${values.isDraft ? '(DRAFT}' : ''}` : ''

      const personnelNames = values?.personnel?.map((id: string) => renderFullNameForUserId(id))

      const title = `${formTitle} ${personnelNames?.length > 0 ? `- ${personnelNames.join(', ')}` : ''}`

      let filesPosition
      let signaturePosition

      formElements.forEach(element => {
        switch (element.id) {
          case 'datetime':
            values.formData[`e${element.n}`] =
              typeof values.formData[`e${element.n}`] === 'number'
                ? values.formData[`e${element.n}`]
                : toMillis(values.formData[`e${element.n}`])
            break
          case 'files':
            values.formData[`e${element.n}`] = values.formData[`e${element.n}`]
            filesPosition = `e${element.n}`
            break
          case 'signature':
            if (values.formData[`e${element.n}`]) {
              values.formData[`e${element.n}`] = values.formData[`e${element.n}`]
              signaturePosition = `e${element.n}`
            } else {
              values.formData[`e${element.n}`] = undefined
              signaturePosition = undefined
            }
            break
          default:
            break
        }
      })

      const commonDto = {
        title,
        customFormId: customForm.id,
        vesselIds: values.vesselIds && values.vesselIds.length > 0 ? values.vesselIds : ['none'],
        personnelIds: values.personnel && values.personnel.length > 0 ? values.personnel : undefined,
        formData: values.formData,
        isDraft: values.isDraft,
        filesPosition,
        signaturePosition,
      }

      if (type === 'complete') {
        const dto: CompleteCustomFormDto = {
          ...commonDto,
          version: selectedVersion.version,
          versionId: selectedVersion.id,
          attachTo: undefined,
          attachToId: undefined,
          attachToTrainingTaskId: undefined,
          attachToVesselId: undefined,
        }

        const completeCustomForm = services.get(CompleteCustomFormUseCase)
        completeCustomForm
          .execute(dto, userId, licenseeId)
          .then(() => {
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 1000,
            })
            onClose()
          })
          .catch(err => {
            setSavingStateManager({
              action: SaveStateAction.ERROR,
              onCloseTimeout: 1000,
            })
            console.error(`Error submitting complete form\n ${err.message}`)
          })
      } else if (type === 'edit' && selectedCompletedForm) {
        const dto: UpdateCompletedCustomFormDto = {
          ...commonDto,
          id: selectedCompletedForm.id,
        }

        const updateCompleteCustomForm = services.get(UpdateCompletedCustomFormUseCase)

        updateCompleteCustomForm
          .execute(dto, userId, licenseeId)
          .then(() => {
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 1000,
            })
            onClose()
          })
          .catch(err => {
            setSavingStateManager({
              action: SaveStateAction.ERROR,
              onCloseTimeout: 1000,
            })
            console.error(`Error submitting complete form\n ${err.message}`)
          })
      }
    },
    [customForm, licenseeId, userId, selectedVersion, formElements, type, selectedCompletedForm, services, onClose]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
    validateOnBlur: true,
    validateOnChange: false,
    enableReinitialize: true,
  })

  const { values, errors, setFieldValue } = formik

  const crewOptions = useMemo(() => {
    if (!users?.staff) return []

    return users.staff
      .filter(user => typeof user.id === 'string')
      .map(user => ({
        label: renderFullName(user),
        value: user.id ?? '',
      }))
  }, [users])

  const selectPersonnel = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT:
          setFieldValue('personnel', [...(values.personnel ?? []), changedValue])
          return
        case CheckBoxActions.DESELECT:
          setFieldValue(
            'personnel',
            (values.personnel ?? []).filter((id: string) => id !== changedValue)
          )
          return
        default:
          return
      }
    },
    [setFieldValue, values.personnel]
  )

  const Form = useMemo(() => {
    return formElements?.map(element => {
      return (
        <View
          key={element.n}
          style={{
            width: calculateWidth(element.width, isMobileWidth),
          }}>
          <SeaCustomFormElement
            element={{
              ...element,
              value: element.n !== undefined ? values.formData[`e${element.n}`] : undefined,
              error:
                element.n !== undefined
                  ? Array.isArray(errors?.formData?.[`e${element.n}`])
                    ? (errors?.formData?.[`e${element.n}`] as string[])[0]
                    : typeof errors?.formData?.[`e${element.n}`] === 'string'
                      ? (errors?.formData?.[`e${element.n}`] as string)
                      : undefined
                  : undefined,
            }}
            setElement={(updatedElement: any) => {
              setFieldValue(`formData.e${element.n}`, updatedElement.value)
            }}
            mode={
              type === CompleteCustomFormDrawerType.VIEW
                ? CompleteCustomFormDrawerType.VIEW
                : CompleteCustomFormDrawerType.COMPLETE
            }
            selectedElement={undefined}
          />
        </View>
      )
    })
  }, [formElements, isMobileWidth, values.formData, errors, type, setFieldValue])

  useEffect(() => {
    if (!customForm) return
    const selectedVersion =
      customFormVersions?.byFormIdAndVersion[customForm.id][selectedCompletedForm?.version ?? customForm.latestVersion]

    setSelectedVersion(selectedVersion)
  }, [customForm, customFormVersions, selectedCompletedForm])

  return (
    <SeaDrawer
      title={customForm?.title ?? ''}
      titleAdditionalElements={() =>
        selectedCompletedForm?.isDraft ? (
          <SeaStatusPill primaryLabel="DRAFT" variant={SeaStatusType.Attention} />
        ) : (
          <></>
        )
      }
      isDirty={formik.dirty}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        type !== 'view' ? (
          <SeaButton
            label={'Submit Form'}
            variant={SeaButtonVariant.Primary}
            key="Save Document"
            onPress={formik.handleSubmit}
          />
        ) : undefined
      }
      secondaryAction={
        type !== 'view' ? (
          <SeaButton
            label={'Save As Draft'}
            variant={SeaButtonVariant.Tertiary}
            key="Draft"
            onPress={async () => {
              await setFieldValue('isDraft', true)
              formik.handleSubmit()
            }}
          />
        ) : undefined
      }
      headerActions={type === CompleteCustomFormDrawerType.VIEW ? secondaryActionButton : undefined}>
      {/* TODO: Add this login when working on edit completed form */}
      {selectedCompletedForm &&
        customForm &&
        selectedVersion &&
        selectedVersion.version !== customForm.latestVersion && (
          <SeaTypography
            variant="body"
            color={theme.colors.status.critical}
            containerStyle={{ width: '100%', flex: 1, marginBottom: 20 }}>
            {`Note: This is an older version of the form dated ${formatDatetime(selectedVersion?.version)}.`}
          </SeaTypography>
        )}
      <View
        style={{
          width: '100%',
          flexWrap: 'wrap',
          flexDirection: 'row',
          gap: 10,
        }}>
        {customForm?.forVesselIds && customForm.forVesselIds[0] !== 'none' && (
          <View
            style={{
              width: calculateWidth(customForm?.vesselsElement?.width, isMobileWidth),
            }}>
            <VesselSelectInput
              label={customForm?.vesselsElement?.label ?? 'SELECT VESSELS / FACILITIES'}
              vesselIds={values.vesselIds}
              setVesselIds={vesselIds => setFieldValue('vesselIds', vesselIds)}
              isMulti={customForm?.vesselsElement?.allowMultiple ?? false}
              vesselIdOptions={customForm.forVesselIds}
              errorText={errors?.vesselIds}
              hasError={!!errors?.vesselIds}
              preserveLabelCase
              disabled={type === CompleteCustomFormDrawerType.VIEW}
            />
          </View>
        )}

        {customForm?.forCrew && (
          <View
            style={{
              width: calculateWidth(customForm?.crewElement?.width, isMobileWidth),
            }}>
            <SeaSelectInput
              label={customForm?.crewElement?.label ?? 'SELECT PERSONNEL'}
              isMulti={customForm?.crewElement?.allowMultiple ?? false}
              showSelectAllOption={false}
              data={crewOptions}
              selectedItemValues={values.personnel}
              style={{ width: '100%' }}
              onItemSelect={selectPersonnel}
              errorText={formik.errors.personnel}
              hasError={!!formik.errors.personnel}
              preserveLabelCase
              disabled={type === CompleteCustomFormDrawerType.VIEW}
            />
          </View>
        )}

        {Form}
      </View>
    </SeaDrawer>
  )
}
