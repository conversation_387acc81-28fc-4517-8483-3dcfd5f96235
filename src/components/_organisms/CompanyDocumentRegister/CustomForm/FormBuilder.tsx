import { View, ScrollView } from 'react-native'
import { useCallback, useEffect, useRef, useState } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  CustomFormElementType,
  FormTextSize,
  SeaCustomFormElement,
} from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { sharedState } from '@src/shared-state/shared-state'
import { calculateWidth, formElementsToArray } from '@src/lib/customForms'
import { makeSeaFiles } from '@src/lib/files'
import { FieldSettings } from './FieldSettings'
import { AddFormElements } from './AddFormElements'
import { SeaDraggable } from '@src/components/_atoms/SeaDraggable/SeaDraggable'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

const elementDefaults = {
  vessels: {
    name: 'Vessels',
    label: 'Select Vessels / Facilities',
    required: true,
  },
  input: {
    name: 'Text Field',
    label: 'TEXT FIELD',
    required: false,
  },
  textarea: {
    name: 'Text Area',
    label: 'TEXT AREA',
    lines: 3, // height = 37px + (lines * 15px)
    required: false,
  },
  checkbox: {
    name: 'Checkbox',
    label: 'Checked',
    required: false,
  },
  dropdown: {
    name: 'Dropdown',
    label: 'DROPDOWN',
    options: ['Option A', 'Option B'],
    required: false,
  },
  date: {
    name: 'Date',
    label: 'DATE',
    required: false,
  },
  datetime: {
    name: 'Date with Time',
    label: 'DATE WITH TIME',
    required: false,
  },
  yesno: {
    name: 'Yes | No',
    label: 'Question?',
  },
  checks: {
    name: 'Checklist',
    label: 'CHECKLIST',
    options: ['A check'],
  },
  files: {
    name: 'Images / Documents',
    label: 'IMAGES / DOCUMENTS',
    required: false,
  },
  signature: {
    name: 'Signature',
    label: 'SIGN OR INITIAL BELOW',
    required: true,
  },
  heading: {
    name: 'Heading',
    heading: 'A Heading',
    size: FormTextSize.MEDIUM,
  },
  text: {
    name: 'Text',
    text: 'Some text',
    size: FormTextSize.MEDIUM,
  },
  line: {},
  spacer: {
    height: 20,
  },
} as {
  [key: string]: Partial<CustomFormElementType>
}

interface FormBuilderProps {
  customForm: CustomForm
  formElements: CustomFormElementType[]
  setFormElements: (elements: CustomFormElementType[]) => void
  historyElementN: number
  setHistoryElementN: (n: number) => void
}
export function FormBuilder({
  customForm,
  formElements,
  setFormElements,
  historyElementN,
  setHistoryElementN,
}: FormBuilderProps) {
  const { styles } = useStyles(styleSheet)
  const [selectedElement, setSelectedElement] = useState<CustomFormElementType>()
  const nRef = useRef(0)
  const scrollViewRef = useRef<ScrollView>(null)
  const [scrollOffset, setScrollOffset] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)

  // Handle scroll events to track current position for auto-scroll
  const handleScroll = (event: { nativeEvent: { contentOffset: { x: number; y: number } } }) => {
    const { x, y } = event.nativeEvent.contentOffset
    setScrollOffset({ x, y })
  }

  const onAddElement = useCallback(
    (id: string) => {
      nRef.current++
      const properties = {
        n: nRef.current,
        id: id,
        width: 100,
      }

      setFormElements([...formElements, Object.assign(properties, elementDefaults[id])])
    },
    [nRef, formElements, setFormElements]
  )

  const handleReorder = useCallback(
    (newOrder: unknown[]) => {
      setFormElements(newOrder as CustomFormElementType[])
    },
    [setFormElements]
  )

  // Function to determine if an element should be draggable
  const isElementDraggable = useCallback((element: unknown, _index: number) => {
    const formElement = element as CustomFormElementType
    return !formElement.isSpecial && formElement.id !== 'vessels' && formElement.id !== 'crew'
  }, [])

  useEffect(() => {
    if (!formElements) return

    formElements.forEach((element: CustomFormElementType) => {
      if (element?.help?.files) {
        element.help.files = makeSeaFiles(element.help.files)
      }

      if (element.n && element.n > nRef.current) {
        nRef.current = element.n
      }
    })
  }, [formElements])

  return (
    <View style={styles.formBuilder}>
      {/* Form Elements */}
      <View style={{ flex: 1, maxWidth: 280 }}>
        <AddFormElements onAddElement={onAddElement} />
      </View>

      {/* Form Body */}
      <View style={{ flex: 2, alignItems: 'center' }}>
        {formElements.length === 0 ? (
          <SeaTypography variant="overline" textStyle={{ textAlign: 'center', marginTop: 20 }}>
            Start creating your form by adding elements from the left panel.
          </SeaTypography>
        ) : (
          <ScrollView
            ref={scrollViewRef}
            style={{ width: '100%', height: '100%' }}
            contentContainerStyle={{ alignItems: 'center' }}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            scrollEnabled={!isDragging}
            showsVerticalScrollIndicator={true}>
            <View
              style={[
                styles.container,
                {
                  maxWidth: 800,
                  boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
                },
              ]}>
              <SeaDraggable
                type="customForms"
                items={formElements}
                onReorder={handleReorder}
                selectedIndex={selectedIndex}
                setSelectedIndex={setSelectedIndex}
                flexDirection="row"
                scrollViewRef={scrollViewRef}
                scrollOffset={scrollOffset}
                onDragStateChange={setIsDragging}
                isItemDraggable={isElementDraggable}>
                {formElements?.map((element, index) => {
                  return (
                    <View
                      key={`${element.n}-${index}`}
                      style={{
                        width: calculateWidth(element.width, false, true),
                      }}>
                      <SeaCustomFormElement
                        element={element}
                        mode={'edit'}
                        selectedElement={selectedElement}
                        onSelectElement={element => setSelectedElement(element)}
                      />
                    </View>
                  )
                })}
              </SeaDraggable>
            </View>
          </ScrollView>
        )}
      </View>

      {/* Field Settings */}
      <View style={{ flex: 1 }}>
        {selectedElement && (
          <FieldSettings
            selectedElement={selectedElement}
            updateElement={(updatedElement: CustomFormElementType) => {
              const elements = formElements.map(el => (el.n === updatedElement.n ? updatedElement : el))
              setFormElements(elements)
              setSelectedElement(updatedElement)
            }}
            removeElement={(element: CustomFormElementType) => {
              const elements = formElements.filter(el => el.n !== element.n)
              setFormElements(elements)
              setSelectedElement(undefined)
            }}
            historyElementN={historyElementN}
            setHistoryElementN={_n => setHistoryElementN(_n)}
          />
        )}
      </View>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  formBuilder: {
    flex: 1,
    height: '100%',
    flexDirection: 'row',
    gap: 10,
  },
  container: {
    width: '100%',
    backgroundColor: theme.colors.white,
    padding: 20,
    paddingBottom: 30,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
  },
  formElement: {
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 4,
    backgroundColor: '#F1F2F5',
    gap: 10,
  },
}))
