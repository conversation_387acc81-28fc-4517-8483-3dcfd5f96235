import { Text, View } from 'react-native'
import React, { useCallback, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { CompanyDocument } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { formatSeaDate, makeDateTime, subtractInterval } from '@src/lib/datesAndTime'
import { IntervalDropdown } from '@src/components/_molecules/IntervalDropdown/IntervalDropdown'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { getDefaultCategoryId } from '@src/lib/categories'
import { FormikValues, useFormik } from 'formik'
import { SeaFile } from '@src/lib/fileImports'
import Yup, { notTooOld } from '@src/lib/yup'
import {
  UpdateCompanyDocumentDto,
  UpdateCompanyDocumentUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/UpdateCompanyDocumentUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  CreateCompanyDocumentDto,
  CreateCompanyDocumentUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/CreateCompanyDocumentUseCase'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'

enum DocumentationType {
  Controlled = 'sfdoc',
  ExternalFiles = 'files',
}

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  type: Yup.string().max(500).required(),
  dateExpires: Yup.date().when('type', {
    is: 'renewable',
    then: schema => schema.required().min(...notTooOld),
  }),
  interval: Yup.string().when('type', {
    is: 'renewable',
    then: schema => schema.max(4).required(),
  }),
  emailReminder: Yup.string().when('type', {
    is: 'renewable',
    then: schema => schema.max(200),
  }),
  categoryId: Yup.string().max(500).required(),
})

export interface EditCompanyDocumentsDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: CompanyDocument
  mode?: DrawerMode
}

export function EditCompanyDocumentsDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditCompanyDocumentsDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const companyDocumentCategories = sharedState.companyDocumentCategories.use(visible)

  const [files, setFiles] = useState<SeaFile[]>([])

  const services = useServiceContainer()

  const documentTypeOptions = useMemo(() => {
    return [
      {
        label: 'Renewable Document',
        value: 'renewable',
      },
      {
        label: 'Non-Expiring Document',
        value: 'nonExpiring',
      },
    ]
  }, [])

  const categoriesOptions = useMemo(() => {
    if (!companyDocumentCategories) return []

    const categories = companyDocumentCategories?.ids.map(id => {
      const category = companyDocumentCategories.byId[id]
      return {
        label: category.name,
        value: category.id,
      }
    })

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...(categories ?? []),
    ]
  }, [companyDocumentCategories])

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      type: selectedItem?.type ?? 'renewable',
      dateExpires: selectedItem?.dateExpires ?? '',
      emailReminder: selectedItem?.emailReminder ?? '',
      categoryId: selectedItem?.categoryId ?? getDefaultCategoryId('General', companyDocumentCategories),
      interval: selectedItem?.interval ?? '',
      documentationType:
        selectedItem?.files && selectedItem.files.length > 0
          ? DocumentationType.ExternalFiles
          : DocumentationType.Controlled,

      files: selectedItem?.files ?? [],
      sfdoc: selectedItem?.sfdoc ?? {},
    }
  }, [selectedItem, companyDocumentCategories])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!licenseeId || !userId) {
        console.error('Licensee ID, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (values.type === 'renewable' && values.dateExpires && values.emailReminder) {
        dateToRemind = subtractInterval(values.dateExpires, values.emailReminder).toISODate() ?? undefined
      }

      const commonDto = {
        title: values.title,
        dateExpires:
          values.type === 'renewable' && values.dateExpires
            ? (formatSeaDate(values.dateExpires) ?? undefined)
            : undefined,
        emailReminder: values.type === 'renewable' && values.emailReminder ? values.emailReminder : undefined,
        interval: values.type === 'renewable' && values.interval ? values.interval : undefined,
        categoryId: values.categoryId,
        dateToRemind,
        files: files,
        type: values.type,
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateCompanyDocumentDto = {
          ...commonDto,
        }

        const createCompanyDocument = services.get(CreateCompanyDocumentUseCase)

        createCompanyDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Company Document\n ${err.message}`))
      } else {
        const dto: UpdateCompanyDocumentDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateCompanyDocument = services.get(UpdateCompanyDocumentUseCase)

        updateCompanyDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Company Document\n ${err.message}`))
      }
    },
    [files, licenseeId, mode, onClose, selectedItem?.id, services, userId]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  return (
    <SeaDrawer
      title={`${mode === DrawerMode.Create ? 'Add New Company Document' : 'Update Company Document'}`}
      visible={visible}
      onClose={onClose}
      style={style}
      isDirty={formik.dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Company Document'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <SeaStack direction="column" gap={10} justify="start" align="start">
        <SeaTextInput
          label={'Document Title'}
          value={formik.values.title}
          onChangeText={formik.handleChange('title')}
          errorText={formik.errors.title ?? undefined}
          hasError={!!formik.errors.title}
        />

        <SeaStack direction="row" gap={10} justify="start" align="start" width={'100%'}>
          <View style={{ flex: 1 }}>
            <SeaDropdown
              label={'Document Type'}
              items={documentTypeOptions}
              style={{
                width: '100%',
              }}
              onSelect={value => formik.setFieldValue('type', value)}
              value={formik.values.type}
            />
          </View>

          <View style={{ flex: 1 }}>
            <SeaDropdown
              label={'Category'}
              items={categoriesOptions}
              style={{
                width: '100%',
              }}
              onSelect={value => formik.setFieldValue('categoryId', value)}
              value={formik.values.categoryId}
            />
          </View>
        </SeaStack>

        {formik.values.type.includes('renewable') && (
          <>
            <SeaStack direction="row" gap={10} justify="start" align="start" width={'100%'}>
              <View style={{ flex: 1 }}>
                <SeaDateTimeInput
                  value={makeDateTime(formik.values.dateExpires)}
                  onChange={date => formik.setFieldValue('dateExpires', date)}
                  type={'date'}
                  label="Expiry Date"
                  style={{ flex: 1 }}
                  errorText={formik.errors.dateExpires ?? undefined}
                  hasError={!!formik.errors.dateExpires}
                />
              </View>

              <View style={{ flex: 1 }}>
                <IntervalDropdown
                  onChange={value => formik.setFieldValue('interval', value)}
                  value={formik.values.interval}
                  label="Renewal Interval"
                  errorText={formik.errors.interval ?? undefined}
                  hasError={!!formik.errors.interval}
                />
              </View>
            </SeaStack>
            <SeaEmailReminderDropdown
              label="Set Email Reminder"
              value={formik.values.emailReminder}
              onSelect={value => formik.setFieldValue('emailReminder', value)}
              style={{ flex: 1, width: '100%' }}
            />
          </>
        )}
        <SeaDropdown
          label={'Documentation'}
          items={[
            {
              label: 'Controlled',
              value: DocumentationType.Controlled,
            },
            {
              label: 'External File(s)',
              value: DocumentationType.ExternalFiles,
            },
          ]}
          style={{
            width: '100%',
          }}
          onSelect={value => formik.setFieldValue('documentationType', value)}
          value={formik.values.documentationType}
        />
        {formik.values.documentationType === DocumentationType.ExternalFiles ? (
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        ) : (
          <Text>TODO Rich text editor</Text>
        )}
      </SeaStack>
    </SeaDrawer>
  )
}
