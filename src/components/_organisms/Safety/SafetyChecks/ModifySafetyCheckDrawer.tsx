import React, { useMemo, useState } from 'react'
import { KeyboardAvoidingView, ViewStyle } from 'react-native'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import {
  ADD_NEW_DROPDOWN_VALUE,
  AddNewDropdownItem,
  SeaDropdown,
  SeaDropdownItem,
} from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SafetyCheckItem } from '@src/shared-state/VesselSafety/safetyCheckItems'
import { sharedState } from '@src/shared-state/shared-state'
import { IntervalDropdown } from '@src/components/_molecules/IntervalDropdown/IntervalDropdown'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime, Duration } from 'luxon'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateSafetyCheckDto, CreateSafetyCheckUseCase } from '@src/domain/use-cases/safety/CreateSafetyCheckUseCase'
import { useFormik } from 'formik'
import { UpdateSafetyCheckDto, UpdateSafetyCheckUseCase } from '@src/domain/use-cases/safety/UpdateSafetyCheckUseCase'
import Yup from '@src/lib/yup'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { SeaDurationInput } from '@src/components/_atoms/_inputs/SeaDurationInput/SeaDurationInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaFile } from '@src/lib/fileImports'
import { CrewSelectInput } from '@src/components/_molecules/CrewSelectInput/CrewSelectInput'
import { UserType } from '@src/shared-state/Core/user'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { CategoriesData } from '@src/lib/categories'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

interface ModifySafetyCheckDrawerProps {
  visible: boolean
  onClose: () => void
  mode: DrawerMode
  selectedItemId?: string
  style?: ViewStyle
}

const validationSchema = Yup.object({
  itemId: Yup.string().required(),
  interval: Yup.string().required(),
  lastCheck: Yup.number().required(),
  newItemName: Yup.string().when('itemId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
  newLocationName: Yup.string().when('locationId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
  newCategoryName: Yup.string().when('categoryId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
})

export const useInitialSafetyCheckValues = (safetyCheck?: SafetyCheckItem, vesselSafetyItems?: CategoriesData) => {
  return useMemo(() => {
    return {
      itemId: safetyCheck?.itemId ?? '',
      locationId: safetyCheck?.locationId ?? '',
      categoryId: safetyCheck?.categoryId ?? '',
      isCritical: !!vesselSafetyItems?.byId[safetyCheck?.itemId]?.isCritical,
      description: safetyCheck?.description ?? '',
      interval: safetyCheck?.interval ?? '',
      lastCheck: safetyCheck?.whenLastChecked ?? DateTime.now().toMillis(),
      estimatedTime: safetyCheck?.estimatedTime ?? 0,
      assignedTo: safetyCheck?.assignedTo ?? [],
      newItemName: '',
      newLocationName: '',
      newCategoryName: '',
    }
  }, [safetyCheck, vesselSafetyItems])
}

export const ModifySafetyCheckDrawer: React.FC<ModifySafetyCheckDrawerProps> = ({
  visible,
  onClose,
  mode,
  selectedItemId,
  style,
}) => {
  const title = useMemo(() => (mode === DrawerMode.Edit ? 'Update Safety Check' : 'Add Safety Check'), [mode])

  const logger = useLogger(`ModifySafetyCheckDrawer:${mode}`)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const { hasTimeTrackingEnabled } = useLicenseeSettings()

  const user = sharedState.user.use()
  const users = sharedState.users.use()
  const vesselId = sharedState.vesselId.use()
  const licenseeId = sharedState.licenseeId.use()
  const safetyChecks = sharedState.safetyCheckItems.use()
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()

  const selectedSafetyCheck = useMemo(() => {
    if (!selectedItemId || !safetyChecks) {
      return undefined
    }

    return safetyChecks.byId[selectedItemId]
  }, [safetyChecks, selectedItemId])

  const initialValues = useInitialSafetyCheckValues(selectedSafetyCheck, vesselSafetyItems)

  const crewSelectionData = useMemo(() => {
    if (!vesselId) return []

    const vesselUsers = users?.byVesselId[vesselId]

    if (!vesselUsers) return []

    return vesselUsers
      .filter((u: UserType) => u.state === 'active')
      .map(u => ({ label: renderFullNameForUserId(u.id), value: u.id }))
  }, [users, vesselId])

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched, dirty } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  // Safety Item Options
  const safetyItemOptions = useMemo(() => {
    if (!vesselSafetyItems) return []

    const result = Object.entries(vesselSafetyItems.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })

    return [AddNewDropdownItem, ...result]
  }, [vesselSafetyItems])

  const isNewSafetyItem = useMemo(() => values.itemId === ADD_NEW_DROPDOWN_VALUE, [values.itemId])
  const [newSafetyItemName, setNewSafetyItemName] = useState('')

  // Category options
  const safetyCheckCategories = sharedState.safetyCheckCategories.use()
  const categoryOptions = useMemo(() => {
    if (!safetyCheckCategories) return []

    const result = Object.entries(safetyCheckCategories.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => ({ label: value.name, value: value.id }))

    return [AddNewDropdownItem, ...result]
  }, [safetyCheckCategories])

  const isNewCategoryItem = useMemo(() => values.categoryId === ADD_NEW_DROPDOWN_VALUE, [values.categoryId])
  const [newCategoryName, setNewCategoryName] = useState('')

  // Location Options
  const vesselLocations = sharedState.vesselLocations.use()
  const vesselLocationOptions = useMemo<SeaDropdownItem<string>[]>(() => {
    if (!vesselLocations) return []

    const result = Object.entries(vesselLocations.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })

    return [AddNewDropdownItem, ...result]
  }, [vesselLocations])

  const isNewLocationItem = useMemo(() => values.locationId === ADD_NEW_DROPDOWN_VALUE, [values.locationId])
  const [newLocationName, setNewLocationName] = useState('')

  const serviceContainer = useServiceContainer()

  const addSafetyCheckUseCase = serviceContainer.get(CreateSafetyCheckUseCase)
  const updateSafetyCheckUseCase = serviceContainer.get(UpdateSafetyCheckUseCase)

  const executeCreate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const dto: CreateSafetyCheckDto = {
      vesselId,
      itemId: isNewSafetyItem ? undefined : values.itemId,
      isNewItem: isNewSafetyItem,
      newItemName: newSafetyItemName ?? undefined,
      locationId: isNewLocationItem ? undefined : values.locationId,
      isNewLocation: isNewLocationItem,
      newLocationName: newLocationName ?? undefined,
      categoryId: isNewCategoryItem ? undefined : values.categoryId,
      isNewCategory: isNewCategoryItem,
      newCategoryName: newCategoryName ?? undefined,
      isCritical: values.isCritical,
      description: values.description,
      interval: values.interval,
      whenLastChecked: values.lastCheck,
      estimatedTime: values.estimatedTime,
      files: files,
      assignedTo: values.assignedTo ?? [],
    }

    addSafetyCheckUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Check created successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error creating Safety Check\n ${err.message}`, err))
  }

  const executeUpdate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    if (!selectedItemId) {
      throw new Error('Cannot update Safety Check without ID')
    }

    const dto: UpdateSafetyCheckDto = {
      vesselId,
      safetyCheckId: selectedItemId,
      itemId: isNewSafetyItem ? undefined : values.itemId,
      isNewItem: isNewSafetyItem,
      newItemName: newSafetyItemName ?? undefined,
      locationId: isNewLocationItem ? undefined : values.locationId,
      isNewLocation: isNewLocationItem,
      newLocationName: newLocationName ?? undefined,
      categoryId: isNewCategoryItem ? undefined : values.categoryId,
      isNewCategory: isNewCategoryItem,
      newCategoryName: newCategoryName ?? undefined,
      isCritical: values.isCritical,
      description: values.description,
      interval: values.interval,
      whenLastChecked: values.lastCheck,
      estimatedTime: values.estimatedTime,
      files: files,
      assignedTo: values.assignedTo ?? [],
    }
    updateSafetyCheckUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Check updated successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error updating Safety Check\n ${err.message}`, err))
  }

  const doSubmit = (values: typeof initialValues) => {
    if (!user?.id || !licenseeId || !vesselId) {
      // This should never happen at this point
      throw new Error('Missing Licensee or User')
    }

    mode === DrawerMode.Create
      ? executeCreate(values, vesselId, user.id, licenseeId)
      : executeUpdate(values, vesselId, user.id, licenseeId)

    resetForm()
    onClose()
  }

  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      isDirty={dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Safety Check'}
          edit={{ onSubmit: handleSubmit }}
          create={{ onSubmit: handleSubmit }}
        />
      }>
      <KeyboardAvoidingView>
        <DrawerContent>
          {/* Row 1 */}
          <DrawerRow>
            <DrawerColumn>
              <SeaDropdown
                items={safetyItemOptions}
                value={values.itemId}
                onSelect={handleChange('itemId')}
                label={'Item'}
                showIcon={true}
                style={{ flex: 1, width: '100%' }}
                hasError={Boolean(errors.itemId && touched.itemId)}
                errorText={errors.itemId}
              />
              {isNewSafetyItem && (
                <SeaTextInput
                  value={newSafetyItemName}
                  placeholder={'Enter new item name'}
                  onChangeText={text => setNewSafetyItemName(text)}
                  hasError={Boolean(errors.newItemName) && touched.newItemName}
                  errorText={errors.newItemName}
                  style={{ flex: 1, width: '100%' }}
                />
              )}
            </DrawerColumn>
            <DrawerColumn>
              <SeaDropdown
                items={vesselLocationOptions}
                value={values.locationId}
                onSelect={handleChange('locationId')}
                label={'Location'}
                showIcon={true}
                labelIconOptions={{ icon: 'location_on' }}
                style={{ flex: 1, width: '100%' }}
              />
              {isNewLocationItem && (
                <SeaTextInput
                  value={newLocationName}
                  placeholder={'Enter new location'}
                  onChangeText={text => setNewLocationName(text)}
                  hasError={Boolean(errors.newLocationName) && touched.newLocationName}
                  errorText={errors.newLocationName}
                  style={{ flex: 1, width: '100%' }}
                />
              )}
            </DrawerColumn>
          </DrawerRow>

          {/* Row 2 */}
          <DrawerRow>
            <DrawerColumn>
              <SeaDropdown
                items={categoryOptions}
                value={values.categoryId}
                onSelect={handleChange('categoryId')}
                label={'Category'}
                showIcon={true}
                style={{ flex: 1, width: '100%' }}
              />
              {isNewCategoryItem && (
                <SeaTextInput
                  value={newCategoryName}
                  placeholder={'Enter new category'}
                  onChangeText={text => setNewCategoryName(text)}
                  hasError={Boolean(errors.newCategoryName) && touched.newCategoryName}
                  errorText={errors.newCategoryName}
                  style={{ flex: 1, width: '100%' }}
                />
              )}
            </DrawerColumn>
            <DrawerColumn>
              <SeaCheckbox
                heading={'Critical Equipment'}
                label={'This equipment is critical'}
                showIcon={true}
                value={values.isCritical ?? false}
                onChange={x => setFieldValue('isCritical', x)}
                style={{ flex: 1, width: '100%' }}
              />
            </DrawerColumn>
          </DrawerRow>

          {/* Row 3 */}
          <DrawerRow>
            <SeaTextInput
              label={'Description'}
              showIcon={true}
              multiLine={true}
              value={values.description}
              onChangeText={handleChange('description')}
            />
          </DrawerRow>

          {/* Row 4 */}
          <DrawerRow>
            <IntervalDropdown
              value={values.interval}
              showIcon={true}
              onChange={handleChange('interval')}
              hasError={Boolean(errors.interval && touched.interval)}
              errorText={errors.interval}
              style={{ width: '100%', flex: 1 }}
            />
            <SeaDateTimeInput
              value={DateTime.fromMillis(values.lastCheck)}
              onChange={date => setFieldValue('lastCheck', date.toMillis())}
              type={'date'}
              label={'Last Checked'}
              showIcon={true}
              disabled={mode === DrawerMode.Edit}
              hasError={Boolean(errors.lastCheck)}
              errorText={errors.lastCheck}
              style={{ width: '100%', flex: 1 }}
            />
          </DrawerRow>

          <DrawerRow>
            <CrewSelectInput
              label={'Assigned To'}
              showIcon={true}
              selectedIds={values.assignedTo}
              data={crewSelectionData}
              onChange={crew => setFieldValue('assignedTo', crew)}
            />

            {/* Row 5 */}
            <DrawerColumn>
              {hasTimeTrackingEnabled && (
                <SeaDurationInput
                  value={Duration.fromMillis(values.estimatedTime)}
                  onChange={duration => setFieldValue('estimatedTime', duration.toMillis())}
                  label={'Estimated Time'}
                  showIcon={true}
                  hasError={hasTimeTrackingEnabled && Boolean(errors.estimatedTime)}
                  errorText={errors.estimatedTime}
                />
              )}
            </DrawerColumn>
          </DrawerRow>

          {/* Row 5 */}
          <DrawerRow>
            <SeaFileUploader initialFiles={selectedSafetyCheck?.files} files={files} setFiles={setFiles} />
          </DrawerRow>

          <SeaSpacer height={50} />

          {/*  TODO: links*/}
          {/*<SeaStack direction={"row"} gap={10} style={styles.row}>*/}
          {/*<AssignUserModal />*/}
          {/*</SeaStack>*/}
        </DrawerContent>
      </KeyboardAvoidingView>
    </SeaDrawer>
  )
}
