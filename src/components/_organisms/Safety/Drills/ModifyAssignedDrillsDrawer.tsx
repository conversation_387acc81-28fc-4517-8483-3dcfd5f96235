import React, { useMemo } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import {
  UpdateAssignedDrillsDto,
  UpdateAssignedDrillsUseCase,
} from '@src/domain/use-cases/safety/UpdateAssignedDrillsUseCase'
import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'

const validationSchema = Yup.object({
  drillType: Yup.string().required(),
  assignedTo: Yup.array().of(Yup.string()).min(1).required(),
  dueDate: Yup.date().required(),
  description: Yup.string().max(5000),
  interval: Yup.string().required(),
})

export interface ModifyAssignedDrillsDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedUserId: string
}

export const ModifyAssignedDrillsDrawer = ({ visible, onClose, selectedUserId }: ModifyAssignedDrillsDrawerProps) => {
  const vesselId = sharedState.vesselId.use()
  const vesselDrills = sharedState.vesselDrills.use(visible)

  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const logger = useLogger('ModifyAssignedDrillsDrawer')

  const serviceContainer = useServiceContainer()
  const updateAssignedDrillsUseCase = serviceContainer.get(UpdateAssignedDrillsUseCase)

  const drills = useMemo(() => {
    if (!vesselDrills) return []

    return vesselDrills?.all.filter(x => x.state === 'active')
  }, [vesselDrills])

  const drillOptions = useMemo(() => drills?.map(d => ({ label: d.name, value: d.id })), [drills])

  const userAssignedDrills = useMemo(() => {
    return drills?.filter(d => userId && !d.notAssignedTo?.includes(selectedUserId)).map(x => x.id)
  }, [drills, selectedUserId, userId])

  const initialValues = {
    drillIds: userAssignedDrills,
  }

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched, dirty } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  const doSubmit = (values: typeof initialValues) => {
    logger.info('Form submitting with values:', { values })

    if (!userId || !vesselId || !licenseeId) {
      return logger.error('Cannot submit form due to missing User ID, Vessel ID, or Licensee ID')
    }

    logger.info('Executing Update')
    const dto: UpdateAssignedDrillsDto = {
      vesselId,
      vesselDrills: drills.map(d => d.id),
      assignedDrills: values.drillIds,
      drillUserId: selectedUserId,
    }

    updateAssignedDrillsUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Executed Update')
        resetForm()
        onClose()
      })
      .catch(err => {
        logger.error(err.message, err)
        resetForm()
        onClose()
      })
  }

  return (
    <SeaDrawer
      title={'Edit Assigned Drills'}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      isDirty={dirty}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          label={'Save'}
          iconOptions={{ icon: 'save' }}
          onPress={handleSubmit}
        />
      }>
      <SeaStack direction={'column'} gap={20} width={'100%'} justify={'start'} align={'start'}>
        {/* Row 2 */}
        <SeaStack direction={'row'} gap={20} width={'100%'} justify={'between'}>
          <SeaSelectInput
            isMulti={true}
            showSelectAllOption={false}
            selectedItemValues={values.drillIds}
            onItemSelect={(action, changedValues) => {
              switch (action) {
                case CheckBoxActions.SELECT:
                  return setFieldValue('drillIds', [...values.drillIds, changedValues])
                case CheckBoxActions.DESELECT:
                  return setFieldValue(
                    'drillIds',
                    [...values.drillIds].filter(x => x !== changedValues)
                  )
              }
            }}
            label={'Assigned Drills'}
            data={drillOptions}
          />
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}
