import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { DateTime } from 'luxon'
import { useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateDrillReportDto, CreateDrillReportUseCase } from '@src/domain/use-cases/safety/CreateDrillReportUseCase'
import { UpdateDrillReportDto, UpdateDrillReportUseCase } from '@src/domain/use-cases/safety/UpdateDrillReportUseCase'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions, SimpleSelectionData } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import Yup from '@src/lib/yup'
import { SeaFile } from '@src/lib/fileImports'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

const validationSchema = Yup.object({
  drillIds: Yup.array().of(Yup.string()).min(1, 'At least one Drill is required').required(),
  crewInvolvedIds: Yup.array().of(Yup.string()).min(1, 'At least one Crew Member is required').required(),
  dateCompleted: Yup.date().required(),
})

export interface ModifyDrillReportDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: DrillReport
  selectedDrillId?: string
  selectedUserId?: string
  mode: DrawerMode
}

export const ModifyDrillReportDrawer = ({
  visible,
  onClose,
  mode,
  selectedItem,
  selectedDrillId,
  selectedUserId,
}: ModifyDrillReportDrawerProps) => {
  const vesselId = sharedState.vesselId.use()
  const vesselDrills = sharedState.vesselDrills.use(visible)
  const licenseeUsers = sharedState.users.use(visible)

  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const logger = useLogger('ModifyDrillReportDrawer')
  const serviceContainer = useServiceContainer()
  const createDrillReportUseCase = serviceContainer.get(CreateDrillReportUseCase)
  const updateDrillReportUseCase = serviceContainer.get(UpdateDrillReportUseCase)

  const drills = useMemo(() => {
    if (!vesselDrills) return []

    return vesselDrills?.all.filter(x => x.state === 'active')
  }, [vesselDrills])

  const drillOptions = useMemo(() => drills?.map(d => ({ label: d.name, value: d.id })), [drills])

  const users = useMemo(() => {
    if (!licenseeUsers || !vesselId) return []

    return licenseeUsers?.all.filter(x => x.state === 'active' && x.crewVesselIds?.includes(vesselId))
  }, [vesselId, licenseeUsers])

  const userOptions = useMemo(
    () =>
      users?.map(u => ({
        label: renderFullNameForUserId(u.id),
        value: u.id,
      })),
    [users]
  )

  const title = mode === DrawerMode.Create ? 'Complete Drill Report' : 'Edit Drill Report'

  const [files, setFiles] = useState<SeaFile[]>([])

  const initialValues = {
    dateCompleted: selectedItem?.dateCompleted ?? DateTime.now().toISODate(),
    location: selectedItem?.location ?? '',
    drillIds: selectedItem ? selectedItem.drillIds : selectedDrillId ? [selectedDrillId] : [],
    crewInvolvedIds: selectedItem ? selectedItem.crewInvolvedIds : selectedUserId ? [selectedUserId] : [],
    scenario: selectedItem?.scenario ?? '',
    equipment: selectedItem?.equipment ?? '',
    furtherTraining: selectedItem?.furtherTraining ?? '',
    modification: selectedItem?.modification ?? '',
  }

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched, dirty } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  const executeCreate = useCallback(
    (values: typeof initialValues, userId: string, vesselId: string, licenseeId: string) => {
      logger.info('Executing Create')

      const selectedDrills = drills?.filter(x => values.drillIds.includes(x.id))

      const dto: CreateDrillReportDto = {
        vesselId,
        drills: selectedDrills,
        crewInvolvedIds: values.crewInvolvedIds,
        dateCompleted: values.dateCompleted,
        location: values.location,
        scenario: values.scenario,
        equipment: values.equipment,
        furtherTraining: values.furtherTraining,
        modification: values.modification,
        files: [], // TODO
        signature: '', // TODO
      }

      createDrillReportUseCase
        .execute(dto, userId, licenseeId)
        .then(() => {
          logger.info('Executed Create')
          resetForm()
          onClose()
        })
        .catch(err => {
          logger.error(err.message, err)
          resetForm()
          onClose()
        })
    },
    [createDrillReportUseCase, drills, logger, onClose, resetForm]
  )

  const executeUpdate = useCallback(
    (userId: string, vesselId: string, licenseeId: string) => {
      logger.info('Executing Update')
      const dto: UpdateDrillReportDto = {}

      updateDrillReportUseCase
        .execute(dto, userId, licenseeId)
        .then(() => {
          logger.info('Executed Update')
          resetForm()
          onClose()
        })
        .catch(err => {
          logger.error(err.message, err)
          resetForm()
          onClose()
        })
    },
    [logger, onClose, resetForm, updateDrillReportUseCase]
  )

  const doSubmit = useCallback(
    (values: typeof initialValues) => {
      logger.info('Form submitting with values:', { values })

      if (!userId || !vesselId || !licenseeId) {
        return logger.error('Cannot submit form due to missing User ID, Vessel ID, or Licensee ID')
      }

      return mode === DrawerMode.Create
        ? executeCreate(values, userId, vesselId, licenseeId)
        : executeUpdate(userId, vesselId, licenseeId)
    },
    // dependencies
    [logger, userId, vesselId, licenseeId, mode, executeCreate, executeUpdate]
  ) as (values: typeof initialValues) => void

  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      isDirty={dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Drill Report'}
          edit={{ onSubmit: handleSubmit }}
          create={{ onSubmit: handleSubmit }}
        />
      }>
      <DrawerContent>
        {/* Row 1 */}
        <DrawerRow>
          <SeaDateTimeInput
            label={'Drill Date'}
            showIcon={true}
            value={DateTime.fromISO(values.dateCompleted)}
            onChange={date => setFieldValue('dateCompleted', date.toISODate())}
            type={'datetime'}
            style={{ width: '100%', flex: 1 }}
          />
          <SeaTextInput
            label={'Location'}
            showIcon={true}
            labelIconOptions={{ icon: 'location_on' }}
            value={values.location}
            onChangeText={handleChange('location')}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {/* Row 2 */}
        <DrawerRow>
          <SeaSelectInput
            isMulti={true}
            showSelectAllOption={false}
            selectedItemValues={values.drillIds}
            onItemSelect={(action, changedValues) => {
              switch (action) {
                case CheckBoxActions.SELECT:
                  return setFieldValue('drillIds', [...values.drillIds, changedValues])
                case CheckBoxActions.DESELECT:
                  return setFieldValue(
                    'drillIds',
                    [...values.drillIds].filter(x => x !== changedValues)
                  )
              }
            }}
            label={'Drill Type'}
            showIcon={true}
            data={drillOptions}
            hasError={Boolean(errors.drillIds)}
            errorText={errors.drillIds as string}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {/* Row 3 */}
        <DrawerRow>
          <SeaSelectInput
            isMulti={true}
            showSelectAllOption={false}
            selectedItemValues={values.crewInvolvedIds}
            onItemSelect={(action, changedValues) => {
              switch (action) {
                case CheckBoxActions.SELECT:
                  return setFieldValue('crewInvolvedIds', [...values.crewInvolvedIds, changedValues])
                case CheckBoxActions.DESELECT:
                  return setFieldValue(
                    'crewInvolvedIds',
                    [...values.crewInvolvedIds].filter(x => x !== changedValues)
                  )
              }
            }}
            label={'Personnel Present'}
            showIcon={true}
            labelIconOptions={{ icon: 'person' }}
            data={userOptions as SimpleSelectionData}
            hasError={Boolean(errors.crewInvolvedIds)}
            errorText={errors.crewInvolvedIds as string}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {/* Row 4 */}
        <DrawerRow>
          <SeaTextInput
            multiLine
            label={'Scenario'}
            showIcon={true}
            value={values.scenario}
            onChangeText={handleChange('scenario')}
            style={{ width: '100%', flex: 1 }}
          />
          <SeaTextInput
            multiLine
            label={'Equipment Used'}
            showIcon={true}
            value={values.equipment}
            onChangeText={handleChange('equipment')}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {/* Row 5 */}
        <DrawerRow>
          <SeaTextInput
            multiLine
            label={'Further Training Required'}
            showIcon={true}
            value={values.furtherTraining}
            onChangeText={handleChange('furtherTraining')}
            style={{ width: '100%', flex: 1 }}
          />
          <SeaTextInput
            multiLine
            label={'Modification to Current Procedures'}
            showIcon={true}
            value={values.modification}
            onChangeText={handleChange('modification')}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {/* Row 6 */}
        <DrawerRow>
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
