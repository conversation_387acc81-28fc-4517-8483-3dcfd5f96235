import React, { useMemo } from 'react'
import { CompactRowPosition, SeaTable, SeaTableColumn, SeaTableRow } from '@src/components/_atoms/SeaTable/SeaTable'
import { renderFullName } from '@src/shared-state/Core/users'
import { SeaStatusType } from '@src/types/Common'
import { DrillsMatrixData, UserDrillsData } from '@src/hooks/useDrillsMatrix'
import { TrainingCell } from '@src/components/_organisms/Safety/Drills/TrainingCell'
import { Pressable } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { renderCategoryName } from '@src/lib/categories'

export type DrillsTableProps = {
  drillMatrixData: DrillsMatrixData
  onRowPress: (userId?: string) => void
  onColumnPress: (drillId?: string) => void
  onCellPress: (userId?: string, drillId?: string) => void
}

export const DrillsTable = ({ drillMatrixData, onRowPress, onColumnPress, onCellPress }: DrillsTableProps) => {
  const columns = useMemo<SeaTableColumn<UserDrillsData>[]>(() => {
    return buildColumns(drillMatrixData, onColumnPress, onCellPress)
  }, [drillMatrixData, onColumnPress, onCellPress])

  const rows = useMemo<SeaTableRow<UserDrillsData>[]>(() => {
    return buildRows(drillMatrixData, onRowPress)
  }, [drillMatrixData, onRowPress])

  return <SeaTable scrollable columns={columns} rows={rows} />
}

function buildColumns(
  drillMatrixData: DrillsMatrixData,
  onColumnPress: (drillId?: string) => void,
  onCellPress: (userId?: string, drillId?: string) => void
) {
  return [
    {
      label: 'Name',
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
      render: x => (
        <SeaTypography variant={'body'} fontWeight={'bold'}>
          {renderFullName(x.user)}
        </SeaTypography>
      ),
      width: 180,
    },
    ...drillMatrixData.drills.map(drill => ({
      label: drill.name,
      widthPercentage: 0.9,
      onPress: () => onColumnPress(drill.id),
      render: (x: UserDrillsData, isCompactView?: boolean) => {
        const cellDrill = x.drillsData[drill.id]

        return (
          <Pressable
            onPress={() => onCellPress(x.user.id, drill.id)}
            style={{
              display: 'flex',
              flexDirection: 'row',
              width: '100%',
              flex: 1,
            }}>
            <SeaStack direction={'row'} align={'center'} justify={'between'} style={{ flex: 1 }} gap={5}>
              {isCompactView && (
                <SeaTypography numberOfLines={2} variant={'value'} containerStyle={{ flex: 1 }}>
                  {drill.name}
                </SeaTypography>
              )}
              <TrainingCell numberOfDays={cellDrill?.daysRemaining} status={cellDrill.status} />
            </SeaStack>
          </Pressable>
        )
      },
      width: 120,
    })),
  ]
}

function buildRows(drillMatrixData: DrillsMatrixData, onRowPress: (userId?: string) => void) {
  return drillMatrixData.users.map(userDrillsData => {
    return {
      data: userDrillsData,
      status: userDrillsData.hasOverdue ? SeaStatusType.Error : undefined,
      onPress: (x: UserDrillsData) => onRowPress(x.user.id),
    }
  })
}
