import React, { useCallback, useMemo } from 'react'
import { CompactRowPosition, SeaTable, SeaTableColumn, SeaTableRow } from '@src/components/_atoms/SeaTable/SeaTable'
import { SafetyEquipmentItem } from '@src/shared-state/VesselSafety/safetyEquipmentItems'
import { View } from 'react-native'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { formatValue } from '@src/lib/util'
import { formatDateShort, getDateDifferenceInDays, warnDays } from '@src/lib/datesAndTime'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { colors } from '@src/theme/colors'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { SeaStatusType } from '@src/types/Common'
import { sharedState } from '@src/shared-state/shared-state'
import { DateTime } from 'luxon'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import {
  SeaTableIconCalendar,
  SeaTableIconFlag,
  SeaTableIconLocation,
  SeaTableIconMail,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

interface SafetyEquipmentExpiriesTableProps {
  items: SafetyEquipmentItem[]
  vesselSafetyItems: CategoriesData
  vesselLocations: CategoriesData
  onSelect: (selectedItem: SafetyEquipmentItem) => void
  showGrouped?: boolean
}
export const SafetyEquipmentExpiriesTable = ({
  items,
  vesselSafetyItems,
  vesselLocations,
  onSelect,
  showGrouped,
}: SafetyEquipmentExpiriesTableProps) => {
  const { isTabletWidth } = useDeviceWidth()

  const checkIsCritical = useCallback((id: string) => !!vesselSafetyItems?.byId[id]?.isCritical, [vesselSafetyItems])

  const columns = useMemo(
    () => buildColumns(vesselSafetyItems, vesselLocations, checkIsCritical, isTabletWidth),
    [vesselSafetyItems, vesselLocations, checkIsCritical, isTabletWidth]
  )

  const rows = useMemo(() => buildRows(items, onSelect), [items])

  return (
    <View style={{ marginTop: 16 }}>
      <SeaTable
        columns={columns}
        rows={rows}
        showGroupedTable={showGrouped}
        sortFunction={(a, b) => {
          return renderCategoryName(b.itemId, vesselSafetyItems).localeCompare(
            renderCategoryName(a.itemId, vesselSafetyItems)
          )
        }}
      />
    </View>
  )
}

const buildColumns = (
  vesselSafetyItems: CategoriesData,
  vesselLocations: CategoriesData,
  checkIsCritical: (id: string) => boolean,
  isTabletWidth: boolean
): SeaTableColumn<SafetyEquipmentItem>[] => {
  return [
    {
      label: '',
      width: 60, // Hardcode as it is thumbnail column
      render: x => <SeaTableImage files={x.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Safety Item',
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
      widthPercentage: 0.2,
      render: x => (
        <SeaTypography variant={'body'} fontWeight={'bold'}>
          {renderCategoryName(x.itemId, vesselSafetyItems)}
        </SeaTypography>
      ),
    },
    {
      label: 'Location',
      isHidden: isTabletWidth,
      compactModeOptions: {
        hideRow: true,
      },
      value: x => formatValue(renderCategoryName(x.locationId, vesselLocations)),
      icon: () => <SeaTableIconLocation />,
    },
    {
      label: 'Last Service',
      compactModeOptions: {
        hideRow: true,
      },
      value: x => (x.whenLastChecked ? formatDateShort(x.whenLastChecked) : ''),
      icon: x => x.whenLastChecked && <SeaTableIconCalendar />,
    },
    {
      label: 'Service / Expiry',
      value: x => (x.dateDue ? formatDateShort(x.dateDue) : ''),
      icon: x => x.dateDue && <SeaTableIconCalendar />,
      compactModeOptions: {
        label: {
          show: true,
        },
      },
    },
    {
      label: 'Status',
      compactModeOptions: {
        rowPosition: CompactRowPosition.TopRightCorner,
      },
      render: (x, isCompactView?: boolean) =>
        x.type !== 'nonExpiring' &&
        x.dateDue && (
          <WhenDueStatus
            whenDue={x.dateDue}
            warnDaysThreshold={warnDays.safetyEquipmentExpiries[0]}
            hasFault={x.hasFault ?? false}
            compact={isCompactView}
          />
        ),
    },
    {
      label: 'Critical',
      compactModeOptions: {
        rowPosition: CompactRowPosition.BottomRightCorner,
      },
      render: x => <>{checkIsCritical(x.itemId) ? <SeaTableIconFlag /> : null}</>,
      widthPercentage: 0.1,
    },
    {
      label: 'Reminder',
      isHidden: isTabletWidth,
      compactModeOptions: {
        hideRow: true,
      },
      render: x => <>{x.emailReminder ? <SeaTableIconMail /> : null}</>,
    },
  ]
}
const buildRows = (
  items: SafetyEquipmentItem[],
  onPress: (item: SafetyEquipmentItem) => void
): SeaTableRow<SafetyEquipmentItem>[] => {
  return items.map(item => {
    return {
      data: item,
      status: item.hasFault ? SeaStatusType.Critical : getRowStatus(item.dateDue),
      onPress: item => onPress(item),
      group: data => data.type + ' equipment',
    }
  })
}

const getRowStatus = (dateDue?: string): SeaStatusType => {
  if (!dateDue) {
    return SeaStatusType.Ok
  }
  const whenDueDiff = getDateDifferenceInDays(DateTime.fromISO(dateDue), sharedState.todayMillis.current)

  if (whenDueDiff < 0) return SeaStatusType.Error
  if (whenDueDiff > 0 && whenDueDiff < warnDays.safetyEquipmentExpiries[0]) return SeaStatusType.Warning

  return SeaStatusType.Ok
}
