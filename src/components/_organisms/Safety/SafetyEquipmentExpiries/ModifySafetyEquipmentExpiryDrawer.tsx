import React, { useMemo, useState } from 'react'
import { <PERSON>er<PERSON><PERSON>, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SafetyEquipmentItem } from '@src/shared-state/VesselSafety/safetyEquipmentItems'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useFormik } from 'formik'
import {
  ADD_NEW_DROPDOWN_VALUE,
  AddNewDropdownItem,
  SeaDropdown,
  SeaDropdownItem,
} from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useServiceContainer, useLogger } from '@src/providers/ServiceProvider'
import {
  CreateSafetyEquipmentExpiryDto,
  CreateSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/CreateSafetyEquipmentExpiryUseCase'
import { formatEmailReminder } from '@src/lib/datesAndTime'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime } from 'luxon'
import { SeaIntervalDropdown } from '@src/components/_atoms/SeaIntervalDropdown/SeaIntervalDropdown'
import {
  UpdateSafetyEquipmentExpiryDto,
  UpdateSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/UpdateSafetyEquipmentExpiryUseCase'
import Yup from '@src/lib/yup'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'

import { SeaFile } from '@src/lib/fileImports'
import { HALF_WIDTH_DESKTOP, DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const validationSchema = Yup.object({
  safetyItemId: Yup.string().required(),
  expiryDate: Yup.date().when('type', {
    is: 'expiring',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  type: Yup.string().required(),
  interval: Yup.string().when('type', {
    is: 'servicable',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  description: Yup.string().max(5000),
  assignedTo: Yup.string(),
  isCritical: Yup.boolean(),
  newSafetyItemName: Yup.string().when('safetyItemId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
  newLocationName: Yup.string().when('locationId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
})

export interface ModifySafetyEquipmentExpiryDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: SafetyEquipmentItem
  mode: DrawerMode
  isCritical?: boolean
}

export const ModifySafetyEquipmentExpiryDrawer = ({
  selectedItem,
  visible,
  onClose,
  mode,
  isCritical = false,
}: ModifySafetyEquipmentExpiryDrawerProps) => {
  const drawerTitle = mode === DrawerMode.Create ? 'Add Safety Equipment Expiry' : 'Update Safety Equipment Expiry'

  const logger = useLogger(`ModifySafetyEquipmentExpiryDrawer:${mode}`)

  const initialValues = {
    safetyItemId: selectedItem?.itemId ?? '',
    locationId: selectedItem?.locationId ?? '',
    isCritical: isCritical,
    type: selectedItem?.type ?? '',
    quantity: selectedItem?.quantity ?? '',

    interval: selectedItem?.interval ?? '',
    lastCheck: selectedItem?.whenLastChecked ?? DateTime.now().toMillis(),

    expiryDate: selectedItem?.dateDue ?? DateTime.now().toISODate(),

    serviceTask: selectedItem?.description ?? '',
    emailReminder: selectedItem?.emailReminder ?? '',
    newSafetyItemName: '',
    newLocationName: '',
  }
  const [files, setFiles] = useState<SeaFile[]>([])

  const serviceContainer = useServiceContainer()
  const createSafetyEquipmentExpiryUseCase = serviceContainer.get(CreateSafetyEquipmentExpiryUseCase)
  const updateSafetyEquipmentExpiryUseCase = serviceContainer.get(UpdateSafetyEquipmentExpiryUseCase)

  const licenseeId = sharedState.licenseeId.use()
  const vesselId = sharedState.vesselId.use()
  const userId = sharedState.userId.use()

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched, dirty } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  // Safety Item Options
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const safetyItemOptions = useMemo(() => {
    if (!vesselSafetyItems) return []

    const result = Object.entries(vesselSafetyItems.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })

    return [AddNewDropdownItem, ...result]
  }, [vesselSafetyItems])

  const isNewSafetyItem = useMemo(() => values.safetyItemId === ADD_NEW_DROPDOWN_VALUE, [values.safetyItemId])

  // Location Options
  const vesselLocations = sharedState.vesselLocations.use()
  const vesselLocationOptions = useMemo<SeaDropdownItem<string>[]>(() => {
    if (!vesselLocations) return []

    const result = Object.entries(vesselLocations.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })

    return [AddNewDropdownItem, ...result]
  }, [vesselLocations])

  const isNewLocationItem = useMemo(() => values.locationId === ADD_NEW_DROPDOWN_VALUE, [values.locationId])

  const expiryTypeOptions = [
    { label: 'Servicable Equipment', value: 'servicable' },
    { label: 'Expiring Equipment', value: 'expiring' },
    { label: 'Non-Expiring Equipment', value: 'nonExpiring' },
  ]

  const emailReminderOptions = [
    { label: formatEmailReminder('0d'), value: '0d' },
    { label: formatEmailReminder('1d'), value: '1d' },
    { label: formatEmailReminder('2d'), value: '2d' },
    { label: formatEmailReminder('3d'), value: '3d' },
    { label: formatEmailReminder('7d'), value: '7d' },
    { label: formatEmailReminder('14d'), value: '14d' },
    { label: formatEmailReminder('1m'), value: '1m' },
  ]

  const executeCreate = (vals: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const dto: CreateSafetyEquipmentExpiryDto = {
      vesselId: vesselId,
      safetyItemId: isNewSafetyItem ? undefined : vals.safetyItemId,
      isNewSafetyItem: isNewSafetyItem,
      newSafetyItemName: isNewSafetyItem ? vals.newSafetyItemName : undefined,
      type: vals.type,
      isCritical: vals.isCritical,
      locationId: isNewLocationItem ? undefined : vals.locationId,
      isNewLocation: isNewLocationItem,
      newLocationName: isNewLocationItem ? vals.newLocationName : undefined,
      quantity: vals.quantity,

      interval: vals.interval,
      lastCheck: vals.type === 'servicable' ? vals.lastCheck : undefined,
      description: vals.serviceTask,
      expiryDate: vals.type === 'expiring' ? vals.expiryDate : '',
      emailReminder: vals.emailReminder,

      files: files,
    }

    logger.debug('Creating Safety Equipment Expiry with dto', { dto })

    createSafetyEquipmentExpiryUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Equipment Expiry created successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error creating Safety Equipment Expiry\n ${err.message}`, err))
  }

  const executeUpdate = (vals: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const docId = selectedItem?.id
    if (!docId) {
      throw new Error('Cannot execute Update without Doc ID')
    }

    const dto: UpdateSafetyEquipmentExpiryDto = {
      docId: docId,
      vesselId: vesselId,
      safetyItemId: isNewSafetyItem ? undefined : vals.safetyItemId,
      isNewSafetyItem: isNewSafetyItem,
      newSafetyItemName: isNewSafetyItem ? vals.newSafetyItemName : undefined,
      type: vals.type,
      isCritical: vals.isCritical,
      locationId: isNewLocationItem ? undefined : vals.locationId,
      isNewLocation: isNewLocationItem,
      newLocationName: isNewLocationItem ? vals.newLocationName : undefined,
      quantity: vals.quantity,

      interval: vals.interval,
      lastCheck: vals.type === 'servicable' ? vals.lastCheck : undefined,
      description: vals.serviceTask,
      expiryDate: vals.type === 'expiring' ? vals.expiryDate : '',
      emailReminder: vals.emailReminder,

      files: files,
    }

    logger.debug('Updating Safety Equipment Expiry with dto', { dto })

    updateSafetyEquipmentExpiryUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Equipment Expiry updated successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error updating Safety Equipment Expiry\n ${err.message}`, err))
  }

  const doSubmit = (vals: typeof initialValues) => {
    console.log('Submitting')
    if (!userId || !vesselId || !licenseeId) {
      throw new Error('Unable to create Safety Equipment Expiry: Missing User ID, Vessel ID, or Licensee ID')
    }

    mode === DrawerMode.Create
      ? executeCreate(vals, vesselId, userId, licenseeId)
      : executeUpdate(vals, vesselId, userId, licenseeId)
  }

  return (
    <SeaDrawer
      title={drawerTitle}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      isDirty={dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Safety Equipment Expiry'}
          edit={{ onSubmit: handleSubmit }}
          create={{ onSubmit: handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <DrawerColumn>
            <SeaDropdown
              items={safetyItemOptions}
              value={values.safetyItemId}
              onSelect={handleChange('safetyItemId')}
              label={'Safety Item'}
              showIcon={true}
              style={{ flex: 1, width: '100%' }}
              hasError={Boolean(errors.safetyItemId)}
              errorText={errors.safetyItemId}
            />
            {isNewSafetyItem && (
              <SeaTextInput
                value={values.newSafetyItemName}
                onChangeText={handleChange('newSafetyItemName')}
                placeholder="Enter new safety item name"
                hasError={Boolean(errors.newSafetyItemName) && touched.newSafetyItemName}
                errorText={errors.newSafetyItemName}
                style={{ flex: 1, width: '100%' }}
              />
            )}
          </DrawerColumn>
          <DrawerColumn>
            <SeaDropdown
              items={vesselLocationOptions}
              value={values.locationId}
              onSelect={handleChange('locationId')}
              label={'Location'}
              showIcon={true}
              labelIconOptions={{ icon: 'location_on' }}
              style={{ flex: 1, width: '100%' }}
            />

            {isNewLocationItem && (
              <SeaTextInput
                value={values.newLocationName}
                onChangeText={handleChange('newLocationName')}
                placeholder="Enter new location"
                hasError={Boolean(errors.newLocationName) && touched.newLocationName}
                errorText={errors.newLocationName}
                style={{ flex: 1, width: '100%' }}
              />
            )}
          </DrawerColumn>
        </DrawerRow>

        <DrawerRow>
          <SeaCheckbox
            heading={'Critical Equipment'}
            label={'This equipment is critical'}
            showIcon={true}
            value={values.isCritical}
            onChange={x => setFieldValue('isCritical', x)}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaDropdown
            items={expiryTypeOptions}
            value={values.type}
            onSelect={handleChange('type')}
            label={'Type'}
            showIcon={true}
            style={{ width: '100%', flex: 1 }}
            hasError={Boolean(errors.type)}
            errorText={errors.type}
            disabled={mode === DrawerMode.Edit}
          />
          <SeaTextInput
            label={'Quantity'}
            showIcon={true}
            value={values.quantity}
            onChangeText={handleChange('quantity')}
            style={{ width: '100%', flex: 1 }}
          />
        </DrawerRow>

        {(values.type === 'servicable' || values.type === 'expiring') && (
          <DrawerContent>
            {values.type === 'servicable' && (
              <DrawerRow>
                <DrawerColumn>
                  <SeaIntervalDropdown
                    value={values.interval}
                    onSelect={handleChange('interval')}
                    label={'Interval'}
                    showIcon={true}
                    hasError={Boolean(errors.interval)}
                    errorText={errors.interval}
                    style={{ width: '100%', flex: 1 }}
                  />
                </DrawerColumn>
                <DrawerColumn>
                  {/**
                   * Intentionally hiding this when editing the record
                   */}
                  {mode === DrawerMode.Create && (
                    <SeaDateTimeInput
                      label={'Last Check'}
                      showIcon={true}
                      type={'date'}
                      value={DateTime.fromMillis(values.lastCheck)}
                      onChange={date => setFieldValue('lastCheck', date.toMillis())}
                      style={{ width: '100%', flex: 1 }}
                    />
                  )}
                </DrawerColumn>
              </DrawerRow>
            )}
            {values.type === 'expiring' && (
              <DrawerRow width={HALF_WIDTH_DESKTOP}>
                <SeaDateTimeInput
                  value={DateTime.now()}
                  onChange={date => {
                    setFieldValue('expiryDate', date.toISODate())
                  }}
                  type={'date'}
                  label={'Expiry Date'}
                  showIcon={true}
                  style={{ width: '100%', flex: 1 }}
                  disabled={mode === DrawerMode.Edit}
                />
              </DrawerRow>
            )}
            <DrawerRow>
              <SeaTextInput
                multiLine
                label={'Service Task'}
                showIcon={true}
                value={values.serviceTask}
                onChangeText={handleChange('serviceTask')}
              />
            </DrawerRow>

            <DrawerRow width={HALF_WIDTH_DESKTOP}>
              {/** TODO: Use SeaEmailReminderDropdown */}
              <SeaDropdown
                items={emailReminderOptions}
                value={values.emailReminder}
                onSelect={handleChange('emailReminder')}
                label={'Set Email Reminder'}
                labelIconOptions={{ icon: 'update' }}
                showIcon={true}
                style={{ width: '100%', flex: 1 }}
              />
            </DrawerRow>
          </DrawerContent>
        )}

        <DrawerRow>
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
