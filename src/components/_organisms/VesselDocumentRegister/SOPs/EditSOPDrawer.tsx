import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { FormikValues, useFormik } from 'formik'
import { formatSeaDate } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'

import { makeDateTime } from '@src/lib/util'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import Yup, { notTooOld } from '@src/lib/yup'

import { SeaFile } from '@src/lib/fileImports'
import { Text } from 'react-native'
import { SOP } from '@src/shared-state/VesselDocuments/vesselSOPS'
import { DateTime } from 'luxon'
import { UpdateSOPDto, UpdateSOPUseCase } from '@src/domain/use-cases/vesselDocumentRegister/UpdateSOPUseCase'
import { CreateSOPDto, CreateSOPUseCase } from '@src/domain/use-cases/vesselDocumentRegister/CreateSOPUseCase'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

enum DocumentationType {
  Controlled = 'sfdoc',
  ExternalFiles = 'files',
}

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  dateIssued: Yup.date()
    .required()
    .min(...notTooOld),
  categoryId: Yup.string().max(500).required(),
})

export interface EditSOPDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: SOP
  mode?: DrawerMode
}
export function EditSOPDrawer({ selectedItem, visible, onClose, mode = DrawerMode.Edit, style }: EditSOPDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselSopCategories = sharedState.vesselSOPCategories.use(visible)

  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const categoriesOptions = useMemo(() => {
    if (!vesselSopCategories) return []

    const categories = vesselSopCategories?.ids.map(id => {
      const category = vesselSopCategories.byId[id]
      return {
        label: category.name,
        value: category.id,
      }
    })

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...(categories ?? []),
    ]
  }, [vesselSopCategories])

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      dateIssued: selectedItem?.dateIssued ?? DateTime.now().toISODate(),
      categoryId: selectedItem?.categoryId ?? '',
      documentationType:
        selectedItem?.files && selectedItem.files.length > 0
          ? DocumentationType.ExternalFiles
          : DocumentationType.Controlled,
      files: selectedItem?.files ?? [],
      sfdoc: selectedItem?.sfdoc ?? {},
    }
  }, [selectedItem])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      const commonDto = {
        vesselId,
        title: values.title,
        dateIssued: formatSeaDate(values.dateIssued) ?? undefined,
        categoryId: values.categoryId,
        files: files,
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateSOPDto = {
          ...commonDto,
        }
        const createSOPDocument = services.get(CreateSOPUseCase)
        createSOPDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating SOP\n ${err.message}`))
      } else {
        const dto: UpdateSOPDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateSOP = services.get(UpdateSOPUseCase)

        updateSOP
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating SOP\n ${err.message}`))
      }
    },
    [vesselId, licenseeId, userId, vessel, files, mode, services, onClose, selectedItem?.id]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Standard Operating Procedure' : 'Edit Standard Operating Procedure'}
      visible={visible}
      onClose={onClose}
      style={style}
      isDirty={formik.dirty}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Standard Operating Procedure'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'SOP Title'}
            showIcon={true}
            value={formik.values.title}
            onChangeText={formik.handleChange('title')}
            errorText={formik.errors.title ?? undefined}
            hasError={!!formik.errors.title}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaDropdown
            label={'Category'}
            showIcon={true}
            items={categoriesOptions}
            style={{
              flex: 1,
              width: '100%',
            }}
            onSelect={value => formik.setFieldValue('categoryId', value)}
            value={formik.values.categoryId}
          />

          <SeaDateTimeInput
            value={makeDateTime(formik.values.dateIssued)}
            onChange={date => formik.setFieldValue('dateIssued', date)}
            type={'date'}
            label="Issue Date"
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
            errorText={formik.errors.dateIssued ?? undefined}
            hasError={!!formik.errors.dateIssued}
          />
        </DrawerRow>

        <SeaDropdown
          label={'Documentation'}
          showIcon={true}
          items={[
            {
              label: 'Controlled',
              value: DocumentationType.Controlled,
            },
            {
              label: 'External File(s)',
              value: DocumentationType.ExternalFiles,
            },
          ]}
          style={{
            width: '100%',
          }}
          onSelect={value => formik.setFieldValue('documentationType', value)}
          value={formik.values.documentationType}
        />
        {formik.values.documentationType === DocumentationType.ExternalFiles ? (
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        ) : (
          <Text>TODO Rich text editor</Text>
        )}
      </DrawerContent>

      {/* TODO: Add Links */}
    </SeaDrawer>
  )
}
