import React, { useCallback, useMemo, useState } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'

import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'

import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { VesselCertificate } from '@src/shared-state/VesselDocuments/vesselCertificates'
import { FormikValues, useFormik } from 'formik'
import { formatSeaDate, subtractInterval } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'

import { makeDateTime } from '@src/lib/util'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  RenewVesselCertificateDto,
  RenewVesselCertificateUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/RenewCertificateUseCase'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaFile } from '@src/lib/fileImports'
import Yup, { notTooOld } from '@src/lib/yup'

const validationSchema = Yup.object({
  dateIssued: Yup.date()
    .required()
    .min(...notTooOld),
  dateExpires: Yup.date()
    .required()
    .min(...notTooOld),
  certNum: Yup.string().max(500),
})
export interface EditVesselCertificateDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: VesselCertificate
}
export function RenewVesselCertificateDrawer({
  selectedItem,
  visible,
  onClose,
  style,
}: EditVesselCertificateDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const router = useRouter()
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      certNum: selectedItem?.certNum ?? '',
      dateIssued: formatSeaDate(),
      dateExpires: '',
    }
  }, [selectedItem])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel || !selectedItem) {
        console.error('Vessel Certificate, Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (selectedItem.type === 'renewable' && values.dateExpires && selectedItem.emailReminder) {
        dateToRemind = subtractInterval(values.dateExpires, selectedItem.emailReminder).toISODate() ?? undefined
      }

      const commonDto = {
        vesselId,
        title: selectedItem.title,
        certNum: values.certNum ?? undefined,
        issuedBy: selectedItem.issuedBy,
        dateIssued: formatSeaDate(values.dateIssued) ?? '',
        dateExpires: formatSeaDate(values.dateExpires) ?? undefined,
        emailReminder: selectedItem.emailReminder,
        categoryId: selectedItem.categoryId,
        type: selectedItem.type,
        dateToRemind,
        files: files,
        isShoreFacility: vessel?.isShoreFacility ? true : undefined,
      }

      const dto: RenewVesselCertificateDto = {
        ...commonDto,
        id: selectedItem.id,
      }

      const renewVesselCertificate = services.get(RenewVesselCertificateUseCase)

      renewVesselCertificate
        .execute(dto, userId, licenseeId)
        .then(() => {
          onClose()
          router.navigate({
            pathname: getRoutePath(Routes.VESSEL_CERTIFICATES),
            params: {
              id: vessel.id,
            },
          })
        })
        .catch(err => console.error(`Error renewing Vessel Certificate\n ${err.message}`))
    },
    [vesselId, licenseeId, userId, vessel, selectedItem, files, services, onClose, router]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched, dirty } = formik

  return (
    <SeaDrawer
      title={`Renew ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'}: ${selectedItem?.title}`}
      visible={visible}
      onClose={onClose}
      style={style}
      isDirty={dirty}
      primaryAction={
        <SeaButton
          label={'Renew Certificate'}
          variant={SeaButtonVariant.Primary}
          key="Renew Certificate"
          onPress={formik.handleSubmit}
        />
      }>
      <SeaStack direction="column" gap={10} justify="start" align="start">
        <SeaDateTimeInput
          value={makeDateTime(formik.values.dateIssued)}
          onChange={date => formik.setFieldValue('dateIssued', date)}
          type={'date'}
          label="Issue Date"
          style={{ flex: 1 }}
          hasError={Boolean(errors.dateIssued && touched.dateIssued)}
          errorText={errors.dateIssued}
        />

        <SeaDateTimeInput
          value={makeDateTime(formik.values.dateExpires)}
          onChange={date => formik.setFieldValue('dateExpires', date)}
          type={'date'}
          label="New Expiry Date"
          style={{ flex: 1 }}
          hasError={Boolean(errors.dateExpires && touched.dateExpires)}
          errorText={errors.dateExpires}
        />

        <SeaTextInput
          label={`New ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'} #`}
          value={formik.values.certNum}
          onChangeText={formik.handleChange('certNum')}
          hasError={Boolean(errors.certNum && touched.certNum)}
          errorText={errors.certNum}
        />

        <SeaFileUploader initialFiles={[]} files={files} setFiles={setFiles} />
      </SeaStack>
    </SeaDrawer>
  )
}
