import { useHealthAndSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { CorrectiveActions } from '@src/pages/HealthSafety/CorrectiveActions'

export default function CorrectiveActionsPage() {
  const vesselId = sharedState.vesselId.use()
  return <CorrectiveActions headerSubNavigation={useHealthAndSafetySubNav(vesselId, Routes.CORRECTIVE_ACTION)} />
}
