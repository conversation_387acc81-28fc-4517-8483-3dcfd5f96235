import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { ViewCorrectiveAction } from '@src/components/_organisms/HealthSafety/CorrectiveActions/ViewCorrectiveAction'

const CorrectiveActionViewPage = () => {
  const { correctiveActionId } = useLocalSearchParams()

  const correctiveActions = sharedState.correctiveActions.use()
  const correctiveAction = correctiveActions?.byId[correctiveActionId as string]

  return <ViewCorrectiveAction correctiveAction={correctiveAction} />
}

export default CorrectiveActionViewPage
