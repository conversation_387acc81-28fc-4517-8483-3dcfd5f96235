import { View } from 'react-native'
import React, { useMemo, useState } from 'react'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { useCompanyDocumentsSubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { formatMonthDayTime } from '@src/lib/datesAndTime'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { VesselsData } from '@src/shared-state/Core/vessels'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { EditCustomFormDrawer } from '@src/components/_organisms/CompanyDocumentRegister/CustomForm/EditCustomFormDrawer'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import {
  SeaTableIconCalendar,
  SeaTableIconPerson,
  SeaTableIconVessel,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

export function CustomForms() {
  const vessel = sharedState.vessel.use()
  const vessels = sharedState.vessels.use()
  const customFormCategories = sharedState.customFormCategories.use()
  const customForms = sharedState.customForms.use()
  const [isVisibleCreateDrawer, setIsVisibleCreateDrawer] = useState(false)

  const router = useRouter()
  const { styles } = useStyles(styleSheet)

  const data = useMemo(() => {
    if (!customForms) return []

    return customForms.all
  }, [customForms])

  const handlePress = (item: CustomForm) => {
    router.navigate({
      pathname: getRoutePath(Routes.CUSTOM_FORMS_VIEW),
      params: {
        vesselId: vessel?.id,
        formId: item.id,
      },
    })
  }

  const columns = useMemo(() => buildColumns(vessels), [vessels])
  const rows = useMemo(
    () => buildRows(data, item => handlePress(item), customFormCategories),
    [data, customFormCategories]
  )

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="customForms" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Forms/Checklists" />}
          primaryActionButton={
            <RequirePermissions role="customForms" level={permissionLevels.CREATE}>
              <SeaAddButton
                onPress={() => setIsVisibleCreateDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={'Create'}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <RequirePermissions key={'settings'} role="customForms" level={permissionLevels.EDIT}>
              <SeaSettingsButton onPress={() => alert('This functionality is not completed yet')} />
            </RequirePermissions>,
            <RequirePermissions role="customForms" key={'browse-templates'} level={permissionLevels.CREATE}>
              <SeaButton
                onPress={() =>
                  router.navigate({
                    pathname: getRoutePath(Routes.CUSTOM_FORM_BROWSE_TEMPLATES),
                    params: {
                      vesselId: vessel?.id,
                    },
                  })
                }
                variant={SeaButtonVariant.Secondary}
                label={'Browse Templates'}
              />
            </RequirePermissions>,
          ]}
          subNav={useCompanyDocumentsSubNav(vessel?.id, Routes.CUSTOM_FORMS)}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} rows={rows} showGroupedTable sortGroupsByTitle={(a, b) => a.localeCompare(b)} />
        </View>

        {isVisibleCreateDrawer && (
          <EditCustomFormDrawer
            onClose={() => setIsVisibleCreateDrawer(false)}
            visible={isVisibleCreateDrawer}
            mode={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const buildColumns = (vessels?: VesselsData) => {
  const renderForVessels = (row: CustomForm) => {
    if (row.lastCompletedVesselIds && row.lastCompletedVesselIds.length > 0) {
      if (row.lastCompletedVesselIds.length === 1 && vessels?.byId?.[row.lastCompletedVesselIds[0]]) {
        return `, for ${vessels.byId[row.lastCompletedVesselIds[0]].name}`
      }
      return `, for ${row.lastCompletedVesselIds.length} vessels`
    }
    return ''
  }

  const getLinkedToValue = (row: CustomForm) => {
    if (row.forVesselIds && row.forVesselIds.length > 0 && row.forVesselIds[0] !== 'none') {
      if (row.forCrew) {
        return 'Vessels & Personnel'
      } else {
        return 'Vessels'
      }
    } else {
      if (row.forCrew) {
        return 'Personnel'
      }
    }
    return ''
  }

  return [
    {
      label: 'Title',
      value: (row: CustomForm) => row.title,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Last Completed',
      icon: () => <SeaTableIconCalendar />,
      value: (row: CustomForm) =>
        row.whenLastCompleted && row.lastCompletedBy
          ? `${formatMonthDayTime(row.whenLastCompleted)}, by ${renderFullNameForUserId(row.lastCompletedBy)}${renderForVessels(row)}`
          : '-',
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
          name: 'Completed',
        },
      },
    },
    {
      label: 'Linked To',
      icon: row => {
        const value = getLinkedToValue(row)
        return (
          <>
            {value.includes('Vessels') && <SeaTableIconVessel />}
            {value.includes('Personnel') && <SeaTableIconPerson />}
          </>
        )
      },
      value: (row: CustomForm) => getLinkedToValue(row),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Status',
      render: (row: CustomForm) => {
        return row.state === 'draft' ? <SeaStatusPill primaryLabel="Draft" variant={SeaStatusType.Minor} /> : <></>
      },
      width: 80,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
  ] as SeaTableColumn<CustomForm>[]
}

const buildRows = (
  items: CustomForm[],
  onPress: (item: CustomForm) => void,
  companyDocumentCategories?: CategoriesData
) => {
  return items.map(item => ({
    data: item,
    onPress: (item: CustomForm) => onPress(item),
    group: (item: CustomForm) => renderCategoryName(item.categoryId, companyDocumentCategories),
  }))
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))
