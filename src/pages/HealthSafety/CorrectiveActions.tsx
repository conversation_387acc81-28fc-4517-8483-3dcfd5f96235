import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { sharedState } from '@src/shared-state/shared-state'
import { useRouter } from 'expo-router'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { CorrectiveAction } from '@src/shared-state/HealthSafety/correctiveActions'
import { extractSearchTerms, formatValue, makeDateTime } from '@src/lib/util'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { StyleSheet, View } from 'react-native'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'
import { EditCorrectiveActionDrawer } from '@src/components/_organisms/HealthSafety/CorrectiveActions/EditCorrectiveActionDrawer'
import {
  SeaTableIconCalendar,
  SeaTableIconPerson,
  SeaTableIconVessel,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { formatDateShort } from '@src/lib/datesAndTime'
import { renderVesselsList } from '@src/shared-state/Core/vessels'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'

interface CorrectiveActionsProps {
  headerSubNavigation?: SubNav[]
}

export const CorrectiveActions = ({ headerSubNavigation }: CorrectiveActionsProps) => {
  const correctiveActions = sharedState.correctiveActions.use()
  const user = sharedState.user.use()
  const vesselId = sharedState.vesselId.use()
  const vessels = sharedState.vessels.use()
  const divisions = sharedState.divisions.use()
  sharedState.contacts.use() // Prepare for modals to access
  sharedState.vesselLocations.use() // Prepare for modals to access
  sharedState.safetyCheckItems.use() // Prepare for modals to access
  sharedState.spareParts.use() // Prepare for modals to access

  // Hooks
  const router = useRouter()
  const [addDrawerVisible, setAddDrawerVisible] = useState(false)
  /** TODO: History View */
  const [historyModalVisible, setHistoryModalVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])
  const [searchValue, setSearchValue] = useState('')

  useEffect(() => {
    user?.vesselIds && setFilterVesselIds(user?.vesselIds)
  }, [user?.vesselIds])

  const filteredCorrectiveActions = useMemo(() => {
    let allCorrectiveActions: CorrectiveAction[] = []
    const filteredItems: CorrectiveAction[] = []

    // Filter by selected Vessels
    if (filterVesselIds) {
      const hasCorrectiveAction = {} as { [id: string]: true }
      filterVesselIds.forEach(vesselId => {
        correctiveActions?.byVesselId[vesselId]?.forEach(correctiveAction => {
          // Only get the active corrective action
          if (correctiveAction.state === 'active') {
            if (!hasCorrectiveAction[correctiveAction.id]) {
              hasCorrectiveAction[correctiveAction.id] = true
              allCorrectiveActions.push(correctiveAction)
            }
          }
        })
      })
      if (filterVesselIds.length > 1) {
        allCorrectiveActions.sort((a, b) => {
          return a.title.localeCompare(b.title)
        })
      }
    } else {
      // Only get the active corrective actions
      allCorrectiveActions = correctiveActions?.array.active ?? []
    }

    const terms = extractSearchTerms(searchValue, true)
    if (allCorrectiveActions) {
      for (const correctiveAction of allCorrectiveActions) {
        if (terms.length > 0) {
          let isMatch = true
          for (const termsItem of terms) {
            if (correctiveAction.searchText?.includes(termsItem, 0)) {
              isMatch = false
              break
            }
          }
          if (!isMatch) {
            continue
          }
        }
        filteredItems.push(correctiveAction)
      }
    }
    return filteredItems
  }, [correctiveActions, filterVesselIds, searchValue])

  const buildRows = useCallback(
    (items: CorrectiveAction[]) => {
      return items.map(item => {
        return {
          data: item,
          onPress: (item: CorrectiveAction) => {
            return router.navigate({
              pathname: getRoutePath(Routes.CORRECTIVE_ACTION_VIEW),
              params: {
                correctiveActionId: item.id,
              },
            })
          },
        }
      })
    },
    [router]
  )

  const buildColumns = useCallback(() => {
    return [
      {
        label: '',
        width: 60,
        render: item => <SeaTableImage files={item.files} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Corrective Action',
        value: item => formatValue(item.title),
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Action #',
        value: item => formatValue(item.correctiveActionNum),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Assigned To',
        icon: () => <SeaTableIconPerson />,
        value: item => renderFullNameForUserId(item.assignedTo),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
            name: 'Assigned',
          },
        },
      },
      {
        label: 'Tags',
        value: item => item.tags?.join(', '),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Date Added',
        icon: () => <SeaTableIconCalendar />,
        value: item => formatDateShort(item.whenAdded),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Due Date',
        icon: () => <SeaTableIconCalendar />,
        value: item => (item.dateDue ? formatDateShort(item.dateDue) : '-'),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Vessels / Facilities',
        icon: () => <SeaTableIconVessel />,
        value: item => renderVesselsList(item.vesselIds, vessels, divisions),
        compactModeOptions: {},
      },
    ] as SeaTableColumn<CorrectiveAction>[]
  }, [divisions, vessels])

  const newCorrectiveActionData: CorrectiveAction = {
    title: '',
    description: '',
    dateDue: makeDateTime('').toISODate(),
    emailReminder: '',
    assignedTo: '',
    vesselIds: vesselId ? [vesselId] : [],
  }

  return (
    <>
      <ScrollablePageLayout>
        <RequirePermissions
          role="correctiveActions"
          level={permissionLevels.VIEW}
          showDenial={true}
          licenseePermission="hasCorrectiveActions">
          <SeaPageCard
            titleComponent={<SeaPageCardTitle title={'Corrective Actions List'} />}
            primaryActionButton={
              <RequirePermissions key="correctiveActions" role="correctiveActions" level={permissionLevels.CREATE}>
                <SeaAddButton
                  label={'Add New'}
                  variant={SeaButtonVariant.Primary}
                  onPress={() => setAddDrawerVisible(true)}
                />
              </RequirePermissions>
            }
            secondaryActionButton={[
              <RequirePermissions key="correctiveActions" role="correctiveActions" level={permissionLevels.COMPLETE}>
                <SeaCheckButton
                  label={'History'}
                  variant={SeaButtonVariant.Secondary}
                  onPress={() => setHistoryModalVisible(true)}
                />
              </RequirePermissions>,
              <RequirePermissions
                key="correctiveActionsSettings"
                role="correctiveActions"
                level={permissionLevels.EDIT}>
                <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />
              </RequirePermissions>,
            ]}
            subNav={headerSubNavigation}
          />

          {/* Filter Row */}
          <SeaStack direction={'row'} justify={'between'} gap={10}>
            <SeaStack
              style={{
                flexShrink: 1,
              }}>
              <VesselFilterDropdown vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />
            </SeaStack>
            <SeaFilterSearch value={searchValue} onChangeText={setSearchValue} />
          </SeaStack>

          {/* Table View */}
          <View style={styles.tableView}>
            <SeaTable columns={buildColumns()} rows={buildRows(filteredCorrectiveActions)} />
          </View>
        </RequirePermissions>
      </ScrollablePageLayout>
      {addDrawerVisible && (
        <EditCorrectiveActionDrawer
          correctiveAction={newCorrectiveActionData}
          visible={addDrawerVisible}
          onClose={() => setAddDrawerVisible(false)}
          mode={DrawerMode.Create}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  tableView: {
    marginTop: 16,
  },
})
