import React, { useCallback, useMemo, useState } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaFilterTags, SeaFilterTagsValue } from '@src/components/_atoms/SeaFilterTags/SeaFilterTags'
import { formatValue } from '@src/lib/util'
import { formatDateShort, formatShortTimeDurationHrsMinsView } from '@src/lib/datesAndTime'
import { colors } from '@src/theme/colors'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { Job, jobPriorities } from '@src/shared-state/VesselMaintenance/jobs'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaStatusType } from '@src/types/Common'
import { MaintenanceFilters } from '@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters'
import { renderCategoryName } from '@src/lib/categories'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { usePermission } from '@src/hooks/usePermission'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { EditJobListDrawer } from '@src/components/_organisms/Maintenance/JobList/EditJobListDrawer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconFlag,
  SeaTableIconPerson,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'

const priorityOrder: Record<string, number> = {
  Urgent: 5,
  High: 4,
  Medium: 3,
  Low: 2,
  Shipyard: 1,
}

const seaTableColumns = [
  {
    label: '',
    width: 60,
    render: job => <SeaTableImage files={job.files} />,
    compactModeOptions: {
      isThumbnail: true,
    },
  },
  {
    label: 'Task',
    value: job => job.task,
    style: { fontWeight: 'bold' },
    compactModeOptions: {
      rowPosition: CompactRowPosition.Title,
    },
  },
  {
    label: 'Job #',
    value: job => job.jobNum,
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Large,
      },
    },
  },
  {
    label: 'Assigned',
    icon: () => <SeaTableIconPerson />,
    value: job => formatValue(job.assignedTo?.name) + (job.assignedTo?.contactId ? ' (Contact)' : ''),
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Large,
      },
    },
  },
  {
    label: 'Job Tags',
    value: job => {
      formatValue(job.tags?.join(', '))
    },
    compactModeOptions: {
      hideRow: true,
    },
  },
  {
    label: 'Date Added',
    icon: () => <SeaTableIconCalendar />,
    value: job => {
      formatDateShort(job.whenAdded)
    },
    compactModeOptions: {
      hideRow: true,
    },
  },
  {
    label: 'Due Date',
    icon: () => <SeaTableIconCalendar />,
    value: job => (job.dateDue ? formatDateShort(job.dateDue) : '-'),
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Large,
      },
    },
  },
  {
    label: 'Added By',
    icon: () => <SeaTableIconPerson />,
    value: job => formatValue(renderFullNameForUserId(job.addedBy)),
    compactModeOptions: {
      hideRow: true,
    },
  },
  {
    label: 'Critical',
    render: job => <>{job.equipment?.isCritical ? <SeaTableIconFlag color={colors.status.critical} /> : null}</>,
    compactModeOptions: {
      rowPosition: CompactRowPosition.BottomRightCorner,
    },
  },
] as SeaTableColumn<Job>[]

const JobListPermissions = {
  maintenanceSchedule: { level: permissionLevels.CREATE },
}

interface JobListProps {
  headerSubNavigation?: SubNav[]
}

export const JobList = ({ headerSubNavigation }: JobListProps) => {
  const jobs = sharedState.jobs.use()
  const vesselSystems = sharedState.vesselSystems.use()
  const equipment = sharedState.equipment.use()
  const vessel = sharedState.vessel.use()

  const router = useRouter()
  const { mode } = useGlobalSearchParams()

  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<Partial<SeaFilterTagsValue>>({
    all: { isActive: true },
    critical: { isActive: false },
  })
  const [searchValue, setSearchValue] = useState('')
  const [systemFilter, setSystemFilter] = useState('')
  const [equipmentFilter, setEquipmentFilter] = useState('')
  const [assignedToFilter, setAssignedToFilter] = useState('')
  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(mode === 'add')

  const { hasTimeTrackingEnabled } = useLicenseeSettings()
  const modulePermissions = usePermission<typeof JobListPermissions>({
    modules: JobListPermissions,
  })

  const updatedColumns = useMemo(() => {
    const previousColumnIndex = seaTableColumns.findIndex(col => col.label === 'Added By')
    if (hasTimeTrackingEnabled) {
      seaTableColumns.splice(previousColumnIndex + 1, 0, {
        label: 'Estimated time',
        value: job => formatValue(job.estimatedTime ? formatShortTimeDurationHrsMinsView(job.estimatedTime) : '-'),
        compactModeOptions: {
          hideRow: true,
        },
      })
    }

    return seaTableColumns
  }, [hasTimeTrackingEnabled])

  const allData = useMemo(() => {
    //TOODO: Add pagination
    const data = jobs?.prioritised

    setSeaFilterTagsValue(prev => ({
      ...prev,
      all: { count: data?.length ?? 0, isActive: prev.all?.isActive ?? false },
    }))

    return data
  }, [jobs])

  const criticalData = useMemo(() => {
    const data = jobs?.prioritised.filter(job => job.equipment?.isCritical)

    setSeaFilterTagsValue(prev => ({
      ...prev,
      critical: {
        count: data?.length ?? 0,
        isActive: prev.critical?.isActive ?? false,
      },
    }))

    return data
  }, [jobs])

  // Filter categories]
  const filteredTasks = useMemo(() => {
    if (!jobs?.prioritised) return undefined

    let filteredJobs: Job[] = []

    if (seaFilterTagsValue.all?.isActive) {
      filteredJobs = allData ?? []
    } else {
      filteredJobs = criticalData ?? []
    }

    // Filter by system
    if (systemFilter) {
      filteredJobs = filteredJobs.filter(job => job.equipment?.systemId === systemFilter)
    }

    // Filter by equipment
    if (equipmentFilter) {
      filteredJobs = filteredJobs.filter(job => job.equipment?.id === equipmentFilter)
    }

    //Filter by assigned to
    if (assignedToFilter) {
      filteredJobs = filteredJobs.filter(job => job.assignedTo?.name === assignedToFilter)
    }

    // Filter by search
    if (searchValue) {
      filteredJobs = filteredJobs.filter(job => {
        const taskName = job.task?.toLowerCase() ?? ''
        const assignedToName = job.assignedTo?.name?.toLowerCase() ?? ''
        const addedByName = renderFullNameForUserId(job.addedBy).toLowerCase() ?? ''

        return (
          taskName.includes(searchValue.toLowerCase()) ||
          assignedToName.includes(searchValue.toLowerCase()) ||
          addedByName.includes(searchValue.toLowerCase())
        )
      })
    }

    return filteredJobs
  }, [jobs, seaFilterTagsValue, systemFilter, equipmentFilter, assignedToFilter, searchValue, allData, criticalData])

  const systemFilterFilterOptions = useMemo(() => {
    if (!jobs?.filterOptions) return []

    const options = jobs.filterOptions.systemIds.map((id: string) => ({
      label: renderCategoryName(id, vesselSystems),
      value: id,
    }))

    return [
      {
        label: 'All',
        value: '',
      },
      ...options,
    ]
  }, [jobs, vesselSystems])

  const equipmentFilterFilterOptions = useMemo(() => {
    if (!jobs?.filterOptions) return []

    const options = jobs.filterOptions.equipmentIds.map((id: string) => ({
      label: equipment?.byId[id].equipment ?? '',
      value: id,
    }))

    const filteredOptions = options.filter(option => {
      if (systemFilter) {
        const _equipment = equipment?.byId[option.value]
        return _equipment?.systemId === systemFilter
      }
      return true
    })

    return [
      {
        label: 'All',
        value: '',
      },
      ...filteredOptions,
    ]
  }, [equipment, jobs, systemFilter])

  const assignedToFilterOptions = useMemo(() => {
    if (!jobs?.filterOptions.assignedTo) return []

    const options = jobs.filterOptions.assignedTo.map((option: string) => ({
      label: option,
      value: option,
    }))

    return [
      {
        label: 'All',
        value: '',
      },
      ...options,
    ]
  }, [jobs])

  const handleRow = useCallback(
    (item: Job) => {
      router.navigate({
        pathname: getRoutePath(Routes.JOBLIST_VIEW),
        params: {
          vesselId: vessel?.id,
          jobId: item.id,
        },
      })
    },
    [router, vessel]
  )

  const rows = useMemo(() => buildRows(filteredTasks ?? [], (item: Job) => handleRow(item)), [filteredTasks, handleRow])

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="jobList" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Job List" />}
          primaryActionButton={
            modulePermissions.maintenanceSchedule ? (
              <SeaButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
                iconOptions={{ icon: 'add' }}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />,
          ]}
          subNav={headerSubNavigation}
        />

        <View
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}>
          <ScrollView
            horizontal
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              paddingVertical: 10,
            }}
            showsHorizontalScrollIndicator={false}>
            <SeaStack direction="row" justify="between" gap={10} style={{ width: '100%' }}>
              {/* Filter Tags */}
              <SeaFilterTags value={seaFilterTagsValue} onChange={value => setSeaFilterTagsValue(value)} />

              {/* Filter Row */}
              <MaintenanceFilters
                searchFilter={{
                  searchValue: searchValue,
                  setSearchValue: setSearchValue,
                }}
                systemFilter={{
                  value: systemFilter,
                  setValue: setSystemFilter,
                  options: systemFilterFilterOptions,
                }}
                equipmentFilter={{
                  value: equipmentFilter,
                  setValue: setEquipmentFilter,
                  options: equipmentFilterFilterOptions,
                }}
                assignedToFilter={{
                  value: assignedToFilter,
                  setValue: setAssignedToFilter,
                  options: assignedToFilterOptions,
                }}
                setSeaFilterTagsValue={() =>
                  setSeaFilterTagsValue({
                    all: { isActive: true },
                    critical: { isActive: false },
                  })
                }
              />
            </SeaStack>
          </ScrollView>
        </View>

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable
            columns={updatedColumns}
            showGroupedTable={true}
            rows={rows}
            sortGroupsByTitle={sortGroupsByTitle}
          />
        </View>
        {isVisibleAddDrawer && (
          <EditJobListDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            mode={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const buildRows = (items: Job[], handleRow: (item: Job) => void) => {
  return items.map(item => ({
    data: item,
    status: getStatus(item),
    onPress: (item: Job) => handleRow(item),
    group: (item: Job) => jobPriorities[item.priority as keyof typeof jobPriorities],
  }))
}

const getStatus = (item: Job) => {
  switch (item.priority) {
    case '8urgent':
      return SeaStatusType.Critical
    case '6high':
      return SeaStatusType.Error
    case '4medium':
      return SeaStatusType.Warning
    case '2low':
    case '0shipyard':
      return SeaStatusType.Ok
  }

  return SeaStatusType.Ok
}

const sortGroupsByTitle = (a: string, b: string) => {
  return (priorityOrder[b] ?? 0) - (priorityOrder[a] ?? 0)
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
})
