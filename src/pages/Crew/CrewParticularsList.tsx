import React, { useMemo, useState } from 'react'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ScrollView, View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { UserType } from '@src/shared-state/Core/user'
import { renderFullName } from '@src/shared-state/Core/users'
import { sharedState } from '@src/shared-state/shared-state'
import { extractSearchTerms, formatValue } from '@src/lib/util'
import { formatDateShort } from '@src/lib/datesAndTime'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { usePermission } from '@src/hooks/usePermission'
import { SeaTableIconCalendar } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { useCrewSubNav } from '@src/hooks/useSubNav'

interface CrewParticularsListProps {
  vesselId?: string
  visible: boolean
}

const CrewPermisions = {
  crewParticulars: { level: permissionLevels.CREATE },
}

const FullCrewPermisions = {
  crewParticulars: { level: permissionLevels.FULL },
}

export const CrewParticularsList = ({ vesselId, visible }: CrewParticularsListProps) => {
  // Styles
  const { styles } = useStyles(styleSheet)

  // Shared Data
  const vesselIds = sharedState.vesselIds.use(visible)
  const users = sharedState.users.use(visible)
  const userDetails = sharedState.userDetails.use(visible)

  // Hooks
  const router = useRouter()
  const fullPermission = usePermission<typeof FullCrewPermisions>({
    modules: FullCrewPermisions,
  })
  const createPermission = usePermission<typeof CrewPermisions>({
    modules: CrewPermisions,
  })

  const [operationalCrewOnly, setOperationalCrewOnly] = useState(false)

  const filteredUsers = useMemo(() => {
    // TODO: CHANGE TO USE Hook
    const searchText = ''
    // TODO: CHANGE TO USE Hook
    const terms = extractSearchTerms(searchText, true)

    const getProcessedCrewRecords = (crews: UserType[] = []) => {
      const checkMatchedTerms = (crewId: string | undefined) => {
        if (terms.length > 0 && crewId) {
          const _userDetails = userDetails?.byId[crewId]
          return terms.some(_term => {
            return _userDetails?.searchText?.includes(_term)
          })
        }

        return true
      }

      const filteredCrew: UserType[] = crews.filter(crewListItem => {
        if (operationalCrewOnly && crewListItem.crewVesselIds) {
          return crewListItem.crewVesselIds.some(
            _vId => vesselIds?.includes(_vId) && checkMatchedTerms(crewListItem?.id)
            // TODO: CHANGE TO USE Hook
            // filterByVesselId[_vId] && checkMatchedTerms(crewListItem?.id),
            // TODO: CHANGE TO USE Hook
          )
        }

        if (crewListItem.vesselIds) {
          return crewListItem.vesselIds.some(
            _vId => vesselIds?.includes(_vId) && checkMatchedTerms(crewListItem?.id)
            // TODO: CHANGE TO USE Hook
            // filterByVesselId[_vId] && checkMatchedTerms(crewListItem?.id),
            // TODO: CHANGE TO USE Hook
          )
        }

        return false
      })

      return filteredCrew
    }

    return getProcessedCrewRecords(users?.staff)

    // TODO: Once we re-do the filters
    // return {
    //   staff: users?.staff ? getProcessedCrewRecords(users.staff) : [],
    //   nonStaff: users?.nonStaff ? getProcessedCrewRecords(users.nonStaff) : [],
    // };
  }, [
    // searchText,
    users,
    operationalCrewOnly,
    // filterByVesselId,
    userDetails?.byId,
  ])

  /**
   * Get the columns for the Table
   *
   * @return SeaTableColumn<>[]
   */
  const buildColumns = (): SeaTableColumn<UserType>[] => {
    return [
      {
        label: 'Name',
        render: item => (
          <>
            <SeaTypography variant={'value'} textStyle={{ fontWeight: 700 }}>
              {renderFullName(item)}
            </SeaTypography>
            {item.isLicensee && (
              <SeaTypography variant={'label'} color={'red'} containerStyle={{ marginBottom: 0 }}>
                LICENSEE
              </SeaTypography>
            )}
          </>
        ),
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Email',
        value: item => (item.id ? (userDetails?.byId[item.id]?.email ?? '') : ''),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      {
        label: 'Job Title / Position',
        value: item => formatValue(item.position),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
            name: 'Position',
          },
        },
      },
      {
        label: 'Inducted Date',
        icon: () => <SeaTableIconCalendar />,
        value: item => formatDateShort(item.dateInducted),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Login Access',
        render: item =>
          item.isLoginDisabled ? (
            <SeaTypography variant={'value'} color={'red'}>
              N
            </SeaTypography>
          ) : (
            <SeaTypography variant={'value'}>Y</SeaTypography>
          ),
        compactModeOptions: {
          hideRow: true,
        },
      },
    ]
  }

  /**
   * Get the rows for the Table
   *
   * @param items - the filtered list of risks
   * @return SeaTableRow<UserType>[]
   */
  const buildRows = (items: UserType[]): SeaTableRow<UserType>[] => {
    return items.map(item => {
      return {
        data: item,
        onPress: (item: UserType) => {
          return router.navigate({
            pathname: getRoutePath(Routes.CREW_PARTICULARS_VIEW_PROFILE),
            params: {
              crewId: item.id,
              vesselId: vesselId,
            },
          })
        },
      }
    })
  }

  return (
    <RequirePermissions role="jobList" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Crew" />}
          primaryActionButton={
            createPermission.crewParticulars ? (
              <SeaButton
                onPress={() => alert('This functionality is not completed yet')}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
                iconOptions={{ icon: 'add' }}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />,
            fullPermission.crewParticulars ? (
              <SeaButton
                key={'archive'}
                onPress={() => alert('This functionality is not completed yet')}
                variant={SeaButtonVariant.Tertiary}
                label={'Archived Users'}
              />
            ) : (
              <></>
            ),
          ]}
          subNav={useCrewSubNav(vesselId, Routes.CREW_PARTICULARS_LIST)}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          {/* TODO: When we re-do the filters */}
          {/*/!* Filter Tags *!/*/}
          {/*<SeaFilterTags*/}
          {/*  value={seaFilterTagsValue}*/}
          {/*  onChange={(value) => setSeaFilterTagsValue(value)}*/}
          {/*/>*/}

          <SeaTable columns={buildColumns()} rows={buildRows(filteredUsers ?? [])} />
        </View>
      </ScrollView>
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
}))
