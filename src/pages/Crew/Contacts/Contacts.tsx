import React, { useMemo } from 'react'
import { StyleSheet, View } from 'react-native'
import { ContactsTable } from '@src/components/_organisms/Crew/Contacts/ContactsTable'
import { sharedState } from '@src/shared-state/shared-state'
import { ModifyContactDrawer } from '@src/components/_organisms/Crew/Contacts/ModifyContactDrawer'
import { Contact } from '@src/shared-state/Crew/contacts'
import { useLogger } from '@src/providers/ServiceProvider'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { usePathname, useRouter } from 'expo-router'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { useCrewSubNav } from '@src/hooks/useSubNav'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'

export const Contacts: React.FC = () => {
  // Hooks
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()
  const vesselId = sharedState.vesselId.use()
  const pathname = usePathname()

  const logger = useLogger('ViewContacts', { userId, licenseeId, pathname })

  // Shared State
  const contacts = sharedState.contacts.use()
  const contactCategories = sharedState.contactCategories.use()

  // Local State
  const [modifyContactDrawerVisible, setModifyContactDrawerVisible] = React.useState(false)

  const filteredContacts = useMemo<Contact[]>(() => {
    return contacts?.all ?? []
  }, [contacts])

  const router = useRouter()

  const handleSelectContact = (selectedContactId: string) => {
    logger.info(`Selected Contact with ID: [${selectedContactId}]`, { selectedContactId })
    return router.navigate({
      pathname: getRoutePath(Routes.CREW_CONTACTS_VIEW),
      params: {
        contactId: selectedContactId,
        vesselId: vesselId,
      },
    })
  }

  return (
    <>
      <ScrollablePageLayout>
        <RequirePermissions role={'contacts'} level={permissionLevels.VIEW} showDenial={true}>
          <SeaPageCard
            titleComponent={<SeaPageCardTitle title="Contacts / Suppliers" />}
            primaryActionButton={
              <RequirePermissions role={'contacts'} level={permissionLevels.CREATE} showDenial={false}>
                <SeaButton
                  onPress={() => {
                    logger.debug('Opening Add Contact Drawer')
                    setModifyContactDrawerVisible(true)
                  }}
                  variant={SeaButtonVariant.Primary}
                  label={'Add New'}
                  iconOptions={{ icon: 'add' }}
                />
              </RequirePermissions>
            }
            secondaryActionButton={[
              <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
              <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />,
            ]}
            subNav={useCrewSubNav(vesselId, Routes.CREW_CONTACTS)}
          />

          {/* Table View */}
          <View style={styles.tableView}>
            {contacts?.all && contactCategories && (
              <ContactsTable
                contacts={filteredContacts}
                contactCategories={contactCategories}
                onContactSelect={selectedContactId => handleSelectContact(selectedContactId)}
              />
            )}
          </View>
        </RequirePermissions>
      </ScrollablePageLayout>
      {/* Drawers */}
      {modifyContactDrawerVisible && (
        <ModifyContactDrawer
          mode={DrawerMode.Create}
          visible={modifyContactDrawerVisible}
          onClose={() => setModifyContactDrawerVisible(false)}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  tableView: {
    marginTop: 16,
  },
})
