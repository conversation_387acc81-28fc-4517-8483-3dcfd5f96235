import React, { useCallback, useMemo } from 'react'
import { StyleSheet } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import { renderCategoryName } from '@src/lib/categories'
import { ModifyContactDrawer } from '@src/components/_organisms/Crew/Contacts/ModifyContactDrawer'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { usePathname, useRouter } from 'expo-router'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { DeleteContactDto, DeleteContactUseCase } from '@src/domain/use-cases/crew/DeleteContactUseCase'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'

export interface ViewContactProps {
  contactId: string
}

export const ViewContact = ({ contactId }: ViewContactProps) => {
  // Hooks
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()
  const pathname = usePathname()
  const router = useRouter()

  const logger = useLogger('ViewContact', { userId, licenseeId, pathname })
  const serviceContainer = useServiceContainer()

  // Shared State
  const contacts = sharedState.contacts.use()
  const contactCategories = sharedState.contactCategories.use()

  // Local State
  const [modifyContactDrawerVisible, setModifyContactDrawerVisible] = React.useState(false)

  const contact = useMemo(() => {
    if (!contacts?.byId || !contactId) return undefined
    return contacts.byId[contactId]
  }, [contacts, contactId])

  const categoryName = useMemo(() => {
    if (!contact?.categoryId || !contactCategories) return 'Uncategorized'
    return renderCategoryName(contact.categoryId, contactCategories)
  }, [contact, contactCategories])

  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const handleDelete = () => {
    if (!userId || !licenseeId || !contactId || !contact) {
      throw new Error('Cannot delete contact due to missing data')
    }
    const deleteContactUseCase = serviceContainer.get(DeleteContactUseCase)

    const dto: DeleteContactDto = {
      contactId: contactId,
      contactName: contact.name,
    }

    deleteContactUseCase
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.CREW_CONTACTS),
        })
      )
      .catch(err => console.error(`Error deleting safety check\n ${err.message}`))
  }

  const onDelete = useCallback(async () => {
    logger.debug("Clicked 'Delete' secondary action")
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  if (!contact) {
    return <SeaLoadingSpinner />
  }

  return (
    <>
      <ScrollablePageLayout>
        <RequirePermissions role={'contacts'} level={permissionLevels.VIEW} showDenial={true}>
          <SeaPageCard
            titleComponent={<SeaPageCardTitle title={contact.name} />}
            primaryActionButton={
              <RequirePermissions role={'contacts'} level={permissionLevels.EDIT} showDenial={false}>
                <SeaButton
                  onPress={() => {
                    logger.debug('Opening Edit Contact Drawer')
                    setModifyContactDrawerVisible(true)
                  }}
                  variant={SeaButtonVariant.Primary}
                  label={'Edit'}
                  iconOptions={{ icon: 'edit' }}
                />
              </RequirePermissions>
            }
            secondaryActionButton={[
              <RequirePermissions key={'delete'} role={'contacts'} level={permissionLevels.FULL} showDenial={false}>
                <SeaDeleteButton key={'delete'} onPress={onDelete} />
              </RequirePermissions>,
            ]}>
            <SeaPageCardContentSection>
              <SeaStack
                direction={isLargeDesktopWidth ? 'row' : 'column'}
                gap={10}
                justify="start"
                align="start"
                width={'100%'}
                style={{ flex: 1 }}>
                <SeaStack
                  direction="column"
                  gap={isDesktopWidth ? 20 : 10}
                  align={'start'}
                  width={isLargeDesktopWidth ? '70%' : '100%'}>
                  <SeaStack direction="column" align={'start'} width={'100%'} gap={isDesktopWidth ? 5 : 0}>
                    <SeaStack isCollapsible={true} width={'100%'} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        iconOptions={{ icon: 'business' }}
                        showIcon={true}
                        label={'Company'}
                        value={contact.company || 'Not specified'}
                      />
                      <SeaLabelValue
                        iconOptions={{ icon: 'person' }}
                        showIcon={true}
                        label={'Contact Name'}
                        value={contact.name}
                      />
                    </SeaStack>

                    {(contact.number ?? contact.email) && (
                      <SeaStack isCollapsible={true} width={'100%'} gap={isDesktopWidth ? 5 : 0}>
                        {contact.number && (
                          <SeaLabelValue
                            iconOptions={{ icon: 'call' }}
                            showIcon={true}
                            label={'Contact Number'}
                            value={contact.number}
                          />
                        )}
                        {contact.email && (
                          <SeaLabelValue
                            iconOptions={{ icon: 'email' }}
                            showIcon={true}
                            label={'Email'}
                            value={contact.email}
                          />
                        )}
                      </SeaStack>
                    )}

                    <SeaStack isCollapsible={true} width={'100%'} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        iconOptions={{ icon: 'category' }}
                        showIcon={true}
                        label={'Category'}
                        value={categoryName}
                      />
                      {contact.vendorNumber && (
                        <SeaLabelValue
                          iconOptions={{ icon: 'numbers' }}
                          showIcon={true}
                          label={'Vendor Number'}
                          value={contact.vendorNumber}
                        />
                      )}
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaPageCardContentSection>

            <SeaEmptyDivider />

            <SeaPageCardContentSection>
              <SeaStack
                direction={isLargeDesktopWidth ? 'row' : 'column'}
                gap={10}
                justify="start"
                align="start"
                width={'100%'}
                style={{ flex: 1 }}>
                <SeaStack
                  direction="column"
                  gap={isDesktopWidth ? 20 : 10}
                  align={'start'}
                  width={isLargeDesktopWidth ? '70%' : '100%'}>
                  <SeaStack isCollapsible={true} width={'100%'} direction={'row'} gap={isDesktopWidth ? 5 : 0}>
                    {contact.address && (
                      <SeaStack isCollapsible={true} width={'100%'} gap={isDesktopWidth ? 5 : 0}>
                        <SeaLabelValue
                          iconOptions={{ icon: 'location_on' }}
                          showIcon={true}
                          label={'Address'}
                          value={contact.address}
                          layout={'vertical'}
                        />
                      </SeaStack>
                    )}

                    {contact.notes && (
                      <SeaStack isCollapsible={true} width={'100%'} gap={isDesktopWidth ? 5 : 0}>
                        <SeaLabelValue
                          iconOptions={{ icon: 'note' }}
                          showIcon={true}
                          label={'Notes'}
                          value={contact.notes}
                          layout={'vertical'}
                        />
                      </SeaStack>
                    )}
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaPageCardContentSection>
          </SeaPageCard>
        </RequirePermissions>
      </ScrollablePageLayout>

      {/* Drawers */}
      {modifyContactDrawerVisible && contact && (
        <ModifyContactDrawer
          mode={DrawerMode.Edit}
          contactId={contact.id}
          visible={modifyContactDrawerVisible}
          onClose={() => setModifyContactDrawerVisible(false)}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({})
