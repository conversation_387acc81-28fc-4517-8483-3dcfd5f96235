import { View, Text } from 'react-native'
import React, { useMemo, useState } from 'react'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { useRouter } from 'expo-router'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { sharedState } from '@src/shared-state/shared-state'
import { SurveyReport } from '@src/shared-state/VesselDocuments/vesselSurveyReports'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { formatDateShort, formatYear } from '@src/lib/datesAndTime'
import { formatValue } from '@src/lib/util'
import { EditSurveyDocumentsDrawer } from '@src/components/_organisms/VesselDocumentRegister/SurveyDocuments/EditSurveyDocumentsDrawer'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconLocation,
  SeaTableIconPerson,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'

interface SurveyDocumentsProps {
  headerSubNavigation?: SubNav[]
}

export function SurveyDocuments({ headerSubNavigation }: SurveyDocumentsProps) {
  const vessel = sharedState.vessel.use()
  const reportItems = sharedState.vesselSurveyReports.use()

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false)

  const { styles } = useStyles(styleSheet)
  const router = useRouter()

  const data = useMemo(() => {
    if (!reportItems) return []

    const allOptions = reportItems.categories.reduce((acc, categoryId) => {
      if (!reportItems.data[categoryId]) return acc
      const data = reportItems.data[categoryId]

      acc.push(...data)
      return acc
    }, [] as SurveyReport[])

    return allOptions
  }, [reportItems])

  const handlePress = (item: SurveyReport) => {
    router.navigate({
      pathname: getRoutePath(Routes.SURVEY_DOCUMENTS_VIEW),
      params: {
        vesselId: vessel?.id,
        documentId: item.id,
      },
    })
  }

  const columns = useMemo(() => buildColumns(), [vessel])
  const rows = useMemo(() => buildRows(data, item => handlePress(item)), [data])

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="survey" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Survey Documents" />}
          primaryActionButton={
            <RequirePermissions role="survey" level={permissionLevels.CREATE}>
              <SeaAddButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton key="export" onPress={() => alert('This functionality is not completed yet')} />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable
            columns={columns}
            rows={rows}
            showGroupedTable
            sortGroupsByTitle={(a: string, b: string) => Number(b) - Number(a)}
          />
        </View>

        {isVisibleAddDrawer && (
          <EditSurveyDocumentsDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            mode={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const buildColumns = () => {
  return [
    {
      label: '',
      width: 60,
      render: row => <SeaTableImage files={row.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Title',
      value: (row: SurveyReport) => row.title,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Date',
      icon: () => <SeaTableIconCalendar />,
      value: (row: SurveyReport) => formatDateShort(row.dateSurveyed),
      width: 120,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Surveyor',
      icon: () => <SeaTableIconPerson />,
      value: (row: SurveyReport) => formatValue(row.surveyor),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Present',
      icon: () => <SeaTableIconPerson />,
      value: (row: SurveyReport) => formatValue(row.personnelPresent),
      compactModeOptions: {
        hideRow: true,
      },
    },
    {
      label: 'Location',
      icon: () => <SeaTableIconLocation />,
      value: (row: SurveyReport) => formatValue(row.location),
      compactModeOptions: {
        hideRow: true,
      },
    },
    {
      label: 'In / Out Water',
      value: (row: SurveyReport) => formatValue(row.inOrOutWater),
      compactModeOptions: {
        hideRow: true,
      },
    },
  ] as SeaTableColumn<SurveyReport>[]
}

const buildRows = (items: SurveyReport[], onPress: (item: SurveyReport) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: SurveyReport) => onPress(item),
    group: (item: SurveyReport) => formatYear(item.dateSurveyed),
  }))
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))
