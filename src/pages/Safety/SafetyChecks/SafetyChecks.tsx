import React, { useCallback, useMemo, useState } from 'react'
import { ScrollView, StyleSheet, Text, TouchableOpacity, useWindowDimensions, View } from 'react-native'
import { SafetyChecksTable } from '@src/components/_organisms/Safety/SafetyChecks/SafetyChecksTable'
import { sharedState } from '@src/shared-state/shared-state'
import { extractSearchTerms, formatInterval } from '@src/lib/util'
import { renderCategoryName } from '@src/lib/categories'
import { formatDate, warnDays } from '@src/lib/datesAndTime'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { ModifySafetyCheckDrawer } from '@src/components/_organisms/Safety/SafetyChecks/ModifySafetyCheckDrawer'
import { SeaFilterTags, SeaFilterTagsValue } from '@src/components/_atoms/SeaFilterTags/SeaFilterTags'
import { SafetyCheckItem } from '@src/shared-state/VesselSafety/safetyCheckItems'
import { useLogger } from '@src/providers/ServiceProvider'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { usePathname, useRouter } from 'expo-router'
import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { useStatusFilter } from '@src/hooks/useStatusFilter'
import { debounce } from 'lodash'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaLookaheadSelector } from '@src/components/_atoms/SeaLookaheadSelector/SeaLookaheadSelector'
import { useLookaheadFilter } from '@src/hooks/useLookaheadFilter'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import _ from 'lodash'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaActionButton } from '@src/components/_atoms/SeaActionButton/SeaActionButton'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'

interface SafetyChecksProps {
  vesselId: string
  visible: boolean
  headerSubNavigation?: SubNav[]
}

export const SafetyChecks: React.FC<SafetyChecksProps> = ({ headerSubNavigation }) => {
  // Hooks
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()
  const pathname = usePathname()

  const logger = useLogger('ViewSafetyChecks', { userId, licenseeId, pathname })

  const { isMobileWidth, isTabletWidth } = useDeviceWidth()
  const { width } = useWindowDimensions()
  const { theme, styles } = useStyles(styleSheet)

  // Shared State
  const safetyCheckItems = sharedState.safetyCheckItems.use()
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const vesselSafetyCheckCategories = sharedState.safetyCheckCategories.use()
  const vesselId = sharedState.vesselId.use()

  // Local State
  const [modifySafetyCheckDrawerVisible, setModifySafetyCheckDrawerVisible] = React.useState(false)

  const [isMoreFiltersRowVisible, setIsMoreFiltersRowVisible] = React.useState(false)
  const [isCriticalFilterActive, setIsCriticalFilterActive] = React.useState(false)

  // Toggles for which mutually exclusive filter is active
  // Status and Lookhead cannot be used together
  const [isStatusFilterActive, setIsStatusFilterActive] = React.useState(true)
  const [isLookaheadFilterActive, setIsLookaheadFilterActive] = React.useState(false)

  const [seaFilterTagsValue, setSeaFilterTagsValue] = React.useState<Partial<SeaFilterTagsValue>>({
    all: { isActive: false },
    overdue: { isActive: true },
    upcoming: { isActive: true },
  })

  const [lookaheadValue, setLookaheadValue] = useState<string | undefined>()
  const lookaheadFilteredValues = useLookaheadFilter({
    items: safetyCheckItems?.all ?? [],
    getDateField: item => item.dateDue,
    lookaheadValue,
  })

  const [searchInputValue, setSearchInputValue] = useState('')
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('')

  // Create debounced search handler
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value)
    }, 300), // 300ms delay
    []
  )

  // Update search handler
  const handleSearchChange = (value: string) => {
    setSearchInputValue(value)
    debouncedSearch(value)

    // Set search as active filter if there's text, otherwise revert to status filter
    if (value.trim()) {
      setIsSearchFilterActive(true)
      setIsStatusFilterActive(false)
      setIsLookaheadFilterActive(false)
      setLookaheadValue(undefined)

      // Deactivate all filter tags when search is active
      setSeaFilterTagsValue({
        all: { isActive: false },
        overdue: { isActive: false },
        upcoming: { isActive: false },
        critical: { isActive: false },
      })
    } else {
      setIsSearchFilterActive(false)
      setIsStatusFilterActive(true)

      // Reset filter tags to default state
      setSeaFilterTagsValue({
        all: { isActive: false },
        overdue: { isActive: true },
        upcoming: { isActive: true },
        critical: { isActive: false },
      })
    }
  }

  // Add a new state to track if search filter is active
  const [isSearchFilterActive, setIsSearchFilterActive] = React.useState(false)

  const handleLookaheadChange = (lookahead?: string) => {
    if (!lookahead) {
      // User has deselected
      setIsLookaheadFilterActive(false)
      setLookaheadValue(undefined)
      setIsSearchFilterActive(false)
      setIsStatusFilterActive(true)
      setSeaFilterTagsValue({
        all: { isActive: false },
        overdue: { isActive: true },
        upcoming: { isActive: true },
        critical: { isActive: false },
      })
      return
    }

    // Clear search when lookahead is selected
    setSearchInputValue('')
    setDebouncedSearchValue('')
    setIsSearchFilterActive(false)

    setIsStatusFilterActive(false)
    setSeaFilterTagsValue({
      all: { isActive: false },
      overdue: { isActive: false },
      upcoming: { isActive: false },
      critical: { isActive: false },
    })

    setIsLookaheadFilterActive(true)
    setLookaheadValue(lookahead)
  }

  // Update filter tags onChange to also clear search
  const handleFilterTagsChange = (value: Partial<SeaFilterTagsValue>) => {
    setIsLookaheadFilterActive(false)
    setLookaheadValue(undefined)
    setIsSearchFilterActive(false)
    setSearchInputValue('')
    setDebouncedSearchValue('')

    setSeaFilterTagsValue({
      all: { isActive: value.all?.isActive ?? false },
      overdue: { isActive: value.overdue?.isActive ?? false },
      upcoming: { isActive: value.upcoming?.isActive ?? false },
      critical: { isActive: value.critical?.isActive ?? false },
    })
  }

  // Memos and Effects
  const { overdue, upcoming } = useStatusFilter(
    safetyCheckItems?.all ?? [],
    (item: SafetyCheckItem) => item.dateDue,
    warnDays.safetyEquipmentChecks[0]
  )

  const criticalItems = useMemo(() => {
    if (!safetyCheckItems?.all || !vesselSafetyItems) return []
    return safetyCheckItems.all.filter(item => vesselSafetyItems?.byId[item.itemId]?.isCritical)
  }, [safetyCheckItems, vesselSafetyItems])

  const filterCounts = useMemo(() => {
    return {
      all: safetyCheckItems?.all.length ?? 0,
      overdue: overdue.length,
      upcoming: upcoming.length,
      critical: criticalItems.length,
    }
  }, [safetyCheckItems, overdue, upcoming, criticalItems])

  const filterExplanationText = useMemo(() => {
    if (isLookaheadFilterActive) {
      return `Showing Overdue Safety Checks and those due within the next ${lookaheadValue}`
    }
    if (isSearchFilterActive) {
      return 'Searching all Safety Checks'
    }
    if (isStatusFilterActive) {
      if (seaFilterTagsValue.all?.isActive) {
        return 'Showing all Safety Checks'
      }

      const activeStatuses = Object.entries(seaFilterTagsValue)
        .filter(([key, value]) => value.isActive)
        .map(([key, value]) => _.startCase(key))
        .join(', ')

      return `Showing ${activeStatuses} Safety Checks`
    }
    return undefined
  }, [isStatusFilterActive, seaFilterTagsValue, isLookaheadFilterActive, lookaheadValue, isSearchFilterActive])

  const combinedFilterValues = useMemo<Partial<SeaFilterTagsValue>>(() => {
    return {
      all: {
        isActive: seaFilterTagsValue.all?.isActive ?? false,
        count: filterCounts.all,
      },
      overdue: {
        isActive: seaFilterTagsValue.overdue?.isActive ?? false,
        count: filterCounts.overdue,
      },
      upcoming: {
        isActive: seaFilterTagsValue.upcoming?.isActive ?? false,
        count: filterCounts.upcoming,
      },
    }
  }, [seaFilterTagsValue, filterCounts])

  const searchFilteredItems = useMemo(() => {
    if (!safetyCheckItems?.all || !debouncedSearchValue) {
      return safetyCheckItems?.all ?? []
    }

    const searchTerms = extractSearchTerms(debouncedSearchValue, true)
    const filteredItems = safetyCheckItems.all.filter(item => {
      const searchableValues = [
        renderCategoryName(item.itemId, vesselSafetyItems), // Name
        renderCategoryName(item.locationId, vesselLocations), // Location
        formatInterval(item.interval), // Interval
        formatDate(item.dateDue), // Next Check
        item.assignedTo?.map(id => renderFullNameForUserId(id))?.join(','), // Assigned To
      ]
      return searchTerms.some(term => searchableValues.some(val => val?.toLowerCase().includes(term)))
    })

    // Sort the filtered items by date due (similar to other filters)
    return filteredItems.sort((a, b) => {
      // Sort by date due (ascending)
      if (a.dateDue && b.dateDue) {
        return a.dateDue.localeCompare(b.dateDue)
      }
      // Items with dateDue come before items without
      if (a.dateDue) return -1
      if (b.dateDue) return 1
      return 0
    })
  }, [safetyCheckItems, debouncedSearchValue, vesselSafetyItems, vesselLocations])

  const filteredChecks = useMemo<SafetyCheckItem[]>(() => {
    if (!searchFilteredItems.length) return []

    // If 'All' is selected, return all search-filtered items
    if (seaFilterTagsValue.all?.isActive) {
      return searchFilteredItems
    }

    // Apply tag filters to search-filtered items
    const filteredOverdue = overdue.filter(item => searchFilteredItems.includes(item))

    const filteredUpcoming = upcoming.filter(item => searchFilteredItems.includes(item))

    const filteredCritical = criticalItems.filter(item => searchFilteredItems.includes(item))

    let result: SafetyCheckItem[] = []

    if (seaFilterTagsValue.overdue?.isActive) {
      result = [...result, ...filteredOverdue]
    }

    if (seaFilterTagsValue.upcoming?.isActive) {
      result = [...result, ...filteredUpcoming]
    }

    // If critical is selected, filter to only critical items
    if (seaFilterTagsValue.critical?.isActive) {
      if (result.length > 0) {
        result = result.filter(item => filteredCritical.includes(item))
      } else {
        result = filteredCritical
      }
    }

    return result
  }, [searchFilteredItems, seaFilterTagsValue, overdue, upcoming, criticalItems])

  const showGroupedChecks = useMemo(() => seaFilterTagsValue.all?.isActive === true, [seaFilterTagsValue])

  const router = useRouter()

  const handleSelectSafetyCheck = (selectedCheckId: string) => {
    logger.info(`Selected Safety Check with ID: [${selectedCheckId}]`, { selectedCheckId })
    return router.navigate({
      pathname: getRoutePath(Routes.SAFETY_EQUIPMENT_CHECKS_VIEW),
      params: {
        vesselId,
        itemId: selectedCheckId,
      },
    })
  }

  const safetyChecksForTable = useMemo(() => {
    let result: SafetyCheckItem[] = []

    if (isLookaheadFilterActive) {
      result = lookaheadFilteredValues
    } else if (isSearchFilterActive) {
      result = searchFilteredItems
    } else {
      result = filteredChecks
    }

    // Apply critical filter if active
    if (isCriticalFilterActive && criticalItems.length > 0) {
      result = result.filter(item => criticalItems.includes(item))
    }

    return result
  }, [
    isLookaheadFilterActive,
    isSearchFilterActive,
    isCriticalFilterActive,
    filteredChecks,
    lookaheadFilteredValues,
    searchFilteredItems,
    criticalItems,
  ])

  return (
    <>
      <ScrollablePageLayout>
        <RequirePermissions role={'safetyEquipmentChecks'} level={permissionLevels.VIEW} showDenial={true}>
          <SeaPageCard
            titleComponent={<SeaPageCardTitle title="Safety Checks" />}
            primaryActionButton={
              <SeaButton
                onPress={() => {
                  logger.debug('Opening Add Safety Check Drawer')
                  setModifySafetyCheckDrawerVisible(true)
                }}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
                iconOptions={{ icon: 'add' }}
              />
            }
            secondaryActionButton={[
              <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
              <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />,
            ]}
            subNav={headerSubNavigation}
          />

          {/* Filter Row */}
          {isMobileWidth ? (
            <SeaStack direction={'column'} gap={10}>
              <SeaStack direction={'row'} gap={10} width={'100%'} style={{ paddingHorizontal: 12 }}>
                <SeaFilterSearch value={searchInputValue} onChangeText={handleSearchChange} style={{ flex: 1 }} />
                <SeaFiltersButton enabled={isCriticalFilterActive} onPress={() => setIsMoreFiltersRowVisible(!isMoreFiltersRowVisible)} />
              </SeaStack>
              <SeaFilterTags
                value={combinedFilterValues}
                onChange={value => {
                  setIsLookaheadFilterActive(false)
                  setLookaheadValue(undefined)

                  setSearchInputValue('')
                  setDebouncedSearchValue('')

                  setIsStatusFilterActive(true)
                  setSeaFilterTagsValue({
                    all: { isActive: value.all?.isActive ?? false },
                    overdue: { isActive: value.overdue?.isActive ?? false },
                    upcoming: {
                      isActive: value.upcoming?.isActive ?? false,
                    },
                    critical: {
                      isActive: value.critical?.isActive ?? false,
                    },
                  })
                }}
                style={{ width: '100%', flex: 1, paddingHorizontal: 12 }}
              />

              <SeaStack
                direction={'row'}
                justify={'start'}
                width={'100%'}
                style={{ paddingHorizontal: 12, height: 40 }}>
                <SeaLookaheadSelector value={lookaheadValue} onChange={lookahead => handleLookaheadChange(lookahead)} />
              </SeaStack>

              {isMoreFiltersRowVisible && (
                <SeaStack
                  direction={'row'}
                  width={'100%'}
                  justify={'start'}
                  style={[styles.filterRow, { marginBottom: 18 }]}>
                  <SeaCriticalFilterButton
                    enabled={isCriticalFilterActive}
                    onToggle={() => setIsCriticalFilterActive(!isCriticalFilterActive)}
                  />
                </SeaStack>
              )}

              {filterExplanationText && (
                <View style={styles.filterTextContainer}>
                  <SeaTypography variant={'label'}>{filterExplanationText}</SeaTypography>
                </View>
              )}
            </SeaStack>
          ) : isTabletWidth || width < 1200 ? (
            <>
              <SeaStack direction={'row'} gap={10} width={'100%'} style={{ paddingHorizontal: 12 }}>
                <SeaFilterSearch value={searchInputValue} onChangeText={handleSearchChange} style={{ flex: 1 }} />
                <SeaFiltersButton enabled={isCriticalFilterActive} onPress={() => setIsMoreFiltersRowVisible(!isMoreFiltersRowVisible)} />
              </SeaStack>
              <SeaStack
                // isCollapsible
                direction={'row'}
                justify={'between'}
                align={'center'}
                gap={10}
                style={styles.filterRow}>
                <SeaStack
                  direction={'row'}
                  style={
                    {
                      //backgroundColor: "red"
                    }
                  }>
                  <SeaFilterTags
                    value={combinedFilterValues}
                    onChange={value => {
                      setIsLookaheadFilterActive(false)
                      setLookaheadValue(undefined)

                      setSearchInputValue('')
                      setDebouncedSearchValue('')

                      setIsStatusFilterActive(true)
                      setSeaFilterTagsValue({
                        all: { isActive: value.all?.isActive ?? false },
                        overdue: { isActive: value.overdue?.isActive ?? false },
                        upcoming: {
                          isActive: value.upcoming?.isActive ?? false,
                        },
                        critical: {
                          isActive: value.critical?.isActive ?? false,
                        },
                      })
                    }}
                  />
                </SeaStack>
                <SeaStack direction={'row'} justify={'start'}>
                  <SeaLookaheadSelector
                    value={lookaheadValue}
                    onChange={lookahead => handleLookaheadChange(lookahead)}
                  />
                </SeaStack>
              </SeaStack>

              {isMoreFiltersRowVisible && (
                <SeaStack
                  direction={'row'}
                  width={'100%'}
                  justify={'start'}
                  style={[styles.filterRow, { marginBottom: 18 }]}>
                  <SeaCriticalFilterButton
                    enabled={isCriticalFilterActive}
                    onToggle={() => setIsCriticalFilterActive(!isCriticalFilterActive)}
                  />
                </SeaStack>
              )}

              {filterExplanationText && (
                <View style={styles.filterTextContainer}>
                  <SeaTypography variant={'label'}>{filterExplanationText}</SeaTypography>
                </View>
              )}
            </>
          ) : (
            <>
              <SeaStack direction={'row'} justify={'between'} align={'center'} gap={10} style={styles.filterRow}>
                <SeaStack direction={'row'} style={{ flex: 1, minWidth: 0 }}>
                  <SeaFilterTags
                    value={combinedFilterValues}
                    onChange={value => {
                      setIsLookaheadFilterActive(false)
                      setLookaheadValue(undefined)

                      setSearchInputValue('')
                      setDebouncedSearchValue('')

                      setIsStatusFilterActive(true)
                      setSeaFilterTagsValue({
                        all: { isActive: value.all?.isActive ?? false },
                        overdue: { isActive: value.overdue?.isActive ?? false },
                        upcoming: {
                          isActive: value.upcoming?.isActive ?? false,
                        },
                        critical: {
                          isActive: value.critical?.isActive ?? false,
                        },
                      })
                    }}
                  />
                  <SeaSpacer width={10} />
                  <View style={{ borderLeftWidth: 3, borderColor: theme.colors.borderColor, width: 1, height: 40 }} />
                  <SeaSpacer width={10} />
                  <SeaStack direction={'row'} justify={'start'}>
                    <SeaLookaheadSelector
                      value={lookaheadValue}
                      onChange={lookahead => handleLookaheadChange(lookahead)}
                    />
                  </SeaStack>
                </SeaStack>

                <SeaStack direction={'row'} style={{ flexShrink: 0 }}>
                  <SeaFilterSearch value={searchInputValue} onChangeText={handleSearchChange} width={300} />
                  <SeaSpacer width={10} />
                  <SeaFiltersButton enabled={isCriticalFilterActive} onPress={() => setIsMoreFiltersRowVisible(!isMoreFiltersRowVisible)} />
                </SeaStack>
              </SeaStack>

              {isMoreFiltersRowVisible && (
                <SeaStack direction={'row'} style={[styles.filterRow, { marginBottom: 18 }]}>
                  <SeaCriticalFilterButton
                    enabled={isCriticalFilterActive}
                    onToggle={() => setIsCriticalFilterActive(!isCriticalFilterActive)}
                  />
                </SeaStack>
              )}

              {filterExplanationText && (
                <View style={styles.filterTextContainer}>
                  <SeaTypography variant={'label'}>{filterExplanationText}</SeaTypography>
                </View>
              )}
            </>
          )}

          {/* Table View */}
          <View style={styles.tableView}>
            {safetyCheckItems?.all && vesselSafetyItems && vesselLocations && (
              <SafetyChecksTable
                showGrouped={showGroupedChecks}
                safetyCheckItems={safetyChecksForTable}
                vesselSafetyItems={vesselSafetyItems}
                vesselLocations={vesselLocations}
                vesselSafetyCheckCategories={vesselSafetyCheckCategories}
                onSafetyCheckSelect={selectedCheckId => handleSelectSafetyCheck(selectedCheckId)}
              />
            )}
          </View>
        </RequirePermissions>
      </ScrollablePageLayout>
      {/* Drawers */}
      {modifySafetyCheckDrawerVisible && (
        <ModifySafetyCheckDrawer
          mode={DrawerMode.Create}
          visible={modifySafetyCheckDrawerVisible}
          onClose={() => setModifySafetyCheckDrawerVisible(false)}
        />
      )}
    </>
  )
}

const styleSheet = createStyleSheet(theme => ({
  page: {
    height: '100%',
  },
  container: {
    height: '100%',
    paddingTop: 0,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  filterTextContainer: {
    paddingHorizontal: 12,
  },
  filterRow: {
    paddingHorizontal: 12,
    // backgroundColor: "magenta",
    // height: 60,
  },
  title: {},
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
}))

// TODO - Need to work out where to put this
export interface SeaFiltersButtonProps {
  onPress: () => void
  enabled?: boolean
}

export const SeaFiltersButton = ({ onPress, enabled = false }: SeaFiltersButtonProps) => {
  const { theme } = useStyles()

  return (
    <SeaButton
      variant={SeaButtonVariant.Tertiary}
      iconOptions={{ icon: 'tune', color: enabled ? '#2883f8' : undefined }}
      viewStyle={{
        backgroundColor: enabled ? '#c4deff' : theme.colors.input.background,
        borderColor: enabled ? 'transparent' : theme.colors.borderColor,
        borderWidth: 1,
      }}
      onPress={onPress}
    />
  )
}

interface SeaCriticalFilterButtonProps {
  enabled: boolean
  onToggle: () => void
}

export const SeaCriticalFilterButton: React.FC<SeaCriticalFilterButtonProps> = ({ enabled, onToggle }) => {
  const { theme } = useStyles()

  const containerStyle = {
    backgroundColor: enabled ? '#c4deff' : theme.colors.white,
    borderWidth: 1,
    borderColor: enabled ? 'transparent' : theme.colors.borderColor,
    display: 'flex' as const,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    cursor: 'pointer' as const,
    minWidth: 60,
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 24,
    height: 40,
  }

  const labelStyle = {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 12,
    color: enabled ? '#2883f8' : theme.colors.text.primary,
    fontWeight: enabled ? ('600' as const) : ('normal' as const),
  }

  return (
    <TouchableOpacity onPress={onToggle} style={containerStyle}>
      <SeaStack direction={'row'} justify={'between'} align={'center'} gap={5}>
        <SeaIcon icon={'flag'} size={16} color={enabled ? '#2883f8' : theme.colors.text.primary} />
        <Text style={labelStyle}>Critical Only</Text>
      </SeaStack>
    </TouchableOpacity>
  )
}
