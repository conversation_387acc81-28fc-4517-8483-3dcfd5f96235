import { inject, injectable } from 'inversify'
import { UpdateCorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionUseCase'
import { IUseCase } from '../../UseCase'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { CorrectiveActionService } from '@src/domain/services/CorrectiveActionService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { LicenseeService } from '@src/domain/services/LicenseeService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface DeleteCorrectiveActionDto extends UpdateCorrectiveActionDto {}

@injectable()
export class DeleteCorrectiveActionUseCase implements IUseCase<DeleteCorrectiveActionDto> {
  private readonly logger: ILogger

  constructor(
    @inject(CorrectiveActionService)
    private readonly correctiveActionService: CorrectiveActionService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(LicenseeService)
    private readonly licenseeService: LicenseeService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('DeleteCorrectiveActionUseCase')
  }

  public async execute(dto: DeleteCorrectiveActionDto, userId: string, licenseeId: string) {
    this.logger.info('Executing DeleteCorrectiveActionUseCase', { dto })

    // Delete Corrective Action
    const operation = this.firestoreService.createOperation({
      operationType: 'delete',
      operationDescription: 'Delete Corrective Action',
      maximumBatchSize: 20,
    })

    const { ref: deleteCorrectiveActionRef, records: correctiveActionRecords } =
      this.correctiveActionService.deleteDataItem(operation, dto.id, userId)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      this.correctiveActionService.collectionName,
      deleteCorrectiveActionRef.id,
      dto.title
    )

    // Touch Licensee
    const { records: licenseeTouchedRecords } = this.licenseeService.touchLicenseeData(
      this.correctiveActionService.collectionName,
      operation,
      licenseeId
    )

    operation.addMany(correctiveActionRecords).add(actionLogRecord).add(licenseeTouchedRecords)

    await operation.commit()
  }
}
