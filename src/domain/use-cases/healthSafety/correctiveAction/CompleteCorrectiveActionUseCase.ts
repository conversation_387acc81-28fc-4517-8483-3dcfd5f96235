import { UpdateCorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionUseCase'
import { BaseCompleteDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { SeaFile } from '@src/lib/fileImports'
import { inject, injectable } from 'inversify'
import { CorrectiveActionService } from '@src/domain/services/CorrectiveActionService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { LicenseeService } from '@src/domain/services/LicenseeService'

export interface CompleteCorrectiveActionDto extends UpdateCorrectiveActionDto, BaseCompleteDto {
  completedNotes?: string
  completedFiles?: SeaFile[]
}

@injectable()
export class CompleteCorrectiveActionUseCase implements IUseCase<CompleteCorrectiveActionDto> {
  private readonly logger: ILogger

  constructor(
    @inject(CorrectiveActionService)
    private readonly correctiveActionService: CorrectiveActionService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService,
    @inject(LicenseeService)
    private readonly licenseeService: LicenseeService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('CompleteCorrectiveActionUseCase')
  }

  public async execute(dto: CompleteCorrectiveActionDto, userId: string, licenseeId: string) {
    this.logger.info('Executing CompleteCorrectiveActionUseCase', { dto })

    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.completedFiles,
      this.correctiveActionService.collectionName,
      'files',
      userId,
      licenseeId
    )
    dto.completedFiles = filesList as SeaFile[]

    // Complete Corrective Action
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Complete Corrective Action',
      maximumBatchSize: 20,
    })

    const { ref: updatedCorrectiveActionRef, records: correctiveActionRecords } =
      this.correctiveActionService.completeCorrectiveAction(operation, dto, userId)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      this.correctiveActionService.collectionName,
      updatedCorrectiveActionRef.id,
      dto.title
    )

    // Touch Licensee
    const { records: licenseeTouchedRecords } = this.licenseeService.touchLicenseeData(
      this.correctiveActionService.collectionName,
      operation,
      licenseeId
    )

    operation.addMany(correctiveActionRecords).add(actionLogRecord).add(licenseeTouchedRecords)

    await operation.commit()
  }
}
