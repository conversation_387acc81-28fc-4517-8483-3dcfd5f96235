import { CorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/CreateCorrectiveActionUseCase'
import { BaseUpdateDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { CorrectiveActionService } from '@src/domain/services/CorrectiveActionService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SeaFile } from '@src/lib/fileImports'
import { NotificationService } from '@src/domain/services/NotificationService'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { LicenseeService } from '@src/domain/services/LicenseeService'

export interface UpdateCorrectiveActionDto extends CorrectiveActionDto, BaseUpdateDto {}

@injectable()
export class UpdateCorrectiveActionUseCase implements IUseCase<UpdateCorrectiveActionDto> {
  private readonly logger: ILogger

  constructor(
    @inject(CorrectiveActionService)
    private readonly correctiveActionService: CorrectiveActionService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(NotificationService)
    private readonly notificationService: NotificationService,
    @inject(FileService)
    private readonly fileService: FileService,
    @inject(LicenseeService)
    private readonly licenseeService: LicenseeService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('UpdateCorrectiveActionUseCase')
  }

  public async execute(
    dto: UpdateCorrectiveActionDto,
    userId: string,
    licenseeId: string,
    emailToUserIds: string[],
    correctiveActionNum: string
  ) {
    this.logger.info('Executing UpdateCorrectiveActionUseCase', { dto })

    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      this.correctiveActionService.collectionName,
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Update Corrective Action
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Corrective Action',
      maximumBatchSize: 20,
    })

    const { ref: updatedCorrectiveActionRef, records: correctiveActionRecords } =
      this.correctiveActionService.updateDataItem(operation, dto, userId)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      this.correctiveActionService.collectionName,
      updatedCorrectiveActionRef.id,
      dto.title
    )

    const vesselNotificationDto = {
      vesselIds: dto.vesselIds,
      type: 'correctiveActionCreated',
      emailMeType: `${this.correctiveActionService.collectionName}Updated`,
      toIds: emailToUserIds,
      files: dto.files,
      metadata: {
        isUpdate: true,
        id: dto.id,
        title: dto.title,
        description: dto.description,
        correctiveActionNum: correctiveActionNum ?? '',
        assignedToName: renderFullNameForUserId(dto.assignedTo) ?? undefined,
        tags: dto.tags.join(', ') ?? undefined,
        dateDue: dto.dateDue ?? undefined,
      },
    }

    const vesselNotificationRecord = this.notificationService.sendVesselNotification(
      operation,
      userId,
      licenseeId,
      vesselNotificationDto
    )

    // Touch Licensee
    const { records: licenseeTouchedRecords } = this.licenseeService.touchLicenseeData(
      this.correctiveActionService.collectionName,
      operation,
      licenseeId
    )

    operation
      .addMany(correctiveActionRecords)
      .add(actionLogRecord)
      .add(vesselNotificationRecord)
      .add(licenseeTouchedRecords)

    await operation.commit()
  }
}
