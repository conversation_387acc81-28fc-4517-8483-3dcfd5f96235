import { Timestamp } from '@react-native-firebase/firestore'
import { BaseCreateDto, BaseDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SeaFile } from '@src/lib/fileImports'
import { inject, injectable } from 'inversify'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { CorrectiveActionService } from '@src/domain/services/CorrectiveActionService'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { NotificationService } from '@src/domain/services/NotificationService'
import { LicenseeService } from '@src/domain/services/LicenseeService'

export interface CorrectiveActionDto extends BaseDto {
  assignedTo?: string
  correctiveActionNum: string
  dateDue: string
  dateToRemind?: string
  description: string
  emailReminder?: string
  files: SeaFile[]
  licenseeId?: string
  state: string
  tags: string[]
  title: string
  touched: Timestamp
  vesselIds: string[]
}

export interface CreateCorrectiveActionDto extends CorrectiveActionDto, BaseCreateDto {}

@injectable()
export class CreateCorrectiveActionUseCase implements IUseCase<CreateCorrectiveActionDto> {
  private readonly logger: ILogger

  constructor(
    @inject(CorrectiveActionService)
    private readonly correctiveActionService: CorrectiveActionService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(NotificationService)
    private readonly notificationService: NotificationService,
    @inject(FileService)
    private readonly fileService: FileService,
    @inject(LicenseeService)
    private readonly licenseeService: LicenseeService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('CreateCorrectiveActionUseCase')
  }

  public async execute(dto: CreateCorrectiveActionDto, userId: string, licenseeId: string, emailToUserIds: string[]) {
    this.logger.info('Executing CreateCorrectiveActionUseCase', { dto })

    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      this.correctiveActionService.collectionName,
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Create Corrective Action
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Corrective Action',
      maximumBatchSize: 20,
    })

    const { ref: createdCorrectiveActionRef, records: correctiveActionRecords } =
      this.correctiveActionService.createDataItem(operation, dto, userId)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      this.correctiveActionService.collectionName,
      createdCorrectiveActionRef.id,
      dto.title
    )

    const vesselNotificationDto = {
      vesselIds: dto.vesselIds,
      type: 'correctiveActionCreated',
      emailMeType: `${this.correctiveActionService.collectionName}Created`,
      toIds: emailToUserIds,
      files: dto.files,
      metadata: {
        id: createdCorrectiveActionRef.id,
        title: dto.title,
        description: dto.description,
        assignedToName: renderFullNameForUserId(dto.assignedTo) ?? undefined,
        tags: dto.tags.join(', ') ?? undefined,
        dateDue: dto.dateDue ?? undefined,
      },
    }

    const vesselNotificationRecord = this.notificationService.sendVesselNotification(
      operation,
      userId,
      licenseeId,
      vesselNotificationDto
    )

    // Touch Licensee
    const { records: licenseeTouchedRecords } = this.licenseeService.touchLicenseeData(
      this.correctiveActionService.collectionName,
      operation,
      licenseeId
    )

    operation
      .addMany(correctiveActionRecords)
      .add(actionLogRecord)
      .add(vesselNotificationRecord)
      .add(licenseeTouchedRecords)

    await operation.commit()
  }
}
