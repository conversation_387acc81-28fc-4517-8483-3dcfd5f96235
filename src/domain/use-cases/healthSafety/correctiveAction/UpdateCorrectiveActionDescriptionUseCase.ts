import { UpdateCorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionUseCase'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { renderFullName, renderFullNameForUserId } from '@src/shared-state/Core/users'
import { inject, injectable } from 'inversify'
import { CorrectiveActionService } from '@src/domain/services/CorrectiveActionService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { NotificationService } from '@src/domain/services/NotificationService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { formatDatetime } from '@src/lib/util'
import { LicenseeService } from '@src/domain/services/LicenseeService'

export interface UpdateCorrectiveActionDescriptionDto extends UpdateCorrectiveActionDto {
  additionalDescription: string
}

@injectable()
export class UpdateCorrectiveActionDescriptionUseCase implements IUseCase<UpdateCorrectiveActionDescriptionDto> {
  private readonly logger: ILogger

  constructor(
    @inject(CorrectiveActionService)
    private readonly correctiveActionService: CorrectiveActionService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(NotificationService)
    private readonly notificationService: NotificationService,
    @inject(LicenseeService)
    private readonly licenseeService: LicenseeService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('UpdateCorrectiveActionDescriptionUseCase')
  }

  public async execute(dto: UpdateCorrectiveActionDescriptionDto, userId: string, licenseeId: string) {
    this.logger.info('Executing UpdateCorrectiveActionDescriptionUseCase', { dto })
    const existingDescription = dto.description ? dto.description.trim() + '\n\n' : ''
    const newDescription = `${existingDescription}${dto.additionalDescription.trim()}\n(${renderFullName()}, ${formatDatetime(null, ', ')})`
    const finalDto = {
      id: dto.id,
      description: newDescription,
    }

    // Update Corrective Action
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Corrective Action Description',
      maximumBatchSize: 20,
    })

    const { ref: updatedCorrectiveActionRef, records: correctiveActionRecords } =
      this.correctiveActionService.updateDataItem(operation, finalDto, userId)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      this.correctiveActionService.collectionName,
      updatedCorrectiveActionRef.id,
      dto.title
    )

    const vesselNotificationDto = {
      vesselIds: dto.vesselIds,
      type: 'correctiveActionCreated',
      emailMeType: `${this.correctiveActionService.collectionName}Updated`,
      files: dto.files,
      metadata: {
        isUpdate: true,
        id: dto.id,
        title: dto.title,
        // Get the description from the new changes instead of the original dto
        description: finalDto.description,
        assignedToName: renderFullNameForUserId(dto.assignedTo) ?? undefined,
        tags: dto.tags.join(', ') ?? undefined,
        dateDue: dto.dateDue ?? undefined,
      },
    }

    const vesselNotificationRecord = this.notificationService.sendVesselNotification(
      operation,
      userId,
      licenseeId,
      vesselNotificationDto
    )

    // Touch Licensee
    const { records: licenseeTouchedRecords } = this.licenseeService.touchLicenseeData(
      this.correctiveActionService.collectionName,
      operation,
      licenseeId
    )

    operation
      .addMany(correctiveActionRecords)
      .add(actionLogRecord)
      .add(vesselNotificationRecord)
      .add(licenseeTouchedRecords)

    await operation.commit()
  }
}
