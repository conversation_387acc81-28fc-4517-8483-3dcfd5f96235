import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { CustomFormService } from '@src/domain/services/CustomFormService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface DeleteCustomFormDto {
  id: string
  title: string
  versionIds?: string[]
}

export interface IDeleteCustomFormUseCase extends IUseCase<DeleteCustomFormDto> {}

@injectable()
export class DeleteCustomFormUseCase implements IDeleteCustomFormUseCase {
  private readonly logger: ILogger
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
    this.logger = logger.scoped('DeleteCustomFormUseCase')
  }

  async execute(dto: DeleteCustomFormDto, userId: string, licenseeId: string) {
    this.logger.info('Executing DeleteCustomFormUseCase Use Case', {
      customForm: dto.id,
      itemName: dto.title,
      userId,
      licenseeId,
    })
    // Delete Vessel Document
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Delete Custom Form ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { title, ...rest } = dto

    const { ref: updateCustomFormRef, records: customFormRecord } = this.customFormService.deleteCustomForm(
      operation,
      rest,
      userId,
      licenseeId
    )

    this.logger.debug('Custom Form Delete Records', { customFormRecord })

    operation.addMany(customFormRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      [],
      'customForms',
      updateCustomFormRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()

    this.logger.info('Successfully deleted custom form', {
      completedCustomFormId: dto.id,
      itemName: dto.title,
    })
  }
}
