import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { IUseCase } from '../UseCase'
import { inject, injectable } from 'inversify'
import { CustomFormService } from '@src/domain/services/CustomFormService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface UpdateCustomFormDto {
  customFormId: string
  title: string
  version: number
  form: Record<string, CustomFormElementType>
  forVessels: boolean
  forVesselIds?: string[]
  vesselsElement?: Partial<CustomFormElementType>
  crewElement?: Partial<CustomFormElementType>
  historyElementN: number
  categoryId?: string
  forCrew?: boolean
  formIsDraft: boolean
}

export interface IUpdateCustomFormUseCase extends IUseCase<UpdateCustomFormDto> {}

@injectable()
export class UpdateCustomFormUseCase implements IUpdateCustomFormUseCase {
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateCustomFormDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Custom Form ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateCustomFormRef, records: updateCustomFromRecord } = this.customFormService.updateCustomForm(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(updateCustomFromRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      [],
      'customForms',
      updateCustomFormRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
