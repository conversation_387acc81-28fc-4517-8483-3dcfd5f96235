import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { CustomFormService } from '@src/domain/services/CustomFormService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface DeleteCompletedCustomFormDto {
  id: string
  customFormVersionId: string
  title: string
  vesselIds?: string[]
  personnelIds?: string[]
  personnel?: string[]
}

export interface IDeleteCompletedCustomFormUseCase extends IUseCase<DeleteCompletedCustomFormDto> {}

@injectable()
export class DeleteCompletedCustomFormUseCase implements IDeleteCompletedCustomFormUseCase {
  private readonly logger: ILogger
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
    this.logger = logger.scoped('DeleteCompletedCustomFormUseCase')
  }

  async execute(dto: DeleteCompletedCustomFormDto, userId: string, licenseeId: string) {
    this.logger.info('Executing DeleteCompletedCustomFormUseCase Use Case', {
      completedCustomFormId: dto.id,
      itemName: dto.title,
      vesselId: dto.vesselIds,
      userId,
      licenseeId,
    })
    // Delete Vessel Document
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Delete Completed Custom Form ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { title, personnelIds, vesselIds, personnel, ...rest } = dto

    const { ref: updateVesselDocumentsRef, records: customFormRecord } =
      this.customFormService.deleteCompletedCustomForm(operation, rest, userId, licenseeId)

    this.logger.debug('Completed Custom Form Delete Records', { customFormRecord })

    operation.addMany(customFormRecord)

    const actionDetails = `${title} - ${personnel ? personnel.join(', ') : ''}`

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      vesselIds ?? [],
      'customFormsCompleted',
      updateVesselDocumentsRef.id,
      actionDetails,
      undefined,
      personnelIds ?? []
    )

    operation.add(actionLogRecord)

    await operation.commit()

    this.logger.info('Successfully deleted completed custom form', {
      completedCustomFormId: dto.id,
      itemName: dto.title,
    })
  }
}
