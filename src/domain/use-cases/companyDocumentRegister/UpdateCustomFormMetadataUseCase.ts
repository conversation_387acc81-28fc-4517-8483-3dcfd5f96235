import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { CreateCustomFormDto } from './CreateCustomFormUseCase'
import { CustomFormService } from '@src/domain/services/CustomFormService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'

export interface UpdateCustomFormMetadataDto
  extends Pick<
    CreateCustomFormDto,
    'title' | 'categoryId' | 'forCrew' | 'forVesselIds' | 'isTemplate' | 'templateCategory' | 'files'
  > {
  id: string
}

export interface IUpdateCustomFormMetadataUseCase extends IUseCase<UpdateCustomFormMetadataDto> {}

@injectable()
export class UpdateCustomFormMetadataUseCase implements IUpdateCustomFormMetadataUseCase {
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateCustomFormMetadataDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Form/Checklist ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: createCustomFormRef, records: createCustomFromRecord } =
      this.customFormService.updateCustomFormMetadata(operation, dto, userId, licenseeId)

    operation.addMany(createCustomFromRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      [],
      'customForms',
      createCustomFormRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
