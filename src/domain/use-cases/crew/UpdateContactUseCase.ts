import { IContactsService, ContactService } from '@src/domain/services/ContactService'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface UpdateContactDto {
  contactId: string
  name: string
  company?: string
  email?: string
  number?: string
  address?: string
  vendorNumber?: string
  categoryId?: string
  notes?: string
  newCategoryName?: string
}

export interface IUpdateContactUseCase extends IUseCase<UpdateContactDto> {}

@injectable()
export class UpdateContactUseCase implements IUpdateContactUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(ContactService)
    private readonly contactsService: IContactsService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('UpdateContactUseCase')
  }

  public async execute(dto: UpdateContactDto, userId: string, licenseeId: string) {
    this.logger.info('Executing UpdateContact Use Case', {
      dto,
      licenseeId,
      userId,
    })

    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Contact',
      maximumBatchSize: 20,
    })

    const { ref: updatedContactRef, records: contactRecords } = this.contactsService.updateContact(
      operation,
      dto,
      userId,
      licenseeId
    )
    this.logger.debug('Updating Contact record', { records: contactRecords })

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      'any',
      'contacts',
      updatedContactRef.id,
      dto.name
    )
    this.logger.info('Creating Action Log Record', { record: actionLogRecord })

    operation.addMany(contactRecords).add(actionLogRecord)

    await operation.commit()

    this.logger.info('Contact updated successfully', {
      contactId: dto.contactId,
      contactName: dto.name,
    })
  }
}
