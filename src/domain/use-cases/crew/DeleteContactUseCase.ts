import { IContactsService, ContactService } from '@src/domain/services/ContactService'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface DeleteContactDto {
  contactId: string
  contactName: string // For logging and action log purposes
}

export interface IDeleteContactUseCase extends IUseCase<DeleteContactDto> {}

@injectable()
export class DeleteContactUseCase implements IDeleteContactUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(ContactService)
    private readonly contactsService: IContactsService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('DeleteContactUseCase')
  }

  public async execute(dto: DeleteContactDto, userId: string, licenseeId: string) {
    this.logger.info('Executing DeleteContact Use Case', {
      contactId: dto.contactId,
      contactName: dto.contactName,
      licenseeId,
    })

    const operation = this.firestoreService.createOperation({
      operationType: 'delete',
      operationDescription: 'Delete Contact',
      maximumBatchSize: 20,
    })

    const { ref: deletedContactRef, records: contactRecords } = this.contactsService.deleteContact(
      operation,
      dto.contactId,
      userId,
      licenseeId
    )

    this.logger.debug('Deleting Contact', { records: contactRecords })

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      'any',
      'contacts',
      deletedContactRef.id,
      dto.contactName
    )

    operation.addMany(contactRecords)
    // .add(actionLogRecord)

    await operation.commit()

    this.logger.info('Contact deleted successfully', {
      contactId: dto.contactId,
      contactName: dto.contactName,
    })
  }
}
