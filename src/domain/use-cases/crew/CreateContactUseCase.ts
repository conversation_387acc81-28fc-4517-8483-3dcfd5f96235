import { IContactsService, ContactService } from '@src/domain/services/ContactService'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface CreateContactDto {
  name: string
  company?: string
  email?: string
  number?: string
  address?: string
  vendorNumber?: string
  categoryId?: string
  notes?: string
  newCategoryName?: string
}

export interface ICreateContactUseCase extends IUseCase<CreateContactDto> {}

@injectable()
export class CreateContactUseCase implements ICreateContactUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(ContactService)
    private readonly contactsService: IContactsService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('CreateContactUseCase')
  }

  public async execute(dto: CreateContactDto, userId: string, licenseeId: string) {
    this.logger.info('Executing CreateContact Use Case', {
      contactName: dto.name,
      company: dto.company,
      licenseeId,
    })

    // Create Contact
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Contact',
      maximumBatchSize: 20,
    })

    const { ref: createdContactRef, records: contactRecords } = this.contactsService.createContact(
      operation,
      dto,
      licenseeId,
      userId
    )

    this.logger.debug('Creating new Contact record', { records: contactRecords })

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      'any',
      'contacts',
      createdContactRef.id,
      dto.name
    )

    this.logger.info('Creating Action Log Record', { record: actionLogRecord })

    operation.addMany(contactRecords).add(actionLogRecord)

    await operation.commit()

    this.logger.info('Contact created successfully', {
      contactId: createdContactRef.id,
    })
  }
}
