export interface IUseCase<T, U = undefined> {
  execute: (dto: T, userId: string, licenseeId: string) => Promise<U | void>
}
export interface BaseDto {
  id: string
}

export interface BaseWithVesselIdDto {
  vesselId: string
}

export interface BaseWithLicenseeIdDto {
  licenseeId: string
}

export interface BaseWithLicenseeIdsDto {
  licenseeIds: string[]
}

export interface BaseCreateDto {
  addedBy: string
  whenAdded: number
}

export interface BaseUpdateDto {
  updatedBy?: string
  whenUpdated?: number
}

export interface BaseCompleteDto {
  completedBy?: string
  whenCompleted?: number
}

// TODO: Use this when implementing Delete functionality. Most likely add it to the `BaseUpdateDto`
export interface BaseDeleteDto {
  deletedBy?: string
  whenDeleted?: number
}
