import { JobListService } from '@src/domain/services/JobListService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { TagsService } from '@src/domain/services/TagsService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateJobListDto {
  id?: string
  task: string
  description: string
  priority: string
  assignedTo: {
    userId?: string[]
    contactId?: string[]
    name?: string
  }
  tags?: string[]
  maintenanceTags?: string[]
  dateDue?: string
  equipmentId?: string
  locationId?: string
  emailReminder?: string
  estimatedCost?: number
  dateToRemind?: string
  files: SeaFile[]
  vesselId: string
  emailToIds?: string[]
  jobNum?: string
  estimatedTime: number

  userName?: string
  systemName?: string
  equipmentName?: string
  locationName?: string
  isCritical?: boolean
  newMaintenanceTags: string[]
  newTags: string[]
}

export interface IUpdateUpdateJobListUseCase extends IUseCase<UpdateJobListDto> {}

export class UpdateJobListUseCase implements IUpdateUpdateJobListUseCase {
  private readonly jobListService: JobListService
  private readonly actionLogService: ActionLogService
  private readonly TagsService: TagsService

  constructor(
    @inject(JobListService)
    jobListService: JobListService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(TagsService)
    tagsService: TagsService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.jobListService = jobListService
    this.actionLogService = actionLogService
    this.TagsService = tagsService
  }
  async execute(dto: UpdateJobListDto, userId: string, licenseeId: string): Promise<void> {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'jobs', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Job List
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Job List',
      maximumBatchSize: 20,
    })
    const { newMaintenanceTags, newTags, ...rest } = dto

    const { ref: updateJobListRef, records: jobListRecord } = this.jobListService.updateJobList(
      operation,
      rest,
      userId,
      licenseeId
    )

    operation.addMany(jobListRecord)

    if (newMaintenanceTags.length > 0) {
      newMaintenanceTags.forEach(tag => {
        const addMaintenanceTagRecord = this.TagsService.addMaintenanceTag(operation, dto.vesselId, tag)

        operation.add(addMaintenanceTagRecord)
      })
    }

    if (newTags.length > 0) {
      newTags.forEach(tag => {
        const addJobTagRecord = this.TagsService.addJobTag(operation, dto.vesselId, tag)

        operation.add(addJobTagRecord)
      })
    }

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'jobs',
      updateJobListRef.id,
      dto.task
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
