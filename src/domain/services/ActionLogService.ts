import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { inject, injectable } from 'inversify'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SERVICES } from '@src/domain/di/ServiceRegistry'

export enum ActionLogType {
  LOGBOOK_SETTING = 'logbookSettings',
  SAFETY_MEETING_SETTINGS = 'safetyMeetingSettings',
  PROGRESS = 'progress',
  COMPLETE = 'complete',
  SCHEDULED = 'scheduled',
  JOB = 'job',
}

export interface IActionLogService {
  createAddedAction(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    // Made it backwards compatible for now
    vesselIds: string | string[],
    collection: string,
    docId: string,
    detail: string,
    type?: ActionLogType
  ): FirestoreRecord

  createUpdatedAction(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    // Made it backwards compatible for now
    vesselIds: string | string[],
    collection: string,
    docId: string,
    detail: string,
    type?: ActionLogType
  ): FirestoreRecord
}

@injectable()
export class ActionLogService implements IActionLogService {
  private readonly logger: ILogger
  constructor(@inject(SERVICES.ILogger) logger: ILogger) {
    this.logger = logger.scoped('ActionLogService')
  }

  createAddedAction(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    vesselIds: string | string[],
    collection: string,
    docId: string,
    detail: string,
    type?: ActionLogType
  ): FirestoreRecord {
    const vesselIdsArray = Array.isArray(vesselIds) ? vesselIds : [vesselIds]

    const actionLogRecord = {
      licenseeId,
      userId,
      vesselIds: vesselIdsArray,
      collection,
      docId,
      detail,
      type: type ?? '', // Optional type field
      when: Date.now(),
      touched: operation.serverTimestamp(),
      action: 'A',
    }

    this.logger.debug("Created 'Add' action log record: ", { actionLogRecord })

    return {
      ref: operation.makeRef('actionLog'),
      data: actionLogRecord,
      options: { merge: true },
    }
  }

  createUpdatedAction(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    vesselIds: string | string[],
    collection: string,
    docId: string,
    detail: string,
    type?: ActionLogType
  ): FirestoreRecord {
    const vesselIdsArray = Array.isArray(vesselIds) ? vesselIds : [vesselIds]

    const actionLogRecord = {
      licenseeId,
      userId,
      vesselIds: vesselIdsArray,
      collection,
      docId,
      detail,
      type: type ?? operation.deleteField(), // Optional type field
      when: Date.now(),
      touched: operation.serverTimestamp(),
      action: 'U',
    }

    this.logger.debug("Created 'Update' action log record: ", {
      actionLogRecord,
    })

    return {
      ref: operation.makeRef('actionLog'),
      data: actionLogRecord,
      options: { merge: true },
    }
  }

  createDeletedAction(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    vesselIds: string | string[],
    collection: string,
    docId: string,
    detail: string,
    type?: ActionLogType,
    personnelIds?: string[]
  ): FirestoreRecord {
    const vesselIdsArray = Array.isArray(vesselIds) ? vesselIds : [vesselIds]

    const actionLogRecord = {
      licenseeId,
      userId,
      vesselIds: vesselIdsArray,
      personnelIds: personnelIds ?? operation.deleteField(),
      collection,
      docId,
      detail,
      type: type ?? operation.deleteField(), // Optional type field
      when: Date.now(),
      touched: operation.serverTimestamp(),
      action: 'D',
    }

    this.logger.debug("Created 'Deleted' action log record: ", {
      actionLogRecord,
    })

    return {
      ref: operation.makeRef('actionLog'),
      data: actionLogRecord,
      options: { merge: true },
    }
  }
}
