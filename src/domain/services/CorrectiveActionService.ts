import { injectable } from 'inversify'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { CreateCorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/CreateCorrectiveActionUseCase'
import { UpdateCorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionUseCase'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { DateTime } from 'luxon'
import { UpdateCorrectiveActionDescriptionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/UpdateCorrectiveActionDescriptionUseCase'
import { CompleteCorrectiveActionDto } from '@src/domain/use-cases/healthSafety/correctiveAction/CompleteCorrectiveActionUseCase'
import { toMillis } from '@src/lib/util'
import { CorrectiveActionState } from '@src/shared-state/HealthSafety/correctiveActions'

@injectable()
export class CorrectiveActionService
  implements
    IBaseDataService<CreateCorrectiveActionDto, UpdateCorrectiveActionDto | UpdateCorrectiveActionDescriptionDto>
{
  readonly collectionName = 'correctiveActions'

  createDataItem(
    operation: FirestoreOperation,
    createItemDto: CreateCorrectiveActionDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const correctiveActionCreatedRecord = {
      ref: operation.makeRef(this.collectionName),
      data: {
        ...createItemDto,
        addedBy: userId,
        whenAdded: DateTime.now().toUTC().toMillis(),
        touched: operation.serverTimestamp(),
      },
    }

    return {
      ref: correctiveActionCreatedRecord.ref,
      records: [correctiveActionCreatedRecord],
    }
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateCorrectiveActionDto | UpdateCorrectiveActionDescriptionDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, ...rest } = updateItemDto

    const correctiveActionUpdatedRecord = {
      ref: operation.makeRef(this.collectionName, id),
      data: {
        ...rest,
        updatedBy: userId,
        whenUpdated: DateTime.now().toUTC().toMillis(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: correctiveActionUpdatedRecord.ref,
      records: [correctiveActionUpdatedRecord],
    }
  }

  completeCorrectiveAction(
    operation: FirestoreOperation,
    dto: CompleteCorrectiveActionDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const completedRecord = {
      ref: operation.makeRef(this.collectionName, dto.id),
      data: {
        state: dto.state ?? CorrectiveActionState.Completed,
        updatedBy: userId,
        whenUpdated: DateTime.now().toUTC().toMillis(),
        completedBy: userId,
        whenCompleted: dto.whenCompleted ?? DateTime.now().toUTC().toMillis(),
        completedNotes: dto.completedNotes,
        completedFiles: dto.completedFiles ?? [],
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: completedRecord.ref,
      records: [completedRecord],
    }
  }

  deleteDataItem(
    operation: FirestoreOperation,
    id: string,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const deletedRecord = {
      ref: operation.makeRef(this.collectionName, id),
      data: {
        state: CorrectiveActionState.Deleted,
        deletedBy: userId,
        whenDeleted: DateTime.now().toUTC().toMillis(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: deletedRecord.ref,
      records: [deletedRecord],
    }
  }
}
