import { inject, injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { UpdateJobListDto } from '../use-cases/maintenance/UpdateJoblistUseCase'
import { DocRef } from '../data/IFirestoreService'
import { NotificationService } from './NotificationService'
import { CreateJobListDto } from '../use-cases/maintenance/CreateJobListUseCase'
import { UpdateJobListTaskDto } from '../use-cases/maintenance/UpdateJobListTaskUseCase'

export interface IJobListService {
  createJobList(
    operation: FirestoreOperation,
    createJobListDto: Omit<CreateJobListDto, 'newTags' | 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateJobList(
    operation: FirestoreOperation,
    updateJobListDto: UpdateJobListDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateTask(
    operation: FirestoreOperation,
    updateJobListDto: UpdateJobListTaskDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class JobListService implements IJobListService {
  private readonly notificationService: NotificationService
  constructor(
    @inject(NotificationService)
    notificationService: NotificationService
  ) {
    this.notificationService = notificationService
  }

  public updateJobList(
    operation: FirestoreOperation,
    updateJobListDto: Omit<UpdateJobListDto, 'newTags' | 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      id,
      vesselId,
      assignedTo,
      tags,
      maintenanceTags,
      dateDue,
      equipmentId,
      locationId,
      emailReminder,
      estimatedCost,
      dateToRemind,
      emailToIds,
      jobNum,
      systemName,
      equipmentName,
      locationName,
      isCritical,
      userName,
      ...rest
    } = updateJobListDto

    const data = {
      ...rest,
      assignedTo: {
        userId: assignedTo.userId ?? operation.deleteField(),
        contactId: assignedTo.contactId ?? operation.deleteField(),
      },
      tags: tags && tags.length > 0 ? tags : operation.deleteField(),
      maintenanceTags: maintenanceTags && maintenanceTags.length > 0 ? maintenanceTags : operation.deleteField(),
      dateDue: dateDue ?? operation.deleteField(),
      equipmentId: equipmentId ?? operation.deleteField(),
      //TODO: Update the location in the database
      locationId: locationId ?? operation.deleteField(),
      emailReminder: emailReminder ?? operation.deleteField(),
      estimatedCost: estimatedCost ?? operation.deleteField(),
      dateToRemind: dateToRemind ?? operation.deleteField(),

      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedJobListRecord = {
      ref: operation.makeRef('jobs', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'jobs', licenseeId, vesselId)

    const vesselNotificationDto = {
      userName: userName ?? '', // Ensure userName is always a string
      vesselId: vesselId,
      type: 'jobCreated',
      emailMeType: 'jobsUpdated',
      metadata: {
        isUpdate: true,
        id,
        task: rest.task,
        description: rest.description,
        ...(jobNum ? { jobNum } : {}),
        priority: rest.priority,
        assignedToName: assignedTo?.name ?? '',
        tags: tags && tags.length > 0 ? tags : [],
        maintenanceTags: maintenanceTags && maintenanceTags.length > 0 ? maintenanceTags : [],

        ...(equipmentId ? { equipmentId } : {}),
        ...(locationId ? { locationId } : {}),
        ...(systemName ? { system: systemName } : {}),
        ...(equipmentName ? { equipment: equipmentName } : {}),
        ...(locationName ? { location: locationName } : {}),
        ...(isCritical ? { isCritical } : {}),
        ...(dateDue ? { dateDue } : {}),
        ...(estimatedCost ? { estimatedCost } : {}),
      },
      files: rest.files ?? [],
      toIds: emailToIds,
    }

    //TODO: VESSEL NOTIFICATION
    const vesselNotificationRecord = this.notificationService.sendVesselNotification(
      operation,
      userId,
      licenseeId,
      vesselNotificationDto
    )

    return {
      ref: updatedJobListRecord.ref,
      records: [updatedJobListRecord, vesselTouched, vesselNotificationRecord],
    }
  }

  public createJobList(
    operation: FirestoreOperation,
    createJobListDto: Omit<CreateJobListDto, 'newTags' | 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      vesselId,
      assignedTo,
      tags,
      maintenanceTags,
      dateDue,
      equipmentId,
      locationId,
      emailReminder,
      estimatedCost,
      dateToRemind,
      emailToIds,
      jobNum,
      systemName,
      equipmentName,
      locationName,
      isCritical,
      userName,
      ...rest
    } = createJobListDto

    const data = {
      ...rest,
      vesselId,
      licenseeId,

      assignedTo: {
        ...(assignedTo.userId ? { userId: assignedTo.userId } : {}),
        ...(assignedTo.contactId ? { contactId: assignedTo.contactId } : {}),
      },
      estimatedCost: estimatedCost ?? 0,
      tags: tags && tags.length > 0 ? tags : [],
      maintenanceTags: maintenanceTags && maintenanceTags.length > 0 ? maintenanceTags : [],
      ...(dateDue ? { dateDue } : {}),
      ...(equipmentId ? { equipmentId } : {}),
      //TODO: Update the location in the database
      ...(locationId ? { locationId } : {}),
      ...(emailReminder ? { emailReminder } : {}),
      ...(dateToRemind ? { dateToRemind } : {}),

      whenAdded: Date.now(),
      state: 'active',

      addedBy: userId,
      touched: operation.serverTimestamp(),
    }

    const updatedJobListRecord = {
      ref: operation.makeRef('jobs'),
      data,
    }

    const vesselTouched = this.vesselTouched(operation, 'jobs', licenseeId, vesselId)

    const vesselNotificationDto = {
      userName: userName ?? '', // Ensure userName is always a string
      vesselId: vesselId,
      type: 'jobCreated',
      emailMeType: 'jobsCreated',
      metadata: {
        isUpdate: true,
        id: updatedJobListRecord.ref.id,
        task: rest.task,
        description: rest.description,
        priority: rest.priority,
        assignedToName: assignedTo.name,
        ...(tags && tags.length > 0 ? { tags } : {}),
        ...(maintenanceTags && maintenanceTags.length > 0 ? { maintenanceTags } : {}),
        ...(equipmentId ? { equipmentId } : {}),
        ...(locationId ? { locationId } : {}),
        ...(systemName ? { system: systemName } : {}),
        ...(equipmentName ? { equipment: equipmentName } : {}),
        ...(locationName ? { location: locationName } : {}),
        ...(isCritical ? { isCritical } : {}),
        ...(dateDue ? { dateDue } : {}),
        ...(estimatedCost ? { estimatedCost } : {}),
        estimatedTime: rest.estimatedTime ?? 0,
      },
      files: rest.files ?? [],
      toIds: emailToIds,
    }

    const vesselNotificationRecord = this.notificationService.sendVesselNotification(
      operation,
      userId,
      licenseeId,
      vesselNotificationDto
    )

    return {
      ref: updatedJobListRecord.ref,
      records: [updatedJobListRecord, vesselTouched, vesselNotificationRecord],
    }
  }

  public updateTask(
    operation: FirestoreOperation,
    updateJobListTaskDto: UpdateJobListTaskDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      id,
      description,
      tags,
      maintenanceTags,
      systemName,
      equipmentName,
      locationName,
      isCritical,
      dateDue,
      estimatedCost,
      ...rest
    } = updateJobListTaskDto

    const data = {
      updatedBy: userId,
      whenUpdated: Date.now(),
      description,
      touched: operation.serverTimestamp(),
    }

    const updatedJobListRecord = {
      ref: operation.makeRef('jobs', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'jobs', licenseeId, rest.vesselId)

    const vesselNotificationDto = {
      userName: rest.userName ?? '', // Ensure userName is always a string
      vesselId: rest.vesselId,
      type: 'jobCreated',
      emailMeType: 'jobsUpdated',
      metadata: {
        isUpdate: true,
        id,
        task: rest.task,
        description,
        jobNum: rest.jobNum,
        priority: rest.priority,
        ...(rest.assignedTo?.name ? { assignedToName: rest.assignedTo.name } : {}),
        ...(tags && tags.length > 0 ? { tags } : {}),
        ...(maintenanceTags && maintenanceTags.length > 0 ? { maintenanceTags } : {}),
        ...(systemName ? { system: systemName } : {}),
        ...(equipmentName ? { equipment: equipmentName } : {}),
        ...(locationName ? { location: locationName } : {}),
        ...(isCritical ? { isCritical } : {}),
        ...(dateDue ? { dateDue } : {}),
        ...(estimatedCost ? { estimatedCost } : {}),
      },
      files: rest.files ?? [],
    }

    const vesselNotificationRecord = this.notificationService.sendVesselNotification(
      operation,
      userId,
      licenseeId,
      vesselNotificationDto
    )

    return {
      ref: updatedJobListRecord.ref,
      records: [updatedJobListRecord, vesselTouched, vesselNotificationRecord],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
