import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { serverTimestamp } from '@src/lib/firebase/services/firestore.service'
import { CreateContactDto } from '@src/domain/use-cases/crew/CreateContactUseCase'
import { inject, injectable } from 'inversify'
import { UpdateContactDto } from '@src/domain/use-cases/crew/UpdateContactUseCase'
import { DocRef, IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { DateTime } from 'luxon'

export interface IContactsService {
  createContact(
    operation: FirestoreOperation,
    createContactDto: CreateContactDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateContact(
    operation: FirestoreOperation,
    updateContactDto: UpdateContactDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  deleteContact(
    operation: FirestoreOperation,
    contactId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createContactCategory(
    operation: FirestoreOperation,
    newCategoryName: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class ContactService implements IContactsService {
  private readonly logger: ILogger
  private readonly firestoreService: IFirestoreService

  constructor(
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(SERVICES.IFirestoreService) firestoreService: IFirestoreService
  ) {
    this.logger = logger.scoped('ContactsService')
    this.firestoreService = firestoreService
  }

  createContact(operation: FirestoreOperation, dto: CreateContactDto, licenseeId: string, userId: string) {
    const newContactRecord = {
      ref: operation.makeRef('contacts'),
      data: {
        licenseeId: licenseeId,
        name: dto.name,
        company: dto.company ?? '',
        email: dto.email ?? undefined,
        number: dto.number ?? undefined,
        address: dto.address ?? undefined,
        vendorNumber: dto.vendorNumber ?? undefined,
        categoryId: dto.categoryId ?? undefined,
        notes: dto.notes ?? undefined,
        whenAdded: operation.serverTimestamp(),
        addedBy: userId,
        state: 'active',
      },
    }

    const whenLicenseeTouchedRecord = {
      ref: operation.makeRef('whenLicenseeTouched', licenseeId),
      data: {
        touched: operation.serverTimestamp(),
        contacts: operation.serverTimestamp(),
        ...(dto.newCategoryName && { contactCategories: operation.serverTimestamp() }),
      },
      options: { merge: true },
    }

    const records: FirestoreRecord[] = [newContactRecord, whenLicenseeTouchedRecord]

    // Create new category if specified
    if (dto.newCategoryName) {
      const newCategoryRecord = this.createContactCategory(operation, dto.newCategoryName, userId, licenseeId)
      records.push(...newCategoryRecord.records)

      // Update contact to reference new category
      newContactRecord.data.categoryId = newCategoryRecord.ref.id
    }

    return {
      ref: newContactRecord.ref,
      records,
    }
  }

  updateContact(operation: FirestoreOperation, dto: UpdateContactDto, userId: string, licenseeId: string) {
    const contactRef = operation.makeRef('contacts', dto.contactId)

    const updateContactRecord = {
      ref: contactRef,
      data: {
        name: dto.name,
        company: dto.company ?? '',
        email: dto.email ?? operation.deleteField(),
        number: dto.number ?? operation.deleteField(),
        address: dto.address ?? operation.deleteField(),
        vendorNumber: dto.vendorNumber ?? operation.deleteField(),
        categoryId: dto.categoryId ?? operation.deleteField(),
        notes: dto.notes ?? operation.deleteField(),
        whenUpdated: DateTime.now().toUTC().toMillis(),
        touched: operation.serverTimestamp(),
        updatedBy: userId,
      },
      options: {
        merge: true,
      },
    }

    const records: FirestoreRecord[] = [updateContactRecord]

    if (dto.newCategoryName) {
      const newCategoryRecord = this.createContactCategory(operation, dto.newCategoryName, userId, licenseeId)
      records.push(...newCategoryRecord.records)

      updateContactRecord.data.categoryId = newCategoryRecord.ref.id
    }

    this.logger.info('Updating contact', {
      contactId: dto.contactId,
      contactName: dto.name,
    })

    return {
      ref: contactRef,
      records,
    }
  }

  deleteContact(operation: FirestoreOperation, contactId: string, userId: string, licenseeId: string) {
    const contactRef = operation.makeRef('contacts', contactId)

    const deleteContactRecord = {
      ref: contactRef,
      data: {
        state: 'deleted',
        touched: operation.serverTimestamp(),
        whenDeleted: DateTime.now().toUTC().toMillis(),
        deletedBy: userId,
      },
      options: { merge: true },
    }

    this.logger.info('Deleting contact', { contactId, licenseeId })

    return {
      ref: contactRef,
      records: [deleteContactRecord],
    }
  }

  createContactCategory(operation: FirestoreOperation, newCategoryName: string, userId: string, licenseeId: string) {
    const newCategoryRecord = {
      ref: operation.makeRef('contactCategories'),
      data: {
        licenseeId,
        name: newCategoryName,
        state: 'active',
        touched: operation.serverTimestamp(),
      },
    }

    this.logger.info('Creating new contact category', {
      categoryName: newCategoryName,
      licenseeId,
    })

    return {
      ref: newCategoryRecord.ref,
      records: [newCategoryRecord],
    }
  }
}
