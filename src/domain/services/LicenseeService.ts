import { inject, injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { LicenseeDataSyncCollection } from '@src/shared-state/DataSyncSystem/dataSyncTasks'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SERVICES } from '@src/domain/di/ServiceRegistry'

@injectable()
export class LicenseeService {
  readonly collectionName = 'whenLicenseeTouched'
  private readonly logger: ILogger

  constructor(@inject(SERVICES.ILogger) logger: ILogger) {
    this.logger = logger.scoped('LicenseeService')
  }

  touchLicenseeData(
    collection: LicenseeDataSyncCollection,
    operation: FirestoreOperation,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord
  } {
    this.logger.info(`Touching licensee data: ${collection}`)

    const licenseeTouchedRecord = {
      ref: operation.makeRef(this.collectionName, licenseeId),
      data: {
        touched: operation.serverTimestamp(),
        [collection]: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: licenseeTouchedRecord.ref,
      records: licenseeTouchedRecord,
    }
  }
}
