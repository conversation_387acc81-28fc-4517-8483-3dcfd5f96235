import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { CompleteCustomFormDto } from '../use-cases/companyDocumentRegister/CompleteCustomFormUseCase'
import { DocRef } from '../data/IFirestoreService'
import { UpdateCompletedCustomFormDto } from '../use-cases/companyDocumentRegister/UpdateCompletedCustomFormUseCase'
import { CreateCustomFormDto } from '../use-cases/companyDocumentRegister/CreateCustomFormUseCase'
import { UpdateCustomFormDto } from '../use-cases/companyDocumentRegister/UpdateCustomFormUseCase'
import { UpdateCustomFormMetadataDto } from '../use-cases/companyDocumentRegister/UpdateCustomFormMetadataUseCase'
import { DeleteCompletedCustomFormDto } from '../use-cases/companyDocumentRegister/DeleteCompletedCustomFormUseCase'
import { DeleteCustomFormDto } from '../use-cases/companyDocumentRegister/DeleteCustomFormUseCase'
import { DateTime } from 'luxon'

export interface ICustomFormService {
  completeCustomForm(
    operation: FirestoreOperation,
    dto: Omit<CompleteCustomFormDto, 'title' | 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateCompletedCustomForm(
    operation: FirestoreOperation,
    dto: Omit<UpdateCompletedCustomFormDto, 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createCustomForm(
    operation: FirestoreOperation,
    dto: CreateCustomFormDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateCustomFormMetadata(
    operation: FirestoreOperation,
    dto: UpdateCustomFormMetadataDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateCustomForm(
    operation: FirestoreOperation,
    dto: UpdateCustomFormDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  deleteCustomForm(
    operation: FirestoreOperation,
    dto: Omit<DeleteCustomFormDto, 'title'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  deleteCompletedCustomForm(
    operation: FirestoreOperation,
    dto: Pick<DeleteCompletedCustomFormDto, 'id' | 'customFormVersionId'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class CustomFormService implements ICustomFormService {
  completeCustomForm(
    operation: FirestoreOperation,
    dto: Omit<CompleteCustomFormDto, 'title' | 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      vesselIds,
      personnelIds,
      formData,
      attachTo,
      attachToId,
      attachToTrainingTaskId,
      attachToVesselId,
      versionId,
      ...rest
    } = dto

    const data = {
      ...rest,
      licenseeId,
      vesselIds: vesselIds && vesselIds.length > 0 ? vesselIds : ['none'],
      personnelIds: personnelIds && personnelIds.length > 0 ? personnelIds : [],
      data: formData,
      attachTo: attachTo ?? null,
      ...(attachToId
        ? {
            attachToId,
          }
        : {}),
      ...(attachToTrainingTaskId
        ? {
            attachToTrainingTaskId,
          }
        : {}),
      ...(attachToVesselId
        ? {
            attachToVesselId,
          }
        : {}),
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const records: FirestoreRecord[] = []

    const updatedSOPRecord = {
      ref: operation.makeRef('customFormsCompleted'),
      data,
    }

    records.push(updatedSOPRecord)

    const versionData = {
      numCompleted: operation.increment(1),
      touched: operation.serverTimestamp(),
    }

    const updatedVersionRecord = {
      ref: operation.makeRef('customFormVersions', versionId),
      data: versionData,
      options: { merge: true },
    }

    records.push(updatedVersionRecord)

    if (!dto.isDraft) {
      const updatedCustomFormData = this.updateCustomFormLastCompletedData(
        operation,
        dto.customFormId,
        userId,
        vesselIds
      )

      records.push(updatedCustomFormData)
    }

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    const licenseeTouchedCustomFormsCompleted = this.licenseeTouched(operation, 'customFormsCompleted', licenseeId)

    records.push(licenseeTouchedCustomFormsCompleted)

    return {
      ref: updatedSOPRecord.ref,
      records,
    }
  }

  updateCompletedCustomForm(
    operation: FirestoreOperation,
    dto: Omit<UpdateCompletedCustomFormDto, 'title' | 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, customFormId, vesselIds, personnelIds, formData, ...rest } = dto

    const data = {
      ...rest,
      vesselIds: vesselIds && vesselIds.length > 0 ? vesselIds : ['none'],
      personnelIds: personnelIds && personnelIds.length > 0 ? personnelIds : [],
      data: formData,
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
      ...(!dto.isDraft
        ? {
            whenAdded: Date.now(), // Use batchTrace.whenUpdated?
            addedBy: userId,
          }
        : {}),
    }

    const records: FirestoreRecord[] = []

    const updatedCompletedFormData = {
      ref: operation.makeRef('customFormsCompleted', id),
      data,
      options: { merge: true },
    }

    records.push(updatedCompletedFormData)

    if (!dto.isDraft) {
      const updatedCustomFormData = this.updateCustomFormLastCompletedData(
        operation,
        dto.customFormId,
        userId,
        vesselIds
      )

      records.push(updatedCustomFormData)
    }

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    const licenseeTouchedCustomFormsCompleted = this.licenseeTouched(operation, 'customFormsCompleted', licenseeId)

    records.push(licenseeTouchedCustomFormsCompleted)

    return {
      ref: updatedCompletedFormData.ref,
      records,
    }
  }

  createCustomForm(
    operation: FirestoreOperation,
    dto: CreateCustomFormDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      categoryId,
      forCrew,
      forVesselIds,
      isTemplate,
      templateCategory,
      files,
      latestVersion,
      form,
      historyElement,
      ...rest
    } = dto

    const version = latestVersion ?? Date.now()

    const data = {
      ...rest,
      licenseeId,
      ...(categoryId ? { categoryId } : {}),
      forVesselIds: forVesselIds && forVesselIds.length > 0 ? forVesselIds : ['none'],
      forCrew: forCrew ? true : false,
      ...(isTemplate !== undefined ? { isTemplate: isTemplate ? true : false } : {}),
      ...(templateCategory ? { templateCategory } : {}),
      latestVersion: version,
      state: 'draft',
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const records: FirestoreRecord[] = []

    const customFormRecord = {
      ref: operation.makeRef('customForms'),
      data,
    }

    records.push(customFormRecord)

    if (isTemplate) {
      files?.forEach(file => {
        if (file.id) {
          const filesRecord = {
            ref: operation.makeRef('files', file.id),
            data: { canShare: true },
            options: { merge: true },
          }

          records.push(filesRecord)
        }
      })
    }

    const versionData = {
      customFormId: customFormRecord.ref.id,
      licenseeId,
      version,
      numCompleted: 0,
      state: 'active',
      forVesselIds: forVesselIds && forVesselIds.length > 0 ? forVesselIds : ['none'],
      historyElementN: historyElement ?? 0,
      form,
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const customFormVersionsRecord = {
      ref: operation.makeRef('customFormVersions'),
      data: versionData,
    }

    records.push(customFormVersionsRecord)

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    //TODO Links Logic

    return {
      ref: customFormRecord.ref,
      records,
    }
  }

  updateCustomFormMetadata(
    operation: FirestoreOperation,
    dto: UpdateCustomFormMetadataDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, categoryId, forCrew, forVesselIds, isTemplate, templateCategory, files, ...rest } = dto

    const data = {
      ...rest,
      forVesselIds: forVesselIds && forVesselIds.length > 0 ? forVesselIds : ['none'],
      forCrew: forCrew ? true : false,
      ...(isTemplate !== undefined ? { isTemplate: isTemplate ? true : false } : {}),
      ...(templateCategory ? { templateCategory } : {}),
      categoryId: categoryId ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const records: FirestoreRecord[] = []

    const customFormRecord = {
      ref: operation.makeRef('customForms', id),
      data,
      options: { merge: true },
    }

    records.push(customFormRecord)

    if (isTemplate) {
      files?.forEach(file => {
        if (file.id) {
          const filesRecord = {
            ref: operation.makeRef('files', file.id),
            data: { canShare: true },
            options: { merge: true },
          }

          records.push(filesRecord)
        }
      })
    }

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    //TODO Links Logic
    return {
      ref: customFormRecord.ref,
      records,
    }
  }

  updateCustomForm(
    operation: FirestoreOperation,
    dto: UpdateCustomFormDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      version,
      forVesselIds,
      crewElement,
      vesselsElement,
      customFormId,
      forVessels,
      forCrew,
      categoryId,
      form,
      historyElementN,
      formIsDraft,
      ...rest
    } = dto

    const data = {
      ...rest,
      latestVersion: version,
      forVesselIds: forVesselIds ?? ['none'], // (changes will be propagated server side)
      ...(crewElement ? { crewElement } : {}),
      ...(vesselsElement ? { vesselsElement } : {}),
      forCrew: forCrew ? true : false,
      ...(categoryId ? { categoryId } : {}),
      ...(formIsDraft ? { state: 'active' } : {}),
      updatedBy: userId,
      whenUpdated: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const records: FirestoreRecord[] = []

    const customFormRecord = {
      ref: operation.makeRef('customForms', customFormId),
      data,
      options: { merge: true },
    }

    records.push(customFormRecord)

    const versionData = {
      customFormId,
      licenseeId,
      version,
      numCompleted: 0,
      state: 'active',
      forVesselIds: forVessels ? forVesselIds : ['none'],
      historyElementN,
      form,
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const customFormVersionsRecord = {
      ref: operation.makeRef('customFormVersions'),
      data: versionData,
    }

    records.push(customFormVersionsRecord)

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    return {
      ref: customFormRecord.ref,
      records,
    }
  }

  deleteCustomForm(
    operation: FirestoreOperation,
    dto: Omit<DeleteCustomFormDto, 'title'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, versionIds } = dto

    const records: FirestoreRecord[] = []

    const data = {
      state: 'deleted',
      deletedBy: userId,
      whenDeleted: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedCustomFromRecord = {
      ref: operation.makeRef('customForms', id),
      data,
      options: { merge: true },
    }

    records.push(updatedCustomFromRecord)

    if (versionIds && versionIds.length > 0) {
      versionIds.forEach(versionId => {
        const versionData = {
          state: 'deleted',
          deletedBy: userId,
          whenDeleted: DateTime.now(),
          touched: operation.serverTimestamp(),
        }

        const customFormVersionsRecord = {
          ref: operation.makeRef('customFormVersions', versionId),
          data: versionData,
          options: { merge: true },
        }

        records.push(customFormVersionsRecord)
      })
    }

    const licenseeTouchedCustomFormsCompleted = this.licenseeTouched(operation, 'customFormsCompleted', licenseeId)

    records.push(licenseeTouchedCustomFormsCompleted)

    return {
      ref: updatedCustomFromRecord.ref,
      records,
    }
  }

  deleteCompletedCustomForm(
    operation: FirestoreOperation,
    dto: Pick<DeleteCompletedCustomFormDto, 'id' | 'customFormVersionId'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, customFormVersionId } = dto
    const data = {
      state: 'deleted',
      deletedBy: userId,
      whenDeleted: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedCustomFromRecord = {
      ref: operation.makeRef('customFormsCompleted', id),
      data,
      options: { merge: true },
    }

    const versionData = {
      numCompleted: operation.increment(-1),
      touched: operation.serverTimestamp(),
    }

    const customFormVersionsRecord = {
      ref: operation.makeRef('customFormVersions', customFormVersionId),
      data: versionData,
      options: { merge: true },
    }

    const licenseeTouchedCustomFormsCompleted = this.licenseeTouched(operation, 'customFormsCompleted', licenseeId)

    return {
      ref: updatedCustomFromRecord.ref,
      records: [updatedCustomFromRecord, customFormVersionsRecord, licenseeTouchedCustomFormsCompleted],
    }
  }

  private updateCustomFormLastCompletedData(
    operation: FirestoreOperation,
    customFormId: string,
    userId: string,
    vesselIds?: string[]
  ) {
    const customFormData = {
      whenLastCompleted: Date.now(),
      lastCompletedBy: userId,
      lastCompletedVesselIds: vesselIds && vesselIds.length > 0 ? vesselIds : [],
      touched: operation.serverTimestamp(),
    }

    const updatedCustomFormData = {
      ref: operation.makeRef('customForms', customFormId),
      data: customFormData,
      options: { merge: true },
    }

    return updatedCustomFormData
  }

  private licenseeTouched(operation: FirestoreOperation, collection: string, licenseeId: string) {
    const licenseeData = {
      touched: operation.serverTimestamp(),
      [collection]: operation.serverTimestamp(),
    }

    const licenseeTouched = {
      ref: operation.makeRef('whenLicenseeTouched', licenseeId),
      data: licenseeData,
      options: { merge: true },
    }

    return licenseeTouched
  }
}
