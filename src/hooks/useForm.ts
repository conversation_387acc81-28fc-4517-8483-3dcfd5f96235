import { FormikConfig, useFormik } from 'formik'
import { FormikValues } from 'formik/dist/types'
import { useCallback } from 'react'
import { debounce } from 'lodash'

type FormikAdvancedConfig<Values extends FormikValues = FormikValues> = FormikConfig<Values> & {
  onSubmit: (values: Values) => void
  debounceTimeout?: number
}

/**
 * Custom wrapper for useFormik hook
 *
 * @param config - The configuration for the form.
 * @returns The formik object.
 */
const useForm = <Values extends FormikValues = FormikValues>({
  onSubmit,
  debounceTimeout = 200,
  ...props
}: FormikAdvancedConfig<Values>) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedOnSubmit = useCallback(
    debounce((values: Values) => onSubmit(values), debounceTimeout),
    [onSubmit, debounceTimeout]
  )

  return useFormik({
    ...props,
    onSubmit: debouncedOnSubmit,
  })
}

export default useForm
