import { usePathname, useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes, ROUTES_CONFIG } from '@src/navigation/constants'

export type SubNavRoute = {
  title: string
  path: string
}

export const useSubNav = (vesselId: string | undefined, routes: SubNavRoute[], activeRoute?: Routes) => {
  const router = useRouter()

  const navigateToRoute = (path: string, vesselId?: string) => {
    router.navigate({
      pathname: getRoutePath(path),
      params: {
        vesselId: vesselId,
      },
    })
  }

  const isActive = (path: string) => activeRoute === path

  return routes.map(({ title, path }) => ({
    title,
    onPress: () => navigateToRoute(path, vesselId),
    isActive: isActive(path),
  }))
}

export const useSafetySubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.SAFETY_EQUIPMENT_CHECKS].title ?? 'Safety Checks',
      path: Routes.SAFETY_EQUIPMENT_CHECKS,
    },
    {
      title: ROUTES_CONFIG[Routes.SAFETY_EQUIPMENT_EXPIRIES].title ?? 'Safety Equipment Expiries',
      path: Routes.SAFETY_EQUIPMENT_EXPIRIES,
    },
    { title: 'Drills', path: Routes.DRILLS },
  ]

  return useSubNav(vesselId, subNavRoutes, activeRoute)
}

export const useMaintenanceSubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.MAINTENANCE_SCHEDULE].title ?? 'Maintenance Schedule',
      path: Routes.MAINTENANCE_SCHEDULE,
    },
    {
      title: ROUTES_CONFIG[Routes.JOBLIST].title ?? 'Job List',
      path: Routes.JOBLIST,
    },
    {
      title: ROUTES_CONFIG[Routes.SPARE_PARTS_LIST].title ?? 'Spare Parts List',
      path: Routes.SPARE_PARTS_LIST,
    },
    {
      title: ROUTES_CONFIG[Routes.EQUIPMENT_LIST].title ?? 'Equipment List',
      path: Routes.EQUIPMENT_LIST,
    },
    {
      title: ROUTES_CONFIG[Routes.EQUIPMENT_MANUALS].title ?? 'Equipment Manuals',
      path: Routes.EQUIPMENT_MANUALS,
    },
    {
      title: ROUTES_CONFIG[Routes.MAINTENANCE_HISTORY].title ?? 'Maintenance History',
      path: Routes.MAINTENANCE_HISTORY,
    },
  ]

  return useSubNav(vesselId, subNavRoutes, activeRoute)
}

export const useHealthAndSafetySubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.INCIDENT_REPORT].title ?? 'Incident / Event Reports',
      path: Routes.INCIDENT_REPORT,
    },
    {
      title: ROUTES_CONFIG[Routes.CORRECTIVE_ACTION].title ?? 'Corrective Actions',
      path: Routes.CORRECTIVE_ACTION,
    },
    {
      title: ROUTES_CONFIG[Routes.RISK_ASSESSMENT].title ?? 'Risk Assessments',
      path: Routes.RISK_ASSESSMENT,
    },
    {
      title: ROUTES_CONFIG[Routes.HEALTH_SAFETY_MEETING].title ?? 'Health & Safety Meetings',
      path: Routes.HEALTH_SAFETY_MEETING,
    },
    {
      title: ROUTES_CONFIG[Routes.DANGEROUS_GOODS_REGISTER].title ?? 'Dangerous Goods Register',
      path: Routes.DANGEROUS_GOODS_REGISTER,
    },
  ]

  return useSubNav(vesselId, subNavRoutes, activeRoute)
}

export const useCrewSubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_LIST].title ?? 'Crew Particulars',
      path: Routes.CREW_PARTICULARS_LIST,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_CERTIFICATES].title ?? 'Crew Certificates',
      path: Routes.CREW_CERTIFICATES,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_CONTACTS].title ?? 'Contacts/Suppliers',
      path: Routes.CREW_CONTACTS,
    },
  ]

  return useSubNav(vesselId, subNavRoutes, activeRoute)
}

export const useCrewParticularsSubNav = (vesselId?: string, crewId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_VIEW_PROFILE].title ?? 'Profile',
      path: Routes.CREW_PARTICULARS_VIEW_PROFILE,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_VIEW_FORMS].title ?? 'Forms / Documents',
      path: Routes.CREW_PARTICULARS_VIEW_FORMS,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_VIEW_SEATIME].title ?? 'Sea Time',
      path: Routes.CREW_PARTICULARS_VIEW_SEATIME,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_VIEW_CERTIFICATES].title ?? 'Certificates',
      path: Routes.CREW_PARTICULARS_VIEW_CERTIFICATES,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_VIEW_DRILLS].title ?? 'Drills',
      path: Routes.CREW_PARTICULARS_VIEW_DRILLS,
    },
    {
      title: ROUTES_CONFIG[Routes.CREW_PARTICULARS_VIEW_TRAINING].title ?? 'Training',
      path: Routes.CREW_PARTICULARS_VIEW_TRAINING,
    },
  ]

  const router = useRouter()
  const navigateToRoute = (path: string) => {
    router.navigate({
      pathname: getRoutePath(path),
      params: {
        vesselId: vesselId,
        crewId: crewId,
        tab: path,
      },
    })
  }

  return subNavRoutes.map(({ title, path }) => ({
    title,
    onPress: () => navigateToRoute(path),
    isActive: activeRoute === path,
  }))
}

export const useVesselDocumentSubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.VESSEL_CERTIFICATES].title ?? 'Vessel Certificates',
      path: Routes.VESSEL_CERTIFICATES,
    },
    {
      title: ROUTES_CONFIG[Routes.VESSEL_DOCUMENTS].title ?? 'Vessel Documents',
      path: Routes.VESSEL_DOCUMENTS,
    },
    {
      title: ROUTES_CONFIG[Routes.SURVEY_DOCUMENTS].title ?? 'Survey Documents',
      path: Routes.SURVEY_DOCUMENTS,
    },
    {
      title: ROUTES_CONFIG[Routes.STANDARD_OPERATING_PROCEDURES].title ?? 'Standard Operating Procedures',
      path: Routes.STANDARD_OPERATING_PROCEDURES,
    },
  ]

  return useSubNav(vesselId, subNavRoutes, activeRoute)
}

export const useCompanyDocumentsSubNav = (vesselId?: string, activeRoute?: Routes) => {
  const subNavRoutes = [
    {
      title: ROUTES_CONFIG[Routes.COMPANY_PLAN].title ?? 'Company Plan',
      path: Routes.COMPANY_PLAN,
    },
    {
      title: ROUTES_CONFIG[Routes.COMPANY_DOCUMENTS].title ?? 'Company Documents',
      path: Routes.COMPANY_DOCUMENTS,
    },
    {
      title: ROUTES_CONFIG[Routes.CUSTOM_FORMS].title ?? 'Forms / Checklist',
      path: Routes.CUSTOM_FORMS,
    },
  ]

  return useSubNav(vesselId, subNavRoutes, activeRoute)
}
