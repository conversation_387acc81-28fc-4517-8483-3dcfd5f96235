name: Build Preview on Push to Main

on:
  push:
    branches:
      - preview

permissions:
  contents: read

jobs:
  build-ios-preview:
    runs-on: ubuntu-latest
    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: yarn

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: yarn install

      - name: Build iOS Preview
        id: build
        run: |
          echo "Starting iOS preview build..."

          # Run the build command and capture output
          if BUILD_OUTPUT=$(eas env:pull preview --non-interactive && eas build --profile preview --platform ios --clear-cache --non-interactive --no-wait 2>&1); then
            echo "Build command succeeded"
            echo "$BUILD_OUTPUT"
            
            # Extract build URL from output
            BUILD_URL=$(echo "$BUILD_OUTPUT" | grep -o 'https://expo\.dev/[^[:space:]]*' | head -1)
            
            if [ -n "$BUILD_URL" ]; then
              echo "Found build URL: $BUILD_URL"
              echo "build_url=$BUILD_URL" >> $GITHUB_OUTPUT
            else
              echo "Build succeeded but no URL found in output"
              echo "Full output:"
              echo "$BUILD_OUTPUT"
            fi
          else
            echo "Build command failed with exit code $?"
            echo "Error output:"
            echo "$BUILD_OUTPUT"
            exit 1
          fi

      - name: Post to Slack
        if: steps.build.outputs.build_url != ''
        uses: slackapi/slack-github-action@v1.26.0
        with:
          payload: |
            {
              "text": "A new preview build for iOS has been released. Access it here: ${{ steps.build.outputs.build_url }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  build-android-preview:
    runs-on: ubuntu-latest
    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: yarn

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: yarn install

      - name: Build Android Preview
        id: build
        run: |
          echo "Starting Android preview build..."

          # Run the build command and capture output
          if BUILD_OUTPUT=$(eas env:pull preview --non-interactive && eas build --profile preview --platform android --clear-cache --non-interactive --no-wait 2>&1); then
            echo "Build command succeeded"
            echo "$BUILD_OUTPUT"
            
            # Extract build URL from output
            BUILD_URL=$(echo "$BUILD_OUTPUT" | grep -o 'https://expo\.dev/[^[:space:]]*' | head -1)
            
            if [ -n "$BUILD_URL" ]; then
              echo "Found build URL: $BUILD_URL"
              echo "build_url=$BUILD_URL" >> $GITHUB_OUTPUT
            else
              echo "Build succeeded but no URL found in output"
              echo "Full output:"
              echo "$BUILD_OUTPUT"
            fi
          else
            echo "Build command failed with exit code $?"
            echo "Error output:"
            echo "$BUILD_OUTPUT"
            exit 1
          fi

      - name: Post to Slack
        if: steps.build.outputs.build_url != ''
        uses: slackapi/slack-github-action@v1.26.0
        with:
          payload: |
            {
              "text": "A new preview build for Android has been released. Access it here: ${{ steps.build.outputs.build_url }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}